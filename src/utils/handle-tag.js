import _ from 'lodash';
import moment from 'moment';
/**
 * 现有标签颜色汇总
 */

const TagsColor = {
  brandBlue: {
    color: '#128BED',
    bgColor: '#E2F1FC',
    hover: '#CAE6FC',
    class: 'text-brand-blue',
  },
  red: {
    color: '#F04040',
    bgColor: '#FFECEC',
    hover: '#FFCCCC',
    class: 'text-red',
  },
  green: {
    color: '#00AD65',
    bgColor: '#E3F6EE',
    hover: '#C4F5E0',
    class: 'text-green',
  },
  orange: {
    color: '#FF8900',
    bgColor: '#FFEEDB',
    hover: '#FFE6CC',
    class: 'text-orange',
  },
  gray: {
    color: '#666666',
    bgColor: '#F3F3F3',
    hover: '#E6E6E6',
    class: 'text-gray',
  },
  blueViolet: {
    color: '#845FFF',
    bgColor: '#F0EBFF',
    hover: '#DACCFF',
    class: 'text-blue-violet',
  },
  cobaltBlue: {
    color: '#6171FF',
    bgColor: '#EDEEFF',
    hover: '#CCCFFF',
    class: 'text-cobalt-blue',
  },
  techBlue: {
    color: '#216FFF',
    bgColor: '#E9F1FF',
    hover: '#CCDFFF',
    class: 'text-tech-blue',
  },
  mingQing: {
    color: '#00A3CC',
    bgColor: '#DFF3F8',
    hover: '#C6EDF7',
    class: 'text-ming-qing',
  },
  orangeRed: {
    color: '#FF722D',
    bgColor: '#FFEEE5',
    hover: '#FFDECC',
    class: 'text-orange-red',
  },
  vipGold: {
    color: '#BB833D',
    bgColor: '#F5EDE3',
    hover: '#F5E0C4',
    class: 'text-vip-gold',
  },
};

/**
 *  处理公司登记状态标签颜色的统一方法
 */
const primaryTagList = ['公开', '实质审查'];
const dangerTagList = [
  '无效',
  '失效',
  '清算',
  '撤销',
  '责令关闭',
  '除名',
  '吊销',
  '已撤销',
  '终止破产',
  '涂销破产',
  '清理完结',
  '清理',
  '破产清算完结',
  '破产程序终结',
  '破产',
  '废止',
  '废止清算完结',
  '废止许可完结',
  '废止许可',
  '废止认许',
  '废止认许完结',
  '废止登记完结',
  '废止登记',
  '废止',
  '不执行',
  '责令改正',
  '撤销完结',
  '撤销无需清算',
  '撤销许可',
  '撤销',
  '撤销认许',
  '撤销认许完结',
  '撤回认许',
  '撤回认许完结',
  '无效',
  '終止破產',
  '涂銷破產',
  '清理完結',
  '破產清算完結',
  '破產程序終結',
  '破產',
  '廢止清算完結',
  '廢止許可完結',
  '廢止許可',
  '廢止認許完結',
  '廢止登記完結',
  '廢止登記',
  '廢止',
  '撤銷完結',
  '撤銷無需清算',
  '撤銷許可',
  '撤銷',
  '撤銷認許',
  '撤銷認許完結',
  '撤回認許',
  '撤回認許完結',
  'ABANDONED',
  'CANCELED',
  'CANCELLED',
  'DELINQUENT',
  'DISSOLVED',
  'EXPIRED',
  'FORFEITED',
  'INACTIVE',
  'REMOVED',
  'SUSPENDED',
  'TERMINATED',
  'WITHDRAWN',
  'REVOKED',
  'LIQUIDATION',
  'STRIKE OFF',
  'STRIKING OFF',
  'DEFUNCT',
  'NOT AVAILabel',
  'DORMANT',
  'CAPTURED',
  'DEREGISTRATION',
  'DUPLICATE',
  'DEREGISTERED',
  'NO STATUS',
  'ARCHIVE',
  '全部无效',
  '权利终止',
  '期限届满',
  '未缴年费',
  '主动放弃',
  '避重放弃',
  '未缴年费专利权终止',
  '期限届满专利权终止',
  '撤銷',
  '廢止',
  '经营异常',
  '税收违法',
  '严重违法',
  '失信被执行人',
  '洗钱罪',
  '吊销，已注销',
  '吊销,已注销',
  '不合格',
  '商标无效',
  '视为放弃',
  '公布视为撤回',
  '公布撤回',
  '公布驳回',
  '企业已撤销',
  '吊销企业',
  '吊销，未注销',
  '已吊销',
];
const successTagList = [
  '迁出',
  '迁入',
  '有效',
  '在业',
  '存续',
  '筹建',
  '存续（在营、开业、在册）',
  '开业',
  '在营（开业）',
  '在营（开业）企业',
  '在册',
  '在营',
  '新申请用户',
  '已成立事先报批',
  '在营企业',
  '成立事先报批中',
  '成立中',
  '名称核准发起中',
  '名称核准通过',
  '已成立',
  '成立',
  '正常',
  '仍注册',
  '接管',
  '核准设立',
  '核准认许',
  '核准许可登记',
  '核准许可',
  '核准报备',
  '核准许可报备',
  '核准登记',
  '有效',
  '核准設立',
  '核准認許',
  '核准許可登記',
  '核准許可',
  '核准報備',
  '核准許可報備',
  '核准登記',
  '核準設立',
  '核準認許',
  '核準許可登記',
  '核準許可',
  '核準報備',
  '核準許可報備',
  '核準登記',
  'ACTIVE',
  'CONVERTED',
  'INCORPORATED',
  'MERGED',
  'OTHERS',
  'PERPETUAL',
  'REDEEMED',
  'UNKNOWN',
  'AMALGAMATED',
  'IN BUSINESS',
  'Live',
  'LIVE',
  'RESERVED',
  'CONVERSION',
  'RE-INSTATEMENT',
  '合格',
  '已注册',
  '权利恢复',
  '部分无效',
  '授权',
  '有效期续展',
];
const warningTagList = [
  '注销',
  '停业',
  '歇业',
  '已告解散',
  '已终止注册',
  '已終止註冊',
  '停業',
  '名称核准不通过',
  '注销中',
  '已注销',
  '已终止营业地点',
  '不再是独立的实体',
  '休止活动',
  '重整',
  '解散',
  '解散清算完结',
  '设立但已解散',
  '合并解散',
  '分割解散',
  '撤销设立',
  '撤销登记完结',
  '撤销登记',
  '撤回登记',
  '撤回登记完结',
  '解散清算完結',
  '設立但已解散',
  '合併解散',
  '撤銷設立',
  '撤銷登記完結',
  '撤銷登記',
  '撤回登記完結',
  '撤回登記',
  '撤銷設立',
  '撤銷登記完结',
  '撤銷登記',
  '撤回登記完结',
  '撤回登记',
  '設立但已解散',
  '合並解散',
  '解散清算完結',
  '存在异议',
  '暂停',
];
const riskHigherTagList = ['高风险', '高风险信息'];
const riskHighTagList = ['较高风险', '较高风险信息', '风险', '风险信息', '红色通道'];
const riskMidTagList = ['中风险', '警示', '警示信息', '中风险信息', '黄色通道'];
const riskLowTagList = ['较低风险', '较低风险信息', '提示', '提示信息'];
const riskLowerTagList = ['低风险', '低风险信息', '绿色通道'];
const riskNoTagList = ['中立', '中立信息', '无风险', '无风险信息', '待处理'];
const otherTags = ['其他', '暂无', '异常'];
const rptTagRunningList = ['进行中'];
const rptTagCompelteList = ['报告完成'];
const emptytag = ['', null, undefined, '-'];

const fundStatusGreen = ['正在运作', '正常清算'];
const fundStatusOrange = ['未披露', '延期清算', '提前清算'];
const fundStatusRed = ['投顾协议已终止'];

const negativeTagList = ['消极', '消极信息'];
const positiveTagList = ['良好信息', '良好', '积极', '积极信息', '利好', '利好信息'];

export const handleCompanyStatusTag = (shortStatus) => {
  const colorMap = {
    emptyTagList: 'empty',
    primaryTagList: 'blue',
    dangerTagList: 'red',
    successTagList: 'green',
    warningTagList: 'orange',
    noTagList: 'grey',
    riskLowerTagList: 'lower-risk',
    riskLowTagList: 'low-risk',
    riskHigherTagList: 'higher-risk',
    riskHighTagList: 'high-risk',
    riskMidTagList: 'mid-risk',
  };

  if (_.includes(emptytag, shortStatus)) {
    return { color: colorMap.emptyTagList };
  } else if (_.includes(primaryTagList, shortStatus)) {
    return { color: colorMap.primaryTagList };
  } else if (_.includes(dangerTagList, shortStatus) || _.includes(fundStatusRed, shortStatus)) {
    return { color: colorMap.dangerTagList };
  } else if (
    _.includes(successTagList, shortStatus) ||
    _.includes(rptTagCompelteList, shortStatus) ||
    _.includes(fundStatusGreen, shortStatus)
  ) {
    return { color: colorMap.successTagList };
  } else if (
    _.includes(warningTagList, shortStatus) ||
    _.includes(rptTagRunningList, shortStatus) ||
    _.includes(fundStatusOrange, shortStatus)
  ) {
    return { color: colorMap.warningTagList };
  } else if (_.includes(riskHigherTagList, shortStatus)) {
    /// /////风险信息----高，较高，中，较低，低
    return {
      // color: colorMap.riskHTagList
      color: colorMap.riskHigherTagList,
    };
  } else if (_.includes(riskHighTagList, shortStatus)) {
    return {
      // color: colorMap.dangerTagList
      color: colorMap.riskHighTagList,
    };
  } else if (_.includes(riskMidTagList, shortStatus)) {
    return {
      // color: colorMap.riskMTagList
      color: colorMap.riskMidTagList,
    };
  } else if (_.includes(riskLowTagList, shortStatus)) {
    return {
      // color: colorMap.primaryTagList
      color: colorMap.riskLowTagList,
    };
  } else if (_.includes(riskLowerTagList, shortStatus)) {
    return {
      // color: colorMap.successTagList
      color: colorMap.riskLowerTagList,
    };
  } else if (_.includes(negativeTagList, shortStatus)) {
    return {
      color: colorMap.dangerTagList,
    };
  } else if (_.includes(positiveTagList, shortStatus)) {
    return {
      color: colorMap.successTagList,
    };
  } else if (_.includes(riskNoTagList, shortStatus)) {
    return { color: colorMap.noTagList };
  } else if (_.includes(otherTags, shortStatus)) {
    return { color: colorMap.noTagList };
  } else {
    return { color: colorMap.noTagList, notMatch: true };
  }
};

// 图谱用
const handleCompanyStatusStyle = (shortStatus) => {
  const colorMap = {
    primaryTagList: {
      bg: '#e5f2fd',
      color: '#128bed',
    },
    dangerTagList: {
      color: '#F04040',
      bg: '#FFECEC',
    },
    successTagList: {
      bg: '#E3F6EE',
      color: '#00AD65',
    },
    warningTagList: {
      bg: '#FFEEE5',
      color: '#FF722D',
    },
    noTagList: {
      bg: '#f6f6f6',
      color: '#666',
    },
  };
  if (_.includes(primaryTagList, shortStatus)) {
    return colorMap.primaryTagList;
  } else if (_.includes(dangerTagList, shortStatus)) {
    return colorMap.dangerTagList;
  } else if (_.includes(successTagList, shortStatus)) {
    return colorMap.successTagList;
  } else if (_.includes(warningTagList, shortStatus)) {
    return colorMap.warningTagList;
  } else {
    return colorMap.noTagList;
  }
};

/**
 *
 * 处理股东信息标签颜色
 */
const handlePartnersTag = (name) => {
  const vipGold = ['大股东', '实际控制人', '最终受益人', '执行事务合伙人', '主要负责人'];

  if (_.includes(vipGold, name)) {
    return TagsColor.vipGold;
  }
};

/**
 *
 * 公司相关信息标签颜色
 */
const handleCompanyInfoTags = (tag, isFranceFlag = false) => {
  const orangeRed = [1, 2, 6, 7, 11, 12, 13, 14, 18, 26, 27, 29, 30, 31, 32, 33, 34, 35, 301, 401, 501, 502, 601, 602, 171, 17];
  const cobaltBlue = [15, 16, 111, 203, 506, 510, 507, 908, 622, 508];
  const techBlue = [88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 102, 103, 104, 105, 106, 107, 108, 505, 508, 913, 911, 912, 404, 405, 916, 900];
  const gray = [8, 9, 10, 25, 28, 99, 503, 603, 700];
  const blueViolet = [207, 208, 209, 402, 114, 910, 112, 115];
  const red = [302, 605, 606, 604, 907, 608, 607, 706, 9998, 9997, 704, 705, 707, 708, 710, 711, 712];
  const brandBlue = [620, 905, 109, 110, 200, 201, 202, 204, 205, 206];
  const mingQing = [3];
  const orange = [623];
  const colorObj = {
    orangeRed,
    cobaltBlue,
    techBlue,
    gray,
    blueViolet,
    red,
    brandBlue,
    mingQing,
    orange,
  };
  let color;
  let bgColor;
  let name;
  let tagClass;
  let className;
  for (const key in colorObj) {
    if (Object.hasOwnProperty.call(colorObj, key)) {
      const item = colorObj[key];
      if (item.includes(tag.type)) {
        color = TagsColor[key].color;
        bgColor = TagsColor[key].bgColor;
        tagClass = TagsColor[key].class;
        className = TagsColor[key].class;
      }
    }
  }

  name = getStockName(tag) || tag.name;

  // 标签颜色补丁处理
  if (tag.type === 3) {
    if (tag.name.includes('已退市')) {
      color = TagsColor.gray.color;
      bgColor = TagsColor.gray.bgColor;
      tagClass = TagsColor.gray.class;
      className = TagsColor.gray.class;
    }
  }

  // 金融标签置灰逻辑补充
  if (tag?.dataExtend2 && typeof tag?.dataExtend2 === 'string') {
    const extraData = JSON.parse(tag?.dataExtend2 || '{}');
    const NG = extraData?.NG || ''; // 上次更新时间
    if (NG === '1') {
      color = TagsColor.gray.color;
      bgColor = TagsColor.gray.bgColor;
      tagClass = TagsColor.gray.class;
      className = TagsColor.gray.class;
    }
  }

  if (isFranceFlag) {
    // 港美股 VIE 特殊处理 科创板 中概股
    if ([1, 2, 6, 7, 9, 30, 31, 401].includes(tag.type)) {
      const extraData = JSON.parse(tag?.dataExtend2 || '{}');
      name = extraData?.WL;
      if (extraData?.LS === '3') {
        color = TagsColor.gray.color;
        bgColor = TagsColor.gray.bgColor;
        tagClass = TagsColor.gray.class;
        className = TagsColor.gray.class;
      }
    }
  }

  return { color, bgColor, name, className, tagClass };
};

/**
 *
 * 公司相关信息标签颜色
 */
export const handleCompanyInfoTags4New = (tag) => {
  const brandBlue = [
    1, 2, 3, 6, 7, 11, 12, 14, 15, 16, 17, 18, 21, 26, 27, 29, 30, 31, 32, 33, 34, 35, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 102, 103,
    104, 105, 106, 107, 108, 109, 110, 111, 112, 114, 115, 118, 121, 171, 200, 203, 207, 208, 209, 301, 401, 402, 407, 502, 505, 506, 507,
    508, 510, 602, 622, 900, 908, 910, 912, 913, 916, 211,
  ];
  const red = [302, 604, 605, 606, 608, 623, 704, 705, 706, 707, 708, 710, 711, 712, 907];
  const gray = [8, 9, 10, 13, 25, 28, 99, 414, 503, 603, 620, 625, 700];
  const vipGold = [410, 411, 412, 413];
  const colorObjName = { brandBlue: 'blue', red: 'red', gray: 'grey', vipGold: 'vip-gold' };

  const colorObj = { brandBlue, red, gray, vipGold };
  const name = getStockName(tag) || tag.Name;
  let color;
  let bgColor;
  let tagClass;
  let className;
  let colorClassName = 'blue';
  const tType = tag.Type || tag.type;
  for (const key in colorObj) {
    if (Object.hasOwnProperty.call(colorObj, key)) {
      const item = colorObj[key];
      if (item.includes(tType)) {
        color = TagsColor[key].color;
        bgColor = TagsColor[key].bgColor;
        tagClass = TagsColor[key].class;
        className = TagsColor[key].class;
        colorClassName = colorObjName[key];
      }
    }
  }

  // 标签颜色补丁处理

  const tName = tag.Name || tag.name || '';
  if (tType === 3) {
    if (tName.includes('已退市')) {
      color = TagsColor.gray.color;
      bgColor = TagsColor.gray.bgColor;
      tagClass = TagsColor.gray.class;
      className = TagsColor.gray.class;
      colorClassName = colorObjName.gray;
    }
  }

  // 金融标签置灰逻辑补充
  const dataExtend2 = tag?.DataExtend2 || tag?.dataExtend2 || '{}';
  if (dataExtend2 && typeof dataExtend2 === 'string') {
    const extraData = JSON.parse(dataExtend2 || '{}');
    const NG = extraData?.NG || ''; // 上次更新时间
    if (NG === '1') {
      color = TagsColor.gray.color;
      bgColor = TagsColor.gray.bgColor;
      tagClass = TagsColor.gray.class;
      className = TagsColor.gray.class;
      colorClassName = colorObjName.gray;
    }
  }

  return { color, bgColor, name, className, tagClass, colorClassName };
};

/**
 * 公司相关信息 文案手动修改
 * 不适用企业主页头部
 */
const getStockName = (tag) => {
  let name = '';
  if (tag.type === 208) {
    name = '投资机构';
  } else if (tag.type === 8) {
    name = `${tag.name}(已摘牌)`;
  } else if (tag.type === 9) {
    name = `${tag.name}(已退市)`;
  } else if (tag.type === 12) {
    name = `${tag.name}(待上市)`;
  } else if (tag.type === 13) {
    name = `${tag.name}(暂停上市)`;
  } else if ([502, 503, 602, 603].includes(tag.type)) {
    name = tag.dataExtend;
  } else if (tag.type === 15) {
    name = tag.dataExtend || '';
  }
  return name;
};

const getStockName2 = (tag, stockStatus) => {
  let name = tag.name;
  if (tag.shortName) {
    name += ` | ${tag.shortName}`;
  }
  if (tag.dataExtend) {
    name += ` ${tag.dataExtend} `;
  }
  if (tag.name === '新三板' && stockStatus === '已退市') {
    stockStatus = '已摘牌';
  }
  if (stockStatus) {
    name += ` ${stockStatus}`;
  }
  return name;
};

// 获取所有企业登记状态
const getStatusTags = () => {
  return [...primaryTagList, ...dangerTagList, ...warningTagList, ...successTagList];
};

// 专利法律状态标签颜色处理
const handlePatentStatusColor = (status) => {
  let tagClass = TagsColor.brandBlue.class;
  if (['公布', '申请恢复', '实质审查', '申请'].includes(status)) {
    tagClass = TagsColor.brandBlue.class;
  } else if (['授权', '部分撤销', '部分无效', '权利恢复', '有效', '有效期续展'].includes(status)) {
    tagClass = TagsColor.green.class;
  } else if (
    [
      '驳回上诉',
      '公布驳回',
      '公布撤回',
      '公布视为撤回',
      '全部无效',
      '撤销',
      '权利终止',
      '期限届满',
      '未缴年费',
      '主动放弃',
      '视为放弃',
      '避重放弃',
      '无效',
      '未缴年费专利权终止',
      '期限届满专利权终止',
    ].includes(status)
  ) {
    tagClass = TagsColor.red.class;
  }
  return tagClass;
};

const handlePatentStatusColorByStatus = (status) => {
  let tagClass = TagsColor.brandBlue.class;
  const blueList = ['ZT001', 'ZT001001', 'ZT001002', 'ZT001003', 'ZT004'];
  const greenList = ['ZT002', 'ZT002001', 'ZT002002', 'ZT002003', 'ZT002004', 'ZT002005', 'ZT002006'];
  const redList = [
    'ZT003',
    'ZT003001',
    'ZT003002',
    'ZT003003',
    'ZT003004',
    'ZT003005',
    'ZT003006',
    'ZT003007',
    'ZT003008',
    'ZT003009',
    'ZT003010',
    'ZT003011',
  ];
  if (blueList.includes(status)) {
    tagClass = `${TagsColor.brandBlue.class} blue`;
  } else if (greenList.includes(status)) {
    tagClass = `${TagsColor.green.class} green`;
  } else if (redList.includes(status)) {
    tagClass = `${TagsColor.red.class} red`;
  }
  return tagClass;
};

// 制造业企业标签颜色及相关提示
const handleManufacturingTag = (name) => {
  let tips = '';
  /* eslint-disable */
  switch (name) {
    case '制造业单项冠军企业':
    case '国家级冠军企业':
    case '省级冠军企业':
    case '冠军企业':
      tips = '指长期专注于制造业某些特定细分产品市场,生产技术或工艺国际领先,单项产品市场占有率位居全球前列的企业。';
      break;
    case '制造业单项冠军产品企业':
      tips = '指拥有生产技术、工艺国内领先，产品质量精良，相关关键性能指标处于国内同类产品的领先水平产品的企业。';
      break;
    case '制造业头雁企业':
      tips = '指一批创新水平高、质量效益优、成长性较好、带动力强的头雁企业。';
      break;
    case '制造业大优强企业':
      tips = '指一批处于产业链关键环节、规模大、实力强、带动性大的大企业（大集团）。';
      break;
    case '高端装备制造业骨干企业':
      tips = '指创新能力强、研发水平高、示范引领作用强、市场占有率高的优势企业。';
      break;
    case '产融合作制造业重点企业':
      tips = '指纳入产融合作制造业“白名单”。';
      break;
    case '环保装备制造业规范条件企业':
      tips = '指大气治理、污水治理、环境监测仪器、固废处理等环保装备制造行业规范条件的企业。';
      break;
    case '制造业云上企业':
      tips =
        '指以数字基础设施云化部署为前提，全面云化构建核心业务系统，以数据资源为关键要素，打造企业内外部、产业链上下游的云上数字化协同生态，不断提高资源要素配置效率和生产管理智能化水平，从而实现竞争力、发展质量和效益显著提升的制造业企业。';
      break;
    case '基于互联网的制造业双创平台':
      tips = '指具有“双创”功能的大型制造企业及为制造业提供“双创”服务的信息技术企业、互联网企业、电信运营企业、高等院校和科研院所等。';
      break;
    case '先进制造业和现代服务业融合发展企业':
      tips = '指推动现代服务业同先进制造业深度融合，加快推进服务业数字化的企业。';
      break;
    case '智能制造标杆企业':
      tips = '指在设计、生产、物流、销售、服务等业务环节所达到的智能制造能力水平，重点关注数据驱动业务优化在企业发展过程发挥作用的企业。';
      break;
    case '服务型制造示范企业':
      tips =
        '服务型制造示范企业主要面向定制化服务、供应链管理、检验检测认证服务、全生命周期管理、总集成总承包、节能环保服务、生产性金融服务及其他服务型制造创新模式开展遴选。在本行业或相关领域内，其生产技术与工艺、服务能力与水平具有一定优势，服务收入占企业营业收入比重达规定占比水平线以上。';
      break;
    case '重点实验室':
      tips =
        '是指国家科技创新体系中重要的实验室，是国家组织高水平基础研究和应用基础研究、聚集和培养优秀科技人才、开展高水平学术交流、科研装备先进的重要基地。';
      break;
    case '创新中心':
      tips =
        '是指主要开展关键技术研究、工程化研发、科技成果转移转化及应用示范，提高关键核心技术创新能力，加快科研成果向现实生产力转化的单位。';
      break;
    case '绿色制造':
      tips =
        '绿色制造是一个产品的全生命周期概念，综合考虑资源效率和环境影响相协调的现代化制造模式。按照《工业和信息化部办公厅关于开展绿色制造体系建设的通知》，绿色制造名单包含绿色工厂、绿色设计产品、绿色工业园区、绿色供应链管理企业名单。';
      break;
    default:
      break;
  }
  return tips;
};

// 企业主页科技型企业的类型
const tecTagMap = {
  88: '企业技术中心',
  89: '科技企业孵化器',
  90: '技术创新示范企业',
  91: '科技型中小企业',
  92: '众创空间',
  93: '隐形冠军企业',
  94: '技术先进型服务企业',
  95: '民营科技企业',
  97: '专精特新小巨人企业',
  102: '独角兽企业',
  103: '瞪羚企业',
  105: '科技小巨人企业',
  106: '专精特新中小企业',
  107: '雏鹰企业',
  108: '高新技术企业',
  911: '工程技术研究中心',
  916: '创新型中小企业',
};
// 企业主页科技型企业的定义
const tecTipMap = {
  88: '是指企业根据市场竞争需要设立的技术研发与创新机构，负责制定企业技术创新规划、开展产业技术研发、创造运用知识产权、建立技术标准体系、凝聚培养创新人才、构建协同创新网络、推进技术创新全过程实施。',
  89: '是以促进科技成果转化，培育科技企业和企业家精神为宗旨，提供物理空间、共享设施和专业化服务的科技创业服务机构，是国家创新体系的重要组成部分、创新创业人才的培养基地、大众创新创业的支撑平台。',
  90: '是指在工业主要产业中技术创新能力较强、创新业绩显著、具有重要示范和导向作用的企业。',
  91: '是指依托一定数量的科技人员从事科学技术研究开发活动，取得自主知识产权并将其转化为高新技术产品或服务，从而实现可持续发展的中小企业。',
  92: '是指为满足大众创新创业需求，提供工作空间、网络空间、社交空间和资源共享空间，积极利用众筹、众扶、众包等新手段，以社会化、专业化、市场化、网络化为服务特色，实现低成本、便利化、全要素、开放式运营的创新创业平台。',
  93: '指那些不为公众所熟知，却在某个细分行业或市场占据领先地位，拥有核心竞争力和明确战略，其产品、服务难以被超越和模仿的中小型企业。',
  94: '国家为了扶持高端技术性服务业的发展，对从事技术外包、业务外包和知识外包服务的企业进行税收等多项政策支持的企业类型。',
  95: '是指以科技人员为主体，以技术密集型产品研制、生产、销售以及技术开发、技术转让、技术咨询和技术服务为主要业务，按照自筹资金、自愿组合、自主经营、自负盈亏原则依法创办和经营的经济实体。',
  97: '是专精特新企业中的佼佼者，是专注于细分市场、创新能力强、市场占有率高、掌握关键核心技术、质量效益优的排头兵企业。',
  102: '一般指10亿美元以上估值，并且创办时间相对较短（一般为十年内）还未上市的企业。',
  103: '一般指创业后跨过死亡谷以科技创新或商业模式创新为支撑进入高成长期的中小企业。',
  105: '一般是指企业在研究、开发、生产、销售和管理过程中，通过技术创新、管理创新、服务创新或模式创新取得核心竞争力，提供高新技术产品或服务，具有较高成长性或发展潜力巨大的科技创新中小企业。',
  106: '一般指具有“专业化、精细化、特色化、新颖化”特征的工业中小企业。',
  107: '一般指技术水平领先、竞争能力强、成长性好的科技型初创企业。',
  108: '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础开展经营活动，在中国境内（不包括港、澳、台地区）注册的居民企业。',
  911: '是指综合实力和创新能力较强的企业、高校或科研院所，具有较完备的工程技术综合配套试验条件，有一支高素质的研究开发、工程设计和试验的专业科技队伍，有稳定的经费来源，并能提供多种综合性技术服务的工程技术研究开发机构。',
  916: '指具有较高专业化水平、较强创新能力和发展潜力的企业，是优质中小企业的基础力量。',
};
const tecValueMap = {
  T_TC: 88,
  T_CI: 89,
  T_TD: 90,
  T_TSMES: 91,
  T_MS: 92,
  T_IC: 93,
  T_ATS: 94,
  T_PT: 95,
  T_SSTE: 97,
  UE: 102,
  GE: 103,
  STE: 105,
  SSE: 106,
  YEE: 107,
  HT001: 108,
  T_NDSC: 911,
  T_INNMS: 916,
};

const gxTags = [
  { desc: '高新技术企业', value: 'HT001' },
  { desc: '独角兽企业', value: 'UE' },
  { desc: '瞪羚企业', value: 'GE' },
  { desc: '创新型企业', value: 'ITE' },
  { desc: '科技小巨人企业', value: 'STE' },
  { desc: '专精特新中小企业', value: 'SSE' },
  { desc: '雏鹰企业', value: 'YEE' },
  { desc: '企业技术中心', value: 'T_TC' },
  { desc: '科技企业孵化器', value: 'T_CI' },
  { desc: '技术创新示范企业', value: 'T_TD' },
  { desc: '科技型中小企业', value: 'T_TSMES' },
  { desc: '众创空间', value: 'T_MS' },
  { desc: '隐形冠军企业', value: 'T_IC' },
  { desc: '技术先进型服务企业', value: 'T_ATS' },
  { desc: '民营科技企业', value: 'T_PT' },
  { desc: '牛羚企业', value: 'T_AC' },
  { desc: '专精特新小巨人企业', value: 'T_SSTE' },
  { desc: '创新型中小企业', value: 'T_INNMS' },
  { desc: '工程技术研究中心', value: 'T_NDSC' },
];

// 科技型标签type
const tecTagCode = [88, 89, 90, 91, 92, 93, 94, 95, 97, 102, 103, 105, 106, 107, 108, 911, 916];

// 各种状态的颜色
const statusColor = (status) => {
  let color = '';
  if (status.indexOf('存续') !== -1) {
    color = '#61DDAA';
  } else if (status.indexOf('在业') !== -1) {
    color = '#5B8FF9';
  } else if (status.indexOf('注销') !== -1) {
    color = '#F6BD16';
  } else if (status.indexOf('撤销') !== -1) {
    color = '#F6903D';
  } else if (status.indexOf('吊销') !== -1) {
    color = '#F04040';
  } else if (status.indexOf('迁入') !== -1) {
    color = '#65789B';
  } else if (status.indexOf('迁出') !== -1) {
    color = '#898E98';
  } else if (status.indexOf('清算') !== -1) {
    color = '#D67B83';
  } else if (status.indexOf('停业') !== -1) {
    color = '#B96C30';
  } else if (status.indexOf('歇业') !== -1) {
    color = '#CBAF0F';
  } else if (status.indexOf('责令关闭') !== -1) {
    color = '#9C0E0E';
  } else {
    color = '#808080';
  }

  return color;
};

const formatFinancialHoverText = (code) => {
  if (code === '0') return '';
  switch (code) {
    case '1':
      return '注册制，拟于沪主板上市';
    case '2':
      return '注册制，拟于深主板上市';
    case '3':
      return '注册制，拟于创业板上市';
    case '4':
      return '注册制，拟于科创板上市';
    case '107':
      return '注册制，拟于京主板上市';
    case '101':
      return '核准制，拟于沪主板上市';
    case '102':
      return '核准制，拟于深主板上市';
    case '31':
      return '当前为基础层，基础层包含的个股都是准入标准最低的个股，这里的公司风险是最大的，一方面它都是创新层和精选层中淘汰下来的个股，另一方面它是一个极其庞大的股票群体，近乎代表整个新三板市场';
    case '32':
      return '当前为创新层，创新层企业资质、财务状况较好，会被要求公司信息披露更加公开公正，支持连续竞价的交易方式使交易更灵活';
    case '33':
      return '当前为精选层，精选层是从创新层中挑出的优质企业，它的挑选标准是从利润指标、收入增长指标、市值指标三者中选择，满足其一即可入选';
    case '34':
      return '两网代表的是staq、net两个系统，退市是指股票退出交易市场。股票退市后会进入该市场交易，投资者可以在证券公司进行确权，并开通三板交易权限就能在三板市场挂牌转让';
  }
};

const stockNameMap = [
  { id: 1, stockStatus: '' },
  { id: 2, stockStatus: '' },
  { id: 401, stockStatus: '' },
  { id: 301, stockStatus: '' },
  { id: 7, stockStatus: '' },
  { id: 17, stockStatus: '' },
  { id: 11, stockStatus: '' },
  { id: 8, stockStatus: '已退市' },
  { id: 9, stockStatus: '已退市' },
  { id: 12, stockStatus: '待上市' },
  { id: 13, stockStatus: '暂停上市' },
];

const getStockName4Type = (tag) => {
  let find = stockNameMap.find((e) => e.id === tag.type);
  if (find) {
    return getStockName2(tag, find.stockStatus);
  } else {
    return '';
  }
};

const map = [
  {
    id: 622,
    text: '是指国务院和地方人民政府分别代表国家履行出资人职责的国有独资企业、国有独资公司以及国有资本控股公司，包括中央和地方国有资产监督管理机构和其他部门所监管的企业本级及其逐级投资形成的企业。',
  },
  { id: 109, text: '教育者有计划、有组织地对受教育者进行系统的教育活动的组织机构。' },
  {
    id: 110,
    text: '按照法律法规和行业规范，为病员开展必要的医学检查、治疗措施、护理技术、接诊服务、康复设备、救治运输等服务，以救死扶伤为主要目的医疗机构。',
  },
  { id: 111, text: '由政府利用国有资产设立的，从事教育、科技、文化、卫生等活动的社会服务组织。' },
  { id: 114, text: '中华人民共和国律师执行职务进行业务活动的工作机构。' },
  { id: 203, text: '利用自然人、法人或者其他组织捐赠的财产，以从事公益事业为目的，按照本条例的规定成立的非营利性法人。' },
  { id: 404, text: '建筑企业是指依法自主经营、自负盈亏、独立核算，从事建筑商品生产和经营，具有法人资格的经济实体。' },
  { id: 501, text: '独立于现有主板市场的新设板块，并在该板块内进行注册制试点。' },
  { id: 502, text: '独立于现有主板市场的新设板块，并在该板块内进行注册制试点。' },
  { id: 503, text: '独立于现有主板市场的新设板块，并在该板块内进行注册制试点。' },
  { id: 510, text: '药品临床试验机构是经国家食品药品监督管理局资格认定的承担药物临床试验的医疗机构。' },

  {
    id: 88,
    text: '根据创新驱动发展要求和经济结构调整需要，对创新能力强、创新机制好、引领示范作用大、符合条件的企业技术中心予以认定，并给予政策支持，鼓励引导行业骨干企业带动产业技术进步和创新能力提高。',
  },
  {
    id: 89,
    text: '培育和扶植高新技术中小企业的服务机构',
  },
  {
    id: 90,
    text: '工业主要产业中技术创新能力较强、创新业绩显著、具有重要示范和导向作用的企业。',
  },
  {
    id: 91,
    text: '以科技人员为主体，由科技人员领办和创办，主要从事高新技术产品的科学研究、研制、生产、销售，以科技成果商品化以及技术开发、技术服务、技术咨询和高新产品为主要内容，以市场为导向，实行“自筹资金、自愿组合、自主经营、自负盈亏、自我发展、自我约束”的知识密集型经济实体。',
  },
  {
    id: 92,
    text: '为满足大众创新创业需求，提供工作空间、网络空间、社交空间和资源共享空间，积极利用众筹、众扶、众包等新手段，以社会化、专业化、市场化、网络化为服务特色，实现低成本、便利化、全要素、开放式运营的创新创业平台。',
  },
  {
    id: 93,
    text: '不为公众所熟知，却在某个细分行业或市场占据领先地位，拥有核心竞争力和明确战略，其产品、服务难以被超越和模仿的中小型企业。',
  },
  {
    id: 94,
    text: '国家为了扶持高端技术性服务业的发展，对从事技术外包、业务外包和知识外包服务的企业进行税收等多项政策支持的企业类型。',
  },
  {
    id: 95,
    text: '以科技人员为主体创办的，实行自筹资金、自愿组合、自主经营、自负盈亏、自我约束、自我发展的经营机制，主要从事科技成果转化及技术开发、技术转让、技术咨询、技术服务或实行高新技术及其产品的研究、开发、生产、销售的智力、技术密集型的经济实体。',
  },
  {
    id: 96,
    text: '具有自主知识产权，连续两年销售收入年均增长30%以上，且最近一个会计年度达500万元人民币以上的企业。',
  },
  {
    id: 97,
    text: '培育一批主营业务突出、竞争力强、成长性好的专精特新‘小巨人’企业。',
  },
  {
    id: 102,
    text: '一般指10亿美元以上估值，并且创办时间相对较短（一般为十年内）还未上市的企业。',
  },
  {
    id: 103,
    text: '高成长中小企业，一般指创业后跨过死亡谷以科技创新或商业模式创新为支撑进入高成长期的中小企业。',
  },
  {
    id: 104,
    text: '主要是指拥有自主知识产权和知名品牌，具有较强国际竞争力，依靠技术创新获取市场竞争优势和持续发展的企业。',
  },
  {
    id: 105,
    text: '一般指在研究、开发、生产、销售和管理过程中，通过技术创新、管理创新、服务创新或模式创新取得核心竞争力，提供高新技术产品或服务，具有较高成长性或发展潜力巨大的科技创新中小企业。',
  },
  {
    id: 106,
    text: '一般指具有“专业化、精细化、特色化、新颖化”特征的工业中小企业。',
  },
  {
    id: 107,
    text: '一般指技术水平领先、竞争能力强、成长性好的科技型初创企业。',
  },
  {
    id: 108,
    text: '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础开展经营活动，在中国境内（不包括港、澳、台地区）注册一年以上的居民企业。',
  },
  {
    id: 112,
    text: '国籍（或地区）/注册地：香港',
  },
  {
    id: 115,
    text: '国籍（或地区）/注册地：台湾',
  },
  {
    id: 200,
    text: '人们为了有效地达到特定目标按照一定的宗旨、制度、系统建立起来的共同活动集体。',
  },
  {
    id: 505,
    text: '小微企业基于国家市场监督管理部门公布的小微企业库，依据企业营业收入，资产总额，从业人员数量等标准进行划分。企业划分由政府综合统计部门根据统计年报每年确定一次。',
  },
  {
    id: 508,
    text: 'GSP是《药品经营质量管理规范》的英文缩写，是药品经营企业统一的质量管理准则。药品经营企业应在药品监督管理部门规定的时间内达到GSP要求，并通过认证取得认证证书。',
  },
  {
    id: 507,
    text: 'GMP是一套指导食物、药品、医疗产品生产和质量管理的标准，药品GMP认证是国家依法对药品生产企业（车间）和药品品种实施GMP监督检查并取得认可的一种制度。',
  },
  {
    id: 510,
    text: '药品临床试验机构是经国家食品药品监督管理局资格认定的承担药物临床试验的医疗机构。',
  },
  {
    id: 908,
    text: '机关单位指从事国家管理和行使国家权力的机关。',
  },
];

const hoverHandle = (type) => {
  let find = map.find((e) => e.id === type);
  if (find) {
    return find.text;
  } else {
    return '';
  }
};

const handleTagCards = (tag, tips) => {
  const levelMap = {
    0: '无法判断',
    1: '国家级',
    2: '省级',
    3: '市级',
    4: '区/县级',
    5: '园区级',
    9: '其他',
  };
  const extendObj = JSON.parse(tag.dataExtend || '{}') || {};
  let releaseDate = extendObj.pd || '';
  let endDate = extendObj.ed || '';
  const source = extendObj.s || '';
  let hoverText = '';
  if (releaseDate) {
    releaseDate = moment(releaseDate * 1000).format('YYYY-MM-DD');
    hoverText += `<span style="font-weight: bold">发布日期</span>：${releaseDate}<br />`;
  }
  if (endDate) {
    endDate = moment(endDate * 1000).format('YYYY-MM-DD');
    if (moment(endDate).diff(moment().format('YYYY-MM-DD'), 'days') < 0) {
      tag.isExpired = true;
    }
    if (moment(endDate).diff(moment().format('YYYY-MM-DD'), 'months') <= -6) {
      tag.isExpiredSixMonth = true;
    }
  }
  hoverText += levelMap[extendObj.l] ? `<span style="font-weight: bold">级别</span>：${levelMap[extendObj.l]}<br />` : '';
  hoverText += source ? `<span style="font-weight: bold">来源</span>：${source}<br />` : '';
  hoverText += `<span style="font-weight: bold">定义</span>：${tips || '-'}`;
  // hoverText += `<div class='check-more'><a href=${link}>查看更多 <i class="iconfont icon-icon_jiantou8"></i></a></div>`
  return '<span style="font-size: 14px;line-height: 22px">' + hoverText + '</span>';
};

const yiyuanMap = {
  三级特等:
    '简称“三特医院”，是依照中国现行《医院分级管理办法》等的规定而划分的医院等级之一。医院建设成绩显著，科室设置、人员配备、管理水平、技术水平、工作质量和技术设施等，按分等标准综合考核检查达900分及以上。三级特等医院从级别上是我国医院的最高级别。',
  三级甲等:
    '三级甲等医院是向所在地区以及周边辐射区域提供高水平医疗卫生服务和执行高等教育、科研任务的区域性以上医院。医院建设升级显著，科室设置、人员配备、管理水平、技术水平、工作质量和技术设施等，按分等标准综合考核检查达900分及以上。',
  三级乙等: '三级乙等医院是指建设成绩尚好，科室设置、人员配备、技术水平、工作质量、技术设施等，按分等标准综合考核检查达750-899分。',
  三级丙等:
    '三级丙等医院是依照中国现行《医院分级管理办法》等的规定而划分的医院等级之一。医院建设有一定成绩，基本标准考核合格，但与本标准要求尚有较大差距。按分等标准综合考核检查在749分及以下。三级丙等医院应有切实可行的改进措施。',
  二级甲等:
    '二级甲等医院属于二级医院的一种，在二级医院中等级最高，二级甲等是按照医院的功能、任务、设施条件、技术建设、医疗服务质量和科学管理的综合水平进行等级评定而确定的医院等级。',
  二级乙等:
    '二级乙等医院属于二级医院的一种，在二级医院中等级中等，主要指一般市、县医院及省辖市的区级医院，以及相当规模的工矿、企事业单位的职工医院。',
  二级丙等:
    '二级丙等医院主要指一般市、县医院及省辖市的区级医院，以及相当规模的工矿企事业单位的职工医院。二级医院是向多个社区提供综合医疗卫生服务和承担一定教学、科研任务的地区性医院。',
  一级甲等:
    '一级甲等医院属于一级医院的一种，在一级医院中等级最高，它是指直接向具有⼀定⼈⼝的社区提供医疗、预防、保健和康复服务的基层医疗卫⽣机构。',
  一级乙等:
    '一级乙等医院属于一级医院的一种，在一级医院中等级中等，它是指直接为一定人口的社区提供预防、治疗、保健、康复服务的基层医院、卫生院。',
  一级丙等: '一级丙等医院属于一级医院的等级之一，一级医院是直接为一定人口的社区提供预防、治疗、保健、康复服务的基层医院、卫生院。',
  一级医院:
    '是直接为社区提供医疗、预防、康复、保健综合服务的基层医院，是初级卫生保健机构。其主要功能是直接对人群提供一级预防，在社区管理多发病常见病现症病人并对疑难重症做好正确转诊，协助高层次医院搞好中间或院后服务，合理分流病人。',
  二级医院:
    '是跨几个社区提供医疗卫生服务的地区性医院，是地区性医疗预防的技术中心。其主要功能是参与指导对高危人群的监测，接受一级转诊，对一级医院进行业务技术指导，并能进行一定程度的教学和科研。',
  三级医院:
    '是跨地区、省、市以及向全国范围提供医疗卫生服务的医院，是具有全面医疗、教学、科研能力的医疗预防技术中心。主要提供专科（包括特殊专科）的医疗服务，解决危重疑难病症，接受二级转诊；完成培养各种高级医疗专业人才的教学和承担省以上科研项目的任务。',
  三级: '是跨地区、省、市以及向全国范围提供医疗卫生服务的医院，是具有全面医疗、教学、科研能力的医疗预防技术中心。主要提供专科（包括特殊专科）的医疗服务，解决危重疑难病症，接受二级转诊；完成培养各种高级医疗专业人才的教学和承担省以上科研项目的任务。',
};

const xuexiaoMap = {
  985: '985工程是指中国共产党和中华人民共和国国务院在世纪之交为建设具有世界先进水平的一流大学而做出的重大决策。',
  211: '211工程是指面向21世纪，重点建设100所左右的高等学校和一批重点学科的建设工程。是新中国成立以来由国家立项在高等教育领域进行的规模最大、层次最高的重点建设工作',
  双一流:
    '“双一流”是指世界一流大学和一流学科。建设世界一流大学和一流学科，是中共中央、国务院作出的重大战略决策，也是继“211工程”，“985工程”之后的又一国家战略。',
};

const handleTags = (tag, color, bgColor) => {
  const orangeRed = [1, 2, 6, 7, 11, 12, 13, 14, 18, 26, 27, 29, 30, 31, 32, 33, 34, 35, 301, 401, 501, 502, 601, 602, 171, 17];
  const cobaltBlue = [15, 16, 111, 203, 506, 510, 507, 908, 622, 508];
  const techBlue = [
    88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 102, 103, 104, 105, 106, 107, 108, 916, 505, 508, 913, 911, 404, 405, 1000001, 1000002, 1000003,
    1000004, 1000005, 1000006, 1000007, 1000008, 1000009, 1000010, 1000011,
  ];
  const gray = [8, 9, 10, 25, 28, 99, 503, 603, 700, 620, 625];
  const blueViolet = [207, 208, 209, 402, 114, 910, 112, 115];
  const red = [302, 901, 605, 902, 606, 904, 604, 907, 608, 607, 704, 705, 706, 907, 707];
  const brandBlue = [905, 109, 110, 200, 201, 202, 204, 205, 206];
  const mingQing = [3];
  const orange = [623];
  const green = [9992];

  const colorObj = {
    'orange-red': orangeRed,
    'cobalt-blue': cobaltBlue,
    'technology-blue': techBlue,
    grey: gray,
    'purple-blue': blueViolet,
    red: red,
    blue: brandBlue,
    green: green,
    'ming-qing': mingQing,
    orange: orange,
  };

  // 标签颜色补丁处理
  if (+tag.type === 3) {
    if (tag.name.includes('已退市')) {
      return { color: 'grey' };
    }
  }
  // 金融标签置灰逻辑补充
  if (tag?.dataExtend2 && typeof tag?.dataExtend2 === 'string') {
    const extraData = JSON.parse(tag?.dataExtend2 || '{}');
    const NG = extraData?.NG || ''; // 上次更新时间
    if (NG === '1') {
      return { color: 'grey' };
    }
  }
  // 港美股 VIE 特殊处理
  if ([30, 31].includes(+tag.type)) {
    const extraData = JSON.parse(tag.dataExtend2 || '{}');
    if (extraData.LS === '3') {
      return { color: 'grey' };
    }
  }

  for (const key in colorObj) {
    if (Object.hasOwnProperty.call(colorObj, key)) {
      const item = colorObj[key];
      if (item.includes(tag.type)) {
        return { color: key };
      }
    }
  }

  return { color, bgColor };
};

export default {
  handleCompanyStatusTag,
  handlePartnersTag,
  handleCompanyInfoTags,
  handleCompanyInfoTags4New,
  handleCompanyStatusStyle,
  handlePatentStatusColor,
  handlePatentStatusColorByStatus,
  getStatusTags,
  handleManufacturingTag,
  gxTags,
  tecTagCode,
  tecTipMap,
  tecTagMap,
  tecValueMap,
  statusColor,
  formatFinancialHoverText,
  getStockName2,
  getStockName4Type,
  hoverHandle,
  handleTagCards,
  yiyuanMap,
  xuexiaoMap,
  handleTags,
};
