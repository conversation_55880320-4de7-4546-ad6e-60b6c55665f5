import { GraphComponent, Insets, Size, SvgExport } from 'yfiles';
import fileSaver from 'file-saver';
import moment from 'moment';
import ImgWatermark from '@/assets/images/shuiying6.png';
import ImgLogo from '@/assets/images/tip-logo.png';
import _ from 'lodash';

export const exportImage = async (graphComponent, scale = 4, margins = Insets.from(5), exportRect) => {
  // const originalQuerySelector = document.querySelector;
  // document.querySelector = function () {
  //   if (arguments[0].indexOf('-webkit') !== -1) {
  //     return null;
  //   }
  //   return originalQuerySelector.apply(document, arguments);
  // };

  const exportComponent = new GraphComponent();
  exportComponent.graph = graphComponent.graph;
  exportComponent.updateContentRect();

  const targetRect = exportRect ?? exportComponent.contentRect;

  const exporter = new SvgExport({
    worldBounds: targetRect,
    scale,
    margins,
    encodeImagesBase64: false,
    rasterizeHtmlVisuals: true,
    inlineSvgImages: true,
    strictMode: false,
    cssStyleSheet: '',
  });
  // document.querySelector = originalQuerySelector;

  const svgElement = await exporter.exportSvgAsync(exportComponent);

  await Promise.all(
    Array.from(svgElement.querySelectorAll('image')).map((imageElement) => {
      const href = imageElement.getAttributeNS('http://www.w3.org/1999/xlink', 'href') || imageElement.getAttribute('href');
      if (!href) return Promise.resolve(null);
      return new Promise((resolve) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.src = href;
        img.onerror = function () {
          // TODO: 弄一个默认图片上去，假如加载失败的话
          // logo/default person/default
          imageElement.setAttribute('href', '');
          resolve(true);
        };
        img.onload = function () {
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.drawImage(img, 0, 0, img.width, img.height);
            imageElement.setAttribute('href', canvas.toDataURL());
          }
          resolve(true);
          canvas?.remove?.();
        };
        img.src = href;
      });
    })
  );

  return renderSvgToPng(svgElement, new Size(exporter.viewWidth, exporter.viewHeight), margins);
};

const renderSvgToPng = (svgElement, size, margins) => {
  const targetCanvas = document.createElement('canvas');
  const targetContext = targetCanvas.getContext('2d');

  const svgString = SvgExport.exportSvgString(svgElement);
  const svgUrl = SvgExport.encodeSvgDataUrl(svgString);

  return new Promise((resolve) => {
    const svgImage = new Image();
    svgImage.onload = async () => {
      /** 在svgImage loaded 回调中并不能保证内联的图片也加载完毕，这里通过 loadInlineImages
       * 方法去主动请求内联图片，加载成功后再画入canvas
       */

      await loadInlineImages();

      targetContext.clearRect(0, 0, targetCanvas.width, targetCanvas.height);

      targetCanvas.width = size.width + (margins.left + margins.right);
      targetCanvas.height = size.height + (margins.top + margins.bottom);
      // 这边将背景设置成白色
      targetContext.fillStyle = 'transparent';
      targetContext.fillRect(0, 0, targetCanvas.width, targetCanvas.height);

      // 画图
      targetContext.drawImage(svgImage, margins.left, margins.top);

      setTimeout(() => resolve(targetCanvas.toDataURL('image/png')), 50);
    };
    svgImage.src = svgUrl;
  });

  function loadInlineImages() {
    return Promise.all(
      Array.from(svgElement.querySelectorAll('image')).map((image) => {
        const href = image.getAttributeNS('http://www.w3.org/1999/xlink', 'href') || image.getAttribute('href');
        if (!href) return Promise.resolve(null);
        return new Promise((resolve, reject) => {
          const img = new Image();
          img.crossOrigin = 'anonymous';
          img.src = href;
          img.onerror = function () {
            return reject(new Error('Could not load image'));
          };
          img.onload = function () {
            resolve(true);
          };
        });
      })
    );
  }
};

const drawText = ({ context, text, font, x, y, fillStyle = '#666' }) => {
  context.font = `${font}px 微软雅黑`;
  context.fillStyle = fillStyle;
  context.fillText(text, x, y);
};

const loading = (image) => {
  return new Promise((resolve) => {
    image.onload = function () {
      resolve(image);
    };
  });
};

const getTextSize = (str, fontSize) => {
  const span = document.createElement('span');
  span.innerHTML = str;
  span.style.fontSize = `${fontSize}px`;
  span.style.visibility = 'hidden';
  span.style.display = 'inline-block';
  document.body.appendChild(span);
  const size = { w: span.clientWidth, h: span.clientHeight };
  document.body.removeChild(span);
  return size;
};

export const saveForGraph = ({
  imgData,
  companyName,
  title,
  noWatermark,
  isNoRelation = false,
  chartType = '',
  legendList = [],
  scale = 2,
  type = 'file',
  isShowALLFlag = true,
}) => {
  return new Promise((resolve, reject) => {
    /** 上下左右间距 */
    const padding = 30 * scale;
    /** 免责高度 */
    const footerHeight = 38 * scale;
    /** 上下总间距 */
    const paddingV = padding * 2 + footerHeight;
    /** 左右间距 */
    const paddingH = padding * 2;

    const loadingImageList = [];
    const img = new Image();
    img.src = imgData;
    img.crossOrigin = 'Anonymous';
    loadingImageList.push(loading(img));

    const shuiying = new Image();
    shuiying.src = ImgWatermark;
    shuiying.crossOrigin = 'Anonymous';
    if (!noWatermark && isShowALLFlag) {
      loadingImageList.push(loading(shuiying));
    }

    const qccLogo = new Image();
    qccLogo.src = ImgLogo;
    qccLogo.crossOrigin = 'Anonymous';
    loadingImageList.push(loading(qccLogo));

    // _.forEach(_.filter(legendList, { type: 'arrow' }), sArrow => {
    //   const arrowImg = new Image()
    //   if (sArrow.arrowType === 'arrow-gray') {
    //     arrowImg.src = arrowGray
    //   } else if (sArrow.arrowType === 'arrow-blue') {
    //     arrowImg.src = arrowBlue
    //   } else if (sArrow.arrowType === 'arrow-red') {
    //     arrowImg.src = arrowRed
    //   } else if (sArrow.arrowType === 'arrow-dash-blue') {
    //     arrowImg.src = arrowDashBlue
    //   } else if (sArrow.arrowType === 'arrow-green') {
    //     arrowImg.src = arrowGreen
    //   } else {
    //     arrowImg.src = arrowDarkBlue
    //   }
    //   loadingImageList.push(loading(arrowImg))
    //   arrowImg.crossOrigin = 'Anonymous'
    //   sArrow.arrow = arrowImg
    // })

    Promise.all([...loadingImageList]).then(() => {
      const canvas = document.createElement('canvas'); // 准备空画布
      const minSize = { w: 940 * scale, h: 402 * scale };
      const imgSize = {
        width: _.max([minSize.w, img.width]),
        height: _.max([minSize.h, img.height]),
      };
      canvas.width = imgSize.width + paddingH;
      canvas.height = imgSize.height + paddingV;

      const context = canvas.getContext('2d'); // 取得画布的2d绘图上下文
      context.fillStyle = '#fff';
      context.fillRect(0, 0, canvas.width, canvas.height);

      // drawwatermark
      if (!noWatermark && isShowALLFlag) {
        shuiying.width = 240;
        shuiying.height = 200;
        for (let i = 0; i < canvas.width + shuiying.width; i += shuiying.width) {
          for (let j = 0; j < canvas.height + shuiying.height; j += shuiying.height) {
            context.drawImage(shuiying, i, j, shuiying.width, shuiying.height);
          }
        }
      }

      /** 绘制无数据 */
      if (isNoRelation) {
        const noRelationText = '根据当前维度配置，未发现主体之间存在符合条件的关系。';
        const noRelationTextSize = getTextSize(noRelationText, 12 * scale);
        context.beginPath();
        const rectSize = {
          width: noRelationTextSize.width + 20,
          height: noRelationTextSize.height + 20,
        };
        if (context.roundRect) {
          context.roundRect(canvas.width / 2 - rectSize.width / 2, 10 * scale, rectSize.width, rectSize.height, 2 * scale);
        } else {
          context.rect(canvas.width / 2 - rectSize.width / 2, 10 * scale, rectSize.width, rectSize.height, 2 * scale);
        }
        context.fillStyle = '#FFF0E0';
        context.fill();
        drawText({
          context,
          font: 12 * scale,
          text: noRelationText,
          x: canvas.width / 2 - rectSize.width / 2 + 10 * scale,
          y: noRelationTextSize.height + 10 * scale + 5,
          fillStyle: '#FF8900',
        });
      }

      /** 绘制下载时间 */
      // const downloadTime = time || moment().format('YYYY-MM-DD')
      // const downloadTimeText = `查询日期：${downloadTime}`
      // const textSize = getTextSize(downloadTimeText, 12 * scale)
      // drawText({
      //   context,
      //   font: 12 * scale,
      //   text: downloadTimeText,
      //   x: canvas.width - textSize.width - 10 * scale - 10 * scale,
      //   y: textSize.height + 10 * scale
      // })
      // 画图谱
      context.drawImage(img, (canvas.width - paddingH - img.width) / 2 + padding, (canvas.height - paddingV - img.height) / 2 + padding);

      /** 画底部内容 */
      /** 免责 */
      const disclaimerPrefix = '以上数据是';
      const disclaimerSubfix = '大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证。';
      const prefixSize = getTextSize(disclaimerPrefix, 12 * scale);
      const subfixSize = getTextSize(disclaimerSubfix, 12 * scale);
      /** 图例计算 */
      let legendListWidth = 0;
      const legendGap = 15 * scale;
      const legendSizeList = [];
      if (legendList) {
        _.forEach(legendList, (sLegend) => {
          const sLegendWidth = getTextSize(sLegend.text, 12 * scale);
          legendSizeList.push({
            text: sLegend.text,
            type: sLegend.type,
            color: sLegend.color,
            arrow: sLegend.arrow,
            bg: sLegend.bg,
            width: sLegendWidth.width,
            height: 12 * scale + 5 * scale + 10 * scale,
            offsetX: legendListWidth,
          });
          legendListWidth += sLegendWidth.width + legendGap;
        });
        legendListWidth += 15 * scale;
      }

      qccLogo.width = 50 * scale;
      qccLogo.height = 16 * scale;
      const bottomWidth = prefixSize.width + legendListWidth + subfixSize.width + qccLogo.width + 10 * scale;

      const bottomStart = canvas.width / 2 - bottomWidth / 2;

      /** 画图例 */
      if (legendSizeList.length) {
        // 计算图例的总宽度
        const totalLegendWidth = legendSizeList.reduce((acc, sLegend) => acc + sLegend.width + legendGap, 0) - legendGap;

        // 计算图例的起始位置
        // if (!isShowALLFlag) {
        //   // 水平居中
        //   bottomStart = (canvas.width - totalLegendWidth) / 2
        // }

        // 重置每个图例的 offsetX
        let cumulativeWidth = 0;
        legendSizeList.forEach((sLegend) => {
          sLegend.offsetX = cumulativeWidth;
          cumulativeWidth += sLegend.width + legendGap;
        });

        _.forEach(legendSizeList, (sLegend) => {
          drawText({
            context,
            font: 12 * scale,
            text: sLegend.text,
            x: bottomStart + sLegend.offsetX,
            y: canvas.height - sLegend.height + 15 * scale,
          });

          if (sLegend.type === 'circle') {
            context.beginPath();
            context.arc(
              bottomStart + sLegend.offsetX + (sLegend.width - 10 * scale) / 2 + 5 * scale,
              canvas.height - sLegend.height - 5 * scale,
              5 * scale,
              0,
              2 * Math.PI
            );
            context.fillStyle = sLegend.bg;
            context.fill();
          } else if (sLegend.type === 'arrow') {
            const arrowWidth = 24 * scale;
            const arrowHeight = 8 * scale;
            context.drawImage(
              sLegend.arrow,
              bottomStart + sLegend.offsetX + (sLegend.width - arrowWidth) / 2,
              canvas.height - sLegend.height - 10 * scale,
              arrowWidth,
              arrowHeight
            );
          } else if (sLegend.type === 'rect') {
            context.beginPath();
            if (context.roundRect) {
              context.roundRect(
                bottomStart + sLegend.offsetX + (sLegend.width - 10 * scale) / 2,
                canvas.height - sLegend.height - 10 * scale,
                10 * scale,
                10 * scale,
                2 * scale
              );
            } else {
              context.rect(
                bottomStart + sLegend.offsetX + (sLegend.width - 10 * scale) / 2,
                canvas.height - sLegend.height - 10 * scale,
                10 * scale,
                10 * scale
              );
            }
            context.fillStyle = sLegend.bg;
            context.fill();
            context.strokeStyle = sLegend.color;
            context.stroke();
          }
        });

        context.beginPath();
        context.moveTo(bottomStart + totalLegendWidth - 15 * scale, canvas.height - (12 + 14) * scale);
        context.lineTo(bottomStart + totalLegendWidth - 15 * scale, canvas.height - 12 * scale);
        context.strokeStyle = '#EEE';
        context.stroke();
      }

      if (isShowALLFlag) {
        drawText({
          context,
          font: 12 * scale,
          text: disclaimerPrefix,
          x: bottomStart + legendListWidth,
          y: canvas.height - 14 * scale,
        });
        context.drawImage(
          qccLogo,
          bottomStart + prefixSize.width + 5 * scale + legendListWidth,
          canvas.height - qccLogo.height - 11 * scale,
          qccLogo.width,
          qccLogo.height
        );
        drawText({
          context,
          font: 12 * scale,
          text: disclaimerSubfix,
          x: bottomStart + legendListWidth + prefixSize.width + qccLogo.width + 10 * scale,
          y: canvas.height - 14 * scale,
        });
      }
      if (type === 'uri') {
        return resolve(canvas.toDataURL());
      }
      // 在文件名中无法使用：
      const fileName =
        chartType === 'find-relation'
          ? `任职关联-${moment().format('YYYY-MM-DD HH:mm')}.png`
          : `${companyName}-${title}-${moment().format('YYYY-MM-DD')}.png`;
      if (!HTMLCanvasElement.prototype.toBlob) {
        Object.defineProperty(HTMLCanvasElement.prototype, 'toBlob', {
          value(callback, type, quality) {
            const self = this;
            setTimeout(() => {
              const binStr = Buffer.from(self.toDataURL(type, quality).split(',')[1]);
              const len = binStr.length;
              const arr = new Uint8Array(len);

              for (let i = 0; i < len; i++) {
                arr[i] = binStr.charCodeAt(i);
              }

              callback(new Blob([arr], { type: type || 'image/png' }));
            });
          },
        });
      }
      setTimeout(() => {
        try {
          canvas.toBlob((blob) => {
            fileSaver.saveAs(blob, fileName);
          });
          resolve('aaa');
        } catch (err) {
          reject(err);
        }
      });
    });
  });
};
