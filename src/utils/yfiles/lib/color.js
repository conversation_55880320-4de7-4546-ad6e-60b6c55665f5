/*
 * Created by chen xue song on - 2023/11/28.
 */

/** 颜色统一管理，方便后续主题支持，这里禁止随意乱加颜色！！！，添加颜色必须和设计确认 */
const colors = {
  /** 黑色：#333333 */
  color_normal_1: '#333333',
  /** 灰色：#008be9 */
  color_normal_2: '#008be9',
  /** 中性4 */
  color_normal_4: '#999999',
  /** 灰色：#BBBBBB */
  color_normal_5: '#BBBBBB',
  /** 白色：#ffffff */
  color_normal_10: '#ffffff',
  /** 蓝色：#128bed */
  color_blue_5: '#128bed',
  color_blue_6: '#008be9',
  /** 红色 #ff4860 */
  color_red_5: '#ff4860',
  /** 橙色 #FF8900 */
  color_orange_5: '#FF8900',
  color_fill: '#F6FBFE',
};

export { colors };
