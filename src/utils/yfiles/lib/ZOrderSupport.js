/*
 * Created by <PERSON> on - 2024/01/12.
 */

// source from: view/zorder

/* eslint-disable max-classes-per-file */

// let yfiles

// if (__BROWSER__) {
//   yfiles = require('yfiles')
// }

import {
  BaseClass,
  EventArgs,
  GroupingNodePositionHandler,
  HashMap,
  IComparer,
  INode,
  IReparentNodeHandler,
  LabelLayerPolicy,
  List,
} from 'yfiles';

export class ZOrderSupport extends BaseClass(IComparer) {
  get masterGraph() {
    return this.$masterGraph;
  }

  tempZOrders;

  zOrderChangedListeners;

  $graphComponent = null;

  $masterGraph = null;

  foldingView = null;

  masterGroupingSupport = null;

  zOrders;

  tempParents;

  addZOrderForNewNodes = false;

  get graphComponent() {
    return this.$graphComponent;
  }

  constructor(graphComponent) {
    if (!graphComponent.inputMode) {
      throw new Error('Your should initialize GraphEditorInputMode for graphComponent firstly');
    }

    super();
    this.zOrderChangedListeners = [];

    this.zOrders = new HashMap();
    this.tempZOrders = new HashMap();
    this.tempParents = new HashMap();

    this.addZOrderForNewNodes = true;

    this.initializeGraphComponent(graphComponent);
  }

  initializeGraphComponent(graphComponent) {
    this.$graphComponent = graphComponent;
    this.foldingView = graphComponent.graph.foldingView;
    this.$masterGraph = this.foldingView !== null ? this.foldingView.manager.masterGraph : graphComponent.graph;
    this.masterGroupingSupport = this.masterGraph.groupingSupport;

    this.$graphComponent.graphModelManager.provideUserObjectOnMainCanvasObject = true;
    this.$graphComponent.graphModelManager.nodeManager.comparer = this;

    graphComponent.graphModelManager.nodeLabelLayerPolicy = LabelLayerPolicy.AT_OWNER;
    graphComponent.graphModelManager.edgeLabelLayerPolicy = LabelLayerPolicy.AT_OWNER;
    graphComponent.graphModelManager.portLabelLayerPolicy = LabelLayerPolicy.AT_OWNER;

    graphComponent.graph.decorator.nodeDecorator.positionHandlerDecorator.setFactory((node) => {
      return new ZOrderNodePositionHandler(node, this, null);
    });

    this.configureGraphClipboard(graphComponent.clipboard);

    this.masterGraph.addNodeCreatedListener(this.onNodeCreated.bind(this));
  }

  compare(x, y) {
    const masterX = this.getMasterNode(x);
    const masterY = this.getMasterNode(y);

    const parentX = this.getParent(masterX);
    const parentY = this.getParent(masterY);
    if (parentX === parentY) {
      return this.zOrderOf(masterX) - this.zOrderOf(masterY);
    }

    const nca = this.masterGroupingSupport.getNearestCommonAncestor(masterX, masterY);
    const pathToRootX = this.masterGroupingSupport.getPathToRoot(masterX);
    const pathToRootY = this.masterGroupingSupport.getPathToRoot(masterY);

    const ancestorX = !nca ? pathToRootX.at(-1) : pathToRootX.at(pathToRootX.indexOf(nca) - 1);
    const ancestorY = !nca ? pathToRootY.at(-1) : pathToRootY.at(pathToRootY.indexOf(nca) - 1);

    return this.zOrderOf(ancestorX) - this.zOrderOf(ancestorY);
  }

  getZOrder(node) {
    return this.zOrderOf(this.getMasterNode(node));
  }

  setZOrder(key, newZOrder) {
    const master = this.getMasterNode(key);
    const oldZOrder = this.zOrders.get(master);
    if (oldZOrder !== newZOrder) {
      if (this.masterGraph.undoEngineEnabled) {
        this.masterGraph.addUndoUnit(
          'Undo z-Order Change',
          'Redo z-Order Change',
          () => {
            this.zOrders.set(master, oldZOrder);
            this.update(master);
          },
          () => {
            this.zOrders.set(master, newZOrder);
            this.update(master);
          }
        );
      }
      this.zOrders.set(master, newZOrder);
      this.onZIndexChanged(key, newZOrder, oldZOrder);
    }
  }

  onZIndexChanged(item, newValue, oldValue) {
    const eventArgs = new ZIndexChangedEventArgs(item, newValue, oldValue);
    this.zOrderChangedListeners.forEach((listener) => {
      listener(this, eventArgs);
    });
  }

  addZIndexChangedLister(listener) {
    this.zOrderChangedListeners.push(listener);
  }

  removeZIndexChangedLister(listener) {
    const index = this.zOrderChangedListeners.indexOf(listener);
    if (index > 0) {
      this.zOrderChangedListeners.splice(index, 1);
    }
  }

  update(node) {
    this.graphComponent.graphModelManager.update(this.getViewNode(node));
  }

  arrangeNodes(viewNodes, zOrder) {
    let prev = null;
    for (const node of viewNodes) {
      this.setZOrder(this.getMasterNode(node), zOrder++);
      const canvasObject = this.graphComponent.graphModelManager.getMainCanvasObject(node);
      if (!prev) {
        canvasObject.toBack();
      } else {
        canvasObject.above(prev);
      }
      prev = canvasObject;
    }
  }

  setTempZOrder(node, tempParent, newZOrder, force = false) {
    const master = this.getMasterNode(node);
    if (force || this.getZOrder(master) !== newZOrder) {
      this.tempZOrders.set(master, newZOrder);
    }
    const masterParent = tempParent ? this.getMasterNode(tempParent) : this.foldingView ? this.foldingView.localRoot : null;
    this.tempParents.set(master, masterParent);
  }

  setTempNormalizedZOrders(parent) {
    const children = this.graphComponent.graph.getChildren(parent).toList();
    children.sort(this);
    let zOrder = 0;
    for (const child of children) {
      this.setTempZOrder(child, parent, zOrder++);
      if (this.graphComponent.graph.isGroupNode(child)) {
        this.setTempNormalizedZOrders(child);
      }
    }
  }

  removeTempZOrder(node) {
    const master = this.getMasterNode(node);
    this.tempZOrders.delete(master);
    this.tempParents.delete(master);
  }

  applyTempZOrders(update = false) {
    this.tempZOrders.forEach((keyValuePair) => {
      this.setZOrder(keyValuePair.key, keyValuePair.value);
      if (update) {
        this.update(keyValuePair.key);
      }
    });
    this.clearTempZOrders();
  }

  clearTempZOrders() {
    this.tempZOrders.clear();
    this.tempParents.clear();
  }

  raise(nodes) {
    nodes.sort(this);

    let prev = null;
    for (let i = nodes.size - 1; i >= 0; i--) {
      const node = nodes.get(i);
      const co = this.graphComponent.graphModelManager.getMainCanvasObject(node);
      const nextCO = co.nextSibling;
      if (nextCO) {
        let tmp;
        const nextNode = (tmp = this.graphComponent.graphModelManager.getModelItem(nextCO)) instanceof INode ? tmp : null;
        if (nextNode && nextNode !== prev) {
          this.swapZOrder(node, nextNode);
          this.graphComponent.graphModelManager.update(node);
        }
      }
      prev = node;
    }
  }

  lower(nodes) {
    nodes.sort(this);

    let prev = null;
    for (const node of nodes) {
      const co = this.graphComponent.graphModelManager.getMainCanvasObject(node);
      const prevCO = co.previousSibling;
      if (prevCO) {
        let tmp;
        const prevNode = (tmp = this.graphComponent.graphModelManager.getModelItem(prevCO)) instanceof INode ? tmp : null;
        if (prevNode && prevNode !== prev) {
          this.swapZOrder(node, prevNode);
          this.graphComponent.graphModelManager.update(node);
        }
      }
      prev = node;
    }
  }

  toFront(nodes) {
    for (const grouping of nodes.groupBy(
      (node) => this.graphComponent.graph.getParent(node),
      (type, elements) => ({
        groupNode: type,
        children: elements ? elements.toList() : new List(),
      })
    )) {
      const groupNode = grouping.groupNode;
      const toFrontChildren = grouping.children;
      const allChildren = this.graphComponent.graph.getChildren(groupNode).toList();
      if (toFrontChildren.size < allChildren.size) {
        allChildren.sort(this);
        toFrontChildren.sort(this);
        allChildren.removeAll((node) => {
          return toFrontChildren.includes(node);
        });
        const last = allChildren.last();
        let zOrder = this.getZOrder(last) + 1;
        for (const node of toFrontChildren) {
          this.setZOrder(node, zOrder++);
          this.update(node);
        }
      }
    }
  }

  toBack(nodes) {
    for (const grouping of nodes.groupBy(
      (node) => this.graphComponent.graph.getParent(node),
      (type, elements) => ({
        groupNode: type,
        children: elements ? elements.toList() : new List(),
      })
    )) {
      const groupNode = grouping.groupNode;
      const toBackChildren = grouping.children;
      const allChildren = this.graphComponent.graph.getChildren(groupNode).toList();
      if (toBackChildren.size < allChildren.size) {
        allChildren.sort(this);
        toBackChildren.sort(this);
        allChildren.removeAll((node) => {
          return toBackChildren.includes(node);
        });
        const first = allChildren.get(0);
        let zOrder = this.getZOrder(first) - 1;

        for (let i = toBackChildren.size - 1; i >= 0; i--) {
          const node = toBackChildren.get(i);
          this.setZOrder(node, zOrder--);
          this.update(node);
        }
      }
    }
  }

  onNodeCreated(sender, evt) {
    const undoEngine = this.graphComponent.graph.undoEngine;
    if (!this.addZOrderForNewNodes || undoEngine?.performingUndo || undoEngine?.performingRedo) {
      return;
    }
    const newNode = evt.item;
    const parent = this.getParent(newNode);
    const newZOrder = this.masterGraph
      .getChildren(parent)
      .map(this.getZOrder.bind(this))
      .reduce((acc, current) => Math.max(acc, current + 1), Number.MIN_VALUE);
    this.zOrders.set(newNode, newZOrder);
    this.update(newNode);
  }

  clear() {
    this.zOrders.clear();
    this.tempZOrders.clear();
    this.tempParents.clear();
  }

  getMasterNode(node) {
    const foldingView = this.graphComponent.graph.foldingView;
    if (foldingView) {
      if (foldingView.manager.masterGraph.contains(node)) {
        return node;
      }
      return foldingView.getMasterItem(node);
    }
    return node;
  }

  getViewNode(node) {
    if (this.graphComponent.graph.contains(node)) {
      return node;
    }
    const foldingView = this.graphComponent.graph.foldingView;
    if (foldingView && foldingView.manager.masterGraph.contains(node)) {
      return foldingView.getViewItem(node);
    }
    return null;
  }

  getParent(masterNode) {
    const parent = this.tempParents.get(masterNode);
    if (parent) {
      return parent;
    }
    if (!masterNode) {
      return null;
    }
    return this.masterGraph.getParent(masterNode);
  }

  zOrderOf(node) {
    let zOrder = this.tempZOrders.get(node);
    if (zOrder) {
      return zOrder;
    }
    zOrder = this.zOrders.get(node);
    if (zOrder) {
      return zOrder;
    }
    return 0;
  }

  swapZOrder(node1, node2) {
    const zOrder1 = this.getZOrder(node1);
    const zOrder2 = this.getZOrder(node2);
    this.setZOrder(node1, zOrder2);
    this.setZOrder(node2, zOrder1);
  }

  $clipboardZOrders = new HashMap();

  $newClipboardItems = new List();

  $clipboard = null;

  configureGraphClipboard(clipboard) {
    this.$clipboard = clipboard;

    clipboard.toClipboardCopier.addNodeCopiedListener(this.onCopiedToClipboard.bind(this));

    clipboard.fromClipboardCopier.addNodeCopiedListener(this.onCopiedFromClipboard.bind(this));
    clipboard.duplicateCopier.addNodeCopiedListener(this.onCopiedFromClipboard.bind(this));

    clipboard.addElementsCuttingListener(this.beforeCut.bind(this));
    clipboard.addElementsCopyingListener(this.beforeCopy.bind(this));
    clipboard.addElementsPastingListener(this.beforePaste.bind(this));
    clipboard.addElementsDuplicatingListener(this.beforeDuplicate.bind(this));
    clipboard.addElementsPastedListener(this.afterPaste.bind(this));
    clipboard.addElementsDuplicatedListener(this.afterDuplicate.bind(this));
  }

  onCopiedToClipboard(sender, evt) {
    this.$clipboardZOrders.set(evt.copy, this.getClipboardZOrder(evt.original));
  }

  onCopiedFromClipboard(sender, evt) {
    this.$newClipboardItems.add(evt.copy);
    this.$clipboardZOrders.set(evt.copy, this.getClipboardZOrder(evt.original));
  }

  getClipboardZOrder(node) {
    const zOrder = this.$clipboardZOrders.get(node);
    return zOrder !== null ? zOrder : this.getZOrder(node);
  }

  beforeCut(sender, eventArgs) {
    this.storeInitialZOrder(
      this.$graphComponent.graph,
      this.$clipboard.createDefaultCutFilter(this.$graphComponent.selection, this.$graphComponent.graph)
    );
  }

  beforeCopy(sender, eventArgs) {
    this.storeInitialZOrder(
      this.$graphComponent.graph,
      this.$clipboard.createDefaultCopyFilter(this.$graphComponent.selection, this.$graphComponent.graph)
    );
  }

  beforePaste(sender, eventArgs) {
    this.$newClipboardItems.clear();
  }

  afterPaste(sender, eventArgs) {
    const targetGraph = this.$graphComponent.graph;
    this.arrangeItems(this.$newClipboardItems, targetGraph.foldingView);
  }

  beforeDuplicate(sender, eventArgs) {
    this.storeInitialZOrder(
      this.$graphComponent.graph,
      this.$clipboard.createDefaultDuplicateFilter(this.$graphComponent.selection, this.$graphComponent.graph)
    );
    this.$newClipboardItems.clear();
  }

  afterDuplicate(sender, eventArgs) {
    const sourceGraph = this.$graphComponent.graph;
    this.arrangeItems(this.$newClipboardItems, sourceGraph.foldingView);
  }

  storeInitialZOrder(sourceGraph, filter) {
    const items = sourceGraph.nodes.filter((node) => filter(node)).toList();
    if (items.size > 1) {
      items.sort(this);
    }
    this.$clipboardZOrders.clear();
    const foldingView = sourceGraph.foldingView;
    for (let i = 0; i < items.size; i++) {
      const item = foldingView ? foldingView.getMasterItem < INode > items.get(i) : items.get(i);
      this.$clipboardZOrders.set(item, i);
    }
  }

  arrangeItems(newMasterItems, foldingView) {
    newMasterItems.sort((node1, node2) => {
      return this.getZOrder(node1) - this.getZOrder(node2);
    });
    const gmm = this.$graphComponent.graphModelManager;

    const itemsNotInView = new List();
    const groupToItems = new HashMap();
    for (const masterItem of newMasterItems) {
      const viewItem = foldingView ? foldingView.getViewItem < INode > masterItem : masterItem;
      if (!viewItem) {
        itemsNotInView.add(masterItem);
      } else {
        const co = gmm.getMainCanvasObject(viewItem);
        if (!co) {
          itemsNotInView.add(masterItem);
        } else {
          const coGroup = co.group;
          let newNodesInGroup = groupToItems.get(coGroup);
          if (!newNodesInGroup) {
            newNodesInGroup = new List();
            groupToItems.set(coGroup, newNodesInGroup);
          }
          newNodesInGroup.add(viewItem);
        }
      }
    }
    for (let i = 0; i < itemsNotInView.size; i++) {
      this.setZOrder(itemsNotInView.get(i), i);
    }

    for (const groupItemsPair of groupToItems) {
      const itemsInGroup = groupItemsPair.value;

      let topNodeNotJustAdded = null;
      let walker = groupItemsPair.key.lastChild;
      while (walker) {
        let tmp;
        const node = (tmp = gmm.getModelItem(walker)) instanceof INode ? tmp : null;
        if (node && !itemsInGroup.includes(node)) {
          topNodeNotJustAdded = node;
          break;
        }
        walker = walker.previousSibling;
      }
      let nextZOrder = topNodeNotJustAdded ? this.getZOrder(topNodeNotJustAdded) + 1 : 0;

      for (const node of itemsInGroup) {
        this.setZOrder(node, nextZOrder++);
      }
      for (const node of itemsInGroup) {
        this.update(node);
      }
    }
  }

  configureInputMode(inputMode) {
    inputMode.addDeletingSelectionListener(this.beforeDeleteSelection.bind(this));
    inputMode.addDeletedSelectionListener(this.afterDeleteSelection.bind(this));
    inputMode.addGroupingSelectionListener(this.beforeGrouping.bind(this));
    inputMode.addUngroupingSelectionListener(this.beforeUngrouping.bind(this));
    inputMode.reparentNodeHandler = new ZOrderReparentHandler(inputMode.reparentNodeHandler, this);
    this.configureMoveInputMode(inputMode);
  }

  beforeGrouping(sender, e) {
    const nodes = e.selectionList.selectedNodes.toList();
    nodes.sort(this);

    for (let i = 0; i < nodes.size; i++) {
      this.setZOrder(nodes.get(i), i);
    }
  }

  beforeUngrouping(sender, e) {
    const graph = this.$graphComponent.graph;
    const nodes = e.selectionList.selectedNodes
      .filter((node) => {
        return graph.getParent(node) !== null;
      })
      .toList();

    nodes.sort(this);

    const topLevelNodes = graph.getChildren(null).toList();
    topLevelNodes.sort(this);

    const newTopLevelNodes = new List();
    let topLevelIndex = 0;

    let nextTopLevelNode = null;
    const gs = graph.groupingSupport;

    for (const node of nodes) {
      const topLevelAncestor = gs.getPathToRoot(node).at(-1);
      while (topLevelAncestor !== nextTopLevelNode) {
        nextTopLevelNode = topLevelNodes.get(topLevelIndex++);
        newTopLevelNodes.add(nextTopLevelNode);
      }
      newTopLevelNodes.add(node);
    }

    for (let i = topLevelIndex; i < topLevelNodes.size; i++) {
      newTopLevelNodes.add(topLevelNodes.get(i));
    }

    for (let i = 0; i < newTopLevelNodes.size; i++) {
      this.setZOrder(newTopLevelNodes.get(i), i);
    }
  }

  $deleteSelectionNewParents = null;

  $absOrder = null;

  $parentChangedListener = null;

  beforeDeleteSelection(sender, eList) {
    const graph = this.$graphComponent.graph;
    const nodes = graph.nodes.toList();
    nodes.sort(this);
    this.$absOrder = new Map();
    for (let i = 0; i < nodes.size; i++) {
      this.$absOrder.set(nodes.get(i), i);
    }
    this.$deleteSelectionNewParents = new Set();
    this.$parentChangedListener = this.onParentChanged.bind(this);
    graph.addParentChangedListener(this.$parentChangedListener);
  }

  afterDeleteSelection(sender, eList) {
    const graph = this.$graphComponent.graph;
    graph.removeParentChangedListener(this.$parentChangedListener);

    for (const newParent of this.$deleteSelectionNewParents) {
      if (newParent === null || graph.contains(newParent)) {
        const children = graph.getChildren(newParent).toList();
        children.sort((node1, node2) => {
          return this.$absOrder.get(node1) - this.$absOrder.get(node2);
        });
        this.arrangeNodes(children, 0);
      }
    }
    this.$deleteSelectionNewParents = null;
  }

  onParentChanged(sender, evt) {
    const graph = this.$graphComponent.graph;
    const newParent = graph.getParent(evt.item);
    this.$deleteSelectionNewParents.add(newParent);
  }

  $movedNodes = new List();

  $oldParents = new Map();

  $maxOldZOrder = new Map();

  $maxRootZOrder = Number.MIN_VALUE;

  configureMoveInputMode(geim) {
    geim.moveInputMode.addDragStartingListener(this.moveStarting.bind(this));
    geim.moveInputMode.addDragFinishedListener(this.moveFinished.bind(this));
    geim.moveInputMode.addDragCanceledListener(this.moveCanceled.bind(this));
  }

  moveStarting(sender, e) {
    const graph = this.$graphComponent.graph;

    this.$movedNodes = this.$graphComponent.inputMode?.graphSelection?.selectedNodes.toList() || new List();
    this.$movedNodes.sort(this);

    this.$movedNodes.forEach((node) => {
      const parent = graph.getParent(node);
      this.$oldParents.set(node, parent);
      this.getOrCalculateMaxZOrder(parent);
    });

    this.getOrCalculateMaxZOrder(null);
  }

  getOrCalculateMaxZOrder(parent) {
    const graph = this.$graphComponent.graph;
    if (!parent) {
      if (this.$maxRootZOrder === Number.MIN_VALUE) {
        this.$maxRootZOrder = graph
          .getChildren(null)
          .map((node) => {
            return this.getZOrder(node);
          })
          .reduce((acc, current) => Math.max(acc, current), Number.MIN_VALUE);
      }
      return this.$maxRootZOrder;
    }
    let maxZOrder = this.$maxOldZOrder.get(parent);
    if (!maxZOrder) {
      const children = graph.getChildren(parent);
      maxZOrder = children.size > 0 ? children.reduce((acc, current) => Math.max(acc, this.getZOrder(current)), Number.MIN_VALUE) : 0;
      this.$maxOldZOrder.set(parent, maxZOrder);
    }
    return maxZOrder;
  }

  getZOrderForNewParent(node, parent) {
    let newZOrder = this.getOrCalculateMaxZOrder(parent) + 1;
    for (const movedNode of this.$movedNodes) {
      if (movedNode === node) {
        return newZOrder;
      }
      if (this.$oldParents.get(movedNode) !== parent) {
        return newZOrder++;
      }
    }
    return 0;
  }

  moveFinished(sender, e) {
    this.applyTempZOrders();
    this.cleanup();
  }

  moveCanceled(sender, e) {
    this.clearTempZOrders();
    this.cleanup();
  }

  cleanup() {
    this.$movedNodes.clear();
    this.$oldParents.clear();
    this.$maxOldZOrder.clear();
    this.$maxRootZOrder = Number.MIN_VALUE;
  }
}

export class ZIndexChangedEventArgs extends EventArgs {
  $item;

  $newZIndex;

  $oldZIndex;

  constructor(item, newZIndex, oldZIndex) {
    super();
    this.$item = item;
    this.$newZIndex = newZIndex;
    this.$oldZIndex = oldZIndex;
  }

  get item() {
    return this.$item;
  }

  get newZIndex() {
    return this.$newZIndex;
  }

  get oldZIndex() {
    return this.$oldZIndex;
  }
}

export class ZOrderNodePositionHandler extends GroupingNodePositionHandler {
  $node;

  $initialParent;

  $currentParent;

  $zOrderSupport;

  constructor(node, zOrderSupport, wrappedHandler = null) {
    super(node, wrappedHandler);
    this.$node = node;

    this.$zOrderSupport = zOrderSupport;
    this.$initialParent = null;
    this.$currentParent = null;
  }

  initializeDrag(context) {
    const graph = context.graph;

    this.$initialParent = graph.getParent(this.$node);
    this.$currentParent = this.$initialParent;

    super.initializeDrag(context);
  }

  setCurrentParent(context, node, parent) {
    if (parent !== this.$initialParent) {
      const zOrderSupport = context.graph?.lookup(ZOrderSupport.$class);
      if (zOrderSupport) {
        const tempZOrder = zOrderSupport.getZOrderForNewParent(node, parent);
        this.$zOrderSupport.setTempZOrder(node, parent, tempZOrder);
      }
      super.setCurrentParent(context, node, parent);
    } else if (parent !== this.$currentParent) {
      this.$zOrderSupport.removeTempZOrder(node);

      super.setCurrentParent(context, node, parent);
    }
    this.$currentParent = parent;
  }

  dragFinished(context, originalLocation, newLocation) {
    super.dragFinished(context, originalLocation, newLocation);
    this.cleanUp();
  }

  cancelDrag(context, originalLocation) {
    super.cancelDrag(context, originalLocation);
    this.cleanUp();
  }

  cleanUp() {
    this.$initialParent = null;
    this.$currentParent = null;
  }
}
class ZOrderReparentHandler extends BaseClass(IReparentNodeHandler) {
  constructor(handler, zOrderSupport) {
    super();
  }

  isReparentGesture(context, node) {
    return this.handler.isReparentGesture(context, node);
  }

  isValidParent(context, node, newParent) {
    return this.handler.isValidParent(context, node, newParent);
  }

  reparent(context, node, newParent) {
    const masterNode = this.zOrderSupport.getMasterNode(node);
    const masterParent = newParent ? this.zOrderSupport.getMasterNode(newParent) : null;

    this.handler.reparent(context, node, newParent);

    const zIndex = this.calculateNewZIndex(this.zOrderSupport.masterGraph, masterNode, masterParent);
    this.zOrderSupport.setZOrder(masterNode, zIndex);

    const viewNode = this.zOrderSupport.getViewNode(masterNode);
    if (viewNode) {
      this.zOrderSupport.graphComponent.graphModelManager.update(viewNode);
    }
  }

  shouldReparent(context, node) {
    return this.handler.shouldReparent(context, node);
  }

  calculateNewZIndex(masterGraph, masterNode, masterParent) {
    const children = masterGraph.getChildren(masterParent);
    if (children.size === 1) {
      return 0;
    }

    return Math.max(...children.filter((current) => current !== masterNode).map((n) => this.zOrderSupport.getZOrder(n))) + 1;
  }
}
