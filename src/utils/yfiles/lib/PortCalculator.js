/*
 * Created by <PERSON> on - 2024/01/18.
 */

import { Point } from 'yfiles';

const fy = (x, r) => Math.sqrt(Math.pow(r, 2) - Math.pow(x, 2));

/**
 *
 * @param count point count
 * @param radius circle radius
 * @param w custom width, default: Math.sqrt(Math.pow(radius, 2) * 2)
 * @returns Point[] {@link Point}
 */
const calcPointsOnCircle = (count, radius, w) => {
  /**
   * 计算步进值 - 将定宽（w或者Math.sqrt(Math.pow(radius, 2) * 2))）平均分成 count - 1 份
   * 奇数边的情况下，中间创建一个Port，剩余按照 step * i 向两边延展
   * 偶数边的情况下，从中间点开始（不创建Port），分别向两边按照 step * i - step / 2的位置创建Port
   */
  const step = (w ?? Math.sqrt(Math.pow(radius, 2) * 2)) / (count - 1);

  const points = [];

  // 奇数的场景 - 从中间开始向两边创建Port（中间位置需要创建Port）
  if (count % 2 === 1) {
    /**
     * 奇数Ports位置示意图, ○: 表示创建Port的位置，-:表示一个单位的长度
     * |-------------------|
     * |                   |
     * ○---------○---------○
     * │         │         │
     * ▼         ▼         ▼
     */
    // 先在底部中心点先添加一个Port
    points.push(new Point(0, radius));
    // 然后从底部中心点开始，分别向两边创建对称的Ports
    for (let i = 1; i <= (count - 1) / 2; ++i) {
      // 计算Y的偏移值
      const offsetY = fy(step * i, radius);
      // 右边一个
      points.push(new Point(step * i, offsetY));
      // 左边一个
      points.push(new Point(-step * i, offsetY));
    }
  } else {
    // 偶数的场景, 从中间向两侧创建Port（中间位置不需要创建Port）
    /**
     * 偶数Ports位置示意图, ○: 表示创建Port的位置，-:表示一个单位的长度
     * |------------------- |
     * |                    |
     * ○------○------○------○
     * │      │      │      │
     * ▼      ▼      ▼      ▼
     */
    // 然后从左到右创建port
    for (let i = 1; i <= count / 2; ++i) {
      // 计算Y的偏移值
      const offsetY = fy(step * i - step / 2, radius);
      // 右边一个
      points.push(new Point(step * i - step / 2, offsetY));
      // 左边一个
      points.push(new Point(-step * i + step / 2, offsetY));
    }
  }

  return points;
};

const calcPointsAtBottom = calcPointsOnCircle;

/**
 *
 * @param count point count
 * @param radius circle radius
 * @param w custom width, default: Math.sqrt(Math.pow(radius, 2) * 2)
 * @returns Point[] {@link Point}
 */
const calcPointsAtTop = (count, radius, w) => {
  const points = [];

  for (const p of calcPointsOnCircle(count, radius, w)) {
    points.push(new Point(p.x, -p.y));
  }

  return points;
};

/**
 *
 * @param count point count
 * @param radius circle radius
 * @param w custom width, default: Math.sqrt(Math.pow(radius, 2) * 2)
 * @returns Point[] {@link Point}
 */
const calcPointsAtRight = (count, radius, w) => {
  const points = [];

  for (const p of calcPointsOnCircle(count, radius, w)) {
    points.push(new Point(p.y, p.x));
  }

  return points;
};

/**
 *
 * @param count point count
 * @param radius circle radius
 * @param w custom width, default: Math.sqrt(Math.pow(radius, 2) * 2)
 * @returns Point[] {@link Point}
 */
const calcPointsAtLeft = (count, radius, w) => {
  const points = [];

  for (const p of calcPointsOnCircle(count, radius, w)) {
    points.push(new Point(-p.y, p.x));
  }

  return points;
};

const PointsForCircle = {
  atBottom: calcPointsAtBottom,
  atTop: calcPointsAtTop,
  atRight: calcPointsAtRight,
  atLeft: calcPointsAtLeft,
};

export { PointsForCircle };

export default { PointsForCircle };
