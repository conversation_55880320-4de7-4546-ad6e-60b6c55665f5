import { createArrowStyle } from './edge-style';
import { LineType } from './node-type';
import { Stroke, INode } from 'yfiles';
import _ from 'lodash';

export const highlightEdges = (highlightedEdges, graphComponent) => {
  highlightedEdges.forEach((edge) => {
    const edgeStyle = edge.style.clone();
    if (!edge.tag.isMain) {
      edgeStyle.stroke = new Stroke({
        fill: '#128BED',
        thickness: 1.5,
        dashStyle: edge.tag.lineType === LineType.dashed ? [3, 3] : '',
      });
      edgeStyle.targetArrow = createArrowStyle({
        strokeColor: '#128BED',
        fillColor: '#128BED',
      });
    }
    graphComponent.graph.setStyle(edge, edgeStyle);
    if (!edge.tag.isMain) {
      setTargetClass(edge, graphComponent, 'hight-light-line');
    }
  });
};

export const disHighlightNode = (notHighlightedNodes, graphComponent) => {
  notHighlightedNodes.forEach((node) => {
    setTargetClass(node, graphComponent);
    node.labels.forEach((label) => {
      setTargetClass(label, graphComponent);
    });
  });
};

export const disHighlightEdge = (notHighlightedEdges, graphComponent) => {
  notHighlightedEdges.forEach((edge) => {
    setTargetClass(edge, graphComponent);

    edge.labels.forEach((label) => {
      setTargetClass(label, graphComponent);
    });
  });
};

export const getHighlightEdges = (node, graphComponent) => {
  let highlightedEdges = [];
  const outEdgesFromSelectedToTarget = (clickNode) => {
    const outEdges = graphComponent.graph.outEdgesAt(clickNode);
    highlightedEdges = [...highlightedEdges, ...outEdges];
    outEdges.forEach((edge) => {
      outEdgesFromSelectedToTarget(edge.targetNode);
    });
  };
  outEdgesFromSelectedToTarget(node);
  return highlightedEdges;
};

export const clearHighlights = (graphComponent) => {
  graphComponent.graph.nodes.forEach((node) => {
    if (supportsCssClass(node.style)) {
      node.style.cssClass = '';
    }
  });

  graphComponent.graph.edges.forEach((edge) => {
    const edgeStyle = edge.style.clone();
    if (edge.tag.isMain) {
      edgeStyle.stroke = new Stroke({
        fill: '#FF6060',
        thickness: 1.5,
        dashStyle: edge.tag.lineType === LineType.dashed ? [3, 3] : '',
      });
      edgeStyle.targetArrow = createArrowStyle({
        strokeColor: '#FF6060',
        fillColor: '#FF6060',
      });
    } else {
      edgeStyle.stroke = new Stroke({
        fill: '#E3E3E3',
        thickness: 1.5,
        dashStyle: edge.tag.lineType === LineType.dashed ? [3, 3] : '',
      });
      edgeStyle.targetArrow = createArrowStyle();
    }
    graphComponent.graph.setStyle(edge, edgeStyle);

    if (supportsCssClass(edge.style)) {
      edge.style.cssClass = '';
    }
  });

  graphComponent.graph.labels.forEach((label) => {
    if (supportsCssClass(label.style)) {
      label.style.cssClass = '';
    }
  });
};

export const handleHoverClass = (evt, graphComponent, getClassFun = undefined) => {
  /** 添加hover效果 */
  if (evt?.item) {
    const item = evt.item;
    let className;
    if (getClassFun && _.isFunction(getClassFun)) {
      className = getClassFun(evt.item.tag);
    }
    if (!item || !(item instanceof INode)) {
      return;
    }
    if (item?.style?.cssClass?.includes('gray20')) {
      return;
    }
    const nodeStyle = item?.style?.clone();
    if (item?.tag?.type === 'company') {
      className += 'hover-company';
      if (item.tag.isSelf || item.tag.isRoot) {
        className += 'hover-self';
      }
    } else if (item?.tag?.type === 'combined') {
      className += 'hover-combined';
    } else if (item?.tag?.type === 'person') {
      className += 'hover-person';
    }
    if (!className || nodeStyle.cssClass) {
      return;
    }

    setTargetClass(item, graphComponent, className);
    item.labels?.forEach((label) => {
      setTargetClass(label, graphComponent, className);
    });
  }

  /** 清除hover效果 */
  if (evt?.oldItem) {
    const oldItem = evt.oldItem;

    if (!oldItem || !(oldItem instanceof INode)) {
      return;
    }

    if (oldItem?.style?.cssClass?.includes('gray20')) {
      return;
    }

    setTargetClass(oldItem, graphComponent, '');

    oldItem.labels?.forEach((label) => {
      setTargetClass(label, graphComponent, '');
    });
  }
};

/** 清除hover效果 */
export const clearHoverClass = (item, graphComponent) => {
  if (!item || !(item instanceof INode)) {
    return;
  }

  if (item?.style?.cssClass?.includes('gray20')) {
    return;
  }

  setTargetClass(item, graphComponent, '');

  item.labels?.forEach((label) => {
    setTargetClass(label, graphComponent, '');
  });
};

const supportsCssClass = (style) => {
  return 'cssClass' in style && typeof style.cssClass === 'string';
};

const setTargetClass = (target, graphComponent, cssClass = 'gray20') => {
  if (supportsCssClass(target.style)) {
    const labelStyle = target?.style?.clone();
    labelStyle.cssClass = cssClass;
    graphComponent.graph.setStyle(target, labelStyle);
  }
};
