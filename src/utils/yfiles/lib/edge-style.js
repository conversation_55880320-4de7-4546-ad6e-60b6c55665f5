import { Arrow, ArrowType, DefaultLabelStyle, Font, PolylineEdgeStyle, Stroke } from 'yfiles';

/** 边连接箭头 */
export const createArrowStyle = ({
  /** 边框颜色 */
  strokeColor = '#128BED',
  /** 填充色 */
  fillColor = '#128BED',
} = {}) => {
  return new Arrow({
    type: ArrowType.TRIANGLE,
    stroke: strokeColor,
    fill: fillColor,
  });
};

/** 边样式 */
export const createEdgeStyle = ({ fillColor = '#E3E3E3', arrowColor = '#128BED', isDashed = false, smoothingLength = 10 } = {}) => {
  return new PolylineEdgeStyle({
    smoothingLength,
    stroke: new Stroke({
      fill: fillColor,
      thickness: 1.5,
      dashStyle: isDashed ? [3, 3] : '',
    }),

    targetArrow: createArrowStyle({
      strokeColor: arrowColor,
      fillColor: arrowColor,
    }),
  });
};

/** 边文案样式 */
export const createEdgeLabelStyle = ({ fillColor = '#128BED' } = {}) => {
  return new DefaultLabelStyle({
    font: new Font({
      fontSize: 12,
    }),
    textSize: 12,
    textFill: fillColor,
    backgroundFill: '#FFF',
    backgroundStroke: 'none',
  });
};
