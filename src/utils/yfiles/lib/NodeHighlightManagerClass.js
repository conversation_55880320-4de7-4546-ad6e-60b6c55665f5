import { IEdge, <PERSON>abel, INode, IPort, Stroke } from 'yfiles';

import { TogglePortStyle } from '../templates/toggle-port-style/TogglePortStyle';
import { createArrowStyle } from './edge-style';
import { LineType } from './node-type';

const colorBlue5 = '#128BED';
const colorRed5 = 'FF6060';
const colorNormal65 = '#E3E3E3';

export class NodeHighlightManager {
  graphComponent;

  graph;

  constructor({ graphComponent = null, graph = null }) {
    this.graphComponent = graphComponent;
    this.graph = graph;
  }

  highlightEdges(highlightedEdges) {
    highlightedEdges.forEach((edge) => {
      const edgeStyle = edge.style.clone();
      if (!edge.tag.isMain) {
        edgeStyle.stroke = new Stroke({
          fill: colorBlue5,
          thickness: 1.5,
          dashStyle: edge.tag.lineType === LineType.dashed ? [3, 3] : '',
        });
        edgeStyle.targetArrow = createArrowStyle({
          strokeColor: colorBlue5,
          fillColor: colorBlue5,
        });
      }
      this.graph.setStyle(edge, edgeStyle);
    });
  }

  disHighlightNode(notHighlightedNodes) {
    notHighlightedNodes.forEach((node) => {
      this.setGray(node);
      node.labels.forEach((label) => {
        this.setGray(label);
      });
      const togglePort = node.ports.find((p) => p.style instanceof TogglePortStyle);
      if (togglePort) {
        this.setGray(togglePort);
      }
    });
  }

  disHighlightEdge(notHighlightedEdges) {
    notHighlightedEdges.forEach((edge) => {
      this.setGray(edge);

      edge.labels.forEach((label) => {
        this.setGray(label);
      });
    });
  }

  getHighlightEdges(node) {
    let highlightedEdges = [];
    const outEdgesFromSelectedToTarget = (clickNode) => {
      let outEdges = this.graph.edges.filter((edge) => edge.sourceNode?.tag?.id === clickNode.tag?.id);
      outEdges = outEdges.filter((edge) => !edge.invisible).toList();

      highlightedEdges = [...highlightedEdges, ...outEdges];
      outEdges.forEach((edge) => {
        outEdgesFromSelectedToTarget(edge.targetNode);
      });
    };
    outEdgesFromSelectedToTarget(node);
    return highlightedEdges;
  }

  removeNodeGrayClass(node) {
    if (this.supportsCssClass(node.style)) {
      node.style.cssClass = node.style.cssClass.replace('gray20', '');
      node.labels.forEach((label) => {
        if (this.supportsCssClass(label.style)) {
          label.style.cssClass = label.style.cssClass.replace('gray20', '');
        }
      });
      const togglePort = node.ports.find((p) => p.style instanceof TogglePortStyle);
      if (togglePort) {
        togglePort.style.cssClass = togglePort.style.cssClass.replace('gray20', '');
      }
    }
  }

  removeEdgeGrayClass(edge) {
    if (this.supportsCssClass(edge.style)) {
      edge.style.cssClass = edge.style.cssClass.replace('gray20', '');
      edge.labels.forEach((label) => {
        if (this.supportsCssClass(label.style)) {
          label.style.cssClass = label.style.cssClass.replace('gray20', '');
        }
      });
    }
  }

  clearHighlights() {
    this.graph.nodes.forEach((node) => {
      if (this.supportsCssClass(node.style)) {
        node.style.cssClass = node.style.cssClass.replace('gray20', '');
        const togglePort = node.ports.find((p) => p.style instanceof TogglePortStyle);
        if (togglePort) {
          togglePort.style.cssClass = togglePort.style.cssClass.replace('gray20', '');
        }
      }
    });

    this.graph.edges.forEach((edge) => {
      const edgeStyle = edge.style.clone();

      if (edge.tag.isMain) {
        edgeStyle.stroke = new Stroke({
          fill: colorRed5,
          thickness: 1.5,
          dashStyle: edge.tag.lineType === LineType.dashed ? [3, 3] : '',
        });
        edgeStyle.targetArrow = createArrowStyle({
          strokeColor: colorRed5,
          fillColor: colorRed5,
        });
      } else {
        edgeStyle.stroke = new Stroke({
          fill: colorNormal65,
          thickness: 1.5,
          dashStyle: edge.tag.lineType === LineType.dashed ? [3, 3] : '',
        });
        edgeStyle.targetArrow = createArrowStyle();
      }
      this.graph.setStyle(edge, edgeStyle);

      if (this.supportsCssClass(edge.style)) {
        edge.style.cssClass = '';
      }
    });

    this.graph.labels.forEach((label) => {
      if (this.supportsCssClass(label.style)) {
        label.style.cssClass = '';
      }
    });
  }

  supportsCssClass(style) {
    return 'cssClass' in style && typeof style.cssClass === 'string';
  }

  setGray(target) {
    if (this.supportsCssClass(target.style)) {
      let labelStyle;
      try {
        labelStyle = target.style.clone();
        labelStyle.cssClass = 'gray20';
      } catch (e) {}
      if (!labelStyle) {
        return;
      }
      if (target instanceof INode) {
        this.graph.setStyle(target, labelStyle);
      } else if (target instanceof IEdge) {
        this.graph.setStyle(target, labelStyle);
      } else if (target instanceof ILabel) {
        this.graph.setStyle(target, labelStyle);
      } else if (target instanceof IPort) {
        this.graph.setStyle(target, labelStyle);
      }
    }
  }
}
