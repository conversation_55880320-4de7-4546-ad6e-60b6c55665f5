import {
  Arrow,
  ArrowType,
  DefaultLabelStyle,
  Font,
  HorizontalTextAlignment,
  PolylineEdgeStyle,
  Rect,
  RectangleNodeStyle,
  ShapeNodeShape,
  ShapeNodeStyle,
  Size,
  Stroke,
  TextWrapping,
  VerticalTextAlignment,
} from 'yfiles';

import { BusinessType } from './node-type';
import { PersonNodeStyle } from '../templates/person-node-style-template';
import { colors } from './color';
const cornerSize = 4;

/*
 * Created by chen xue song on - 2023/11/28.
 */

const nodeConfig = {
  width: 130,
  height: 42,
};

const blueBorderLine = new Stroke(colors.color_blue_5, 1);

const arrowDefault = new Arrow({
  type: ArrowType.TRIANGLE,
  stroke: colors.color_normal_2,
  fill: colors.color_normal_2,
});

const arrowRed = new Arrow({
  type: ArrowType.TRIANGLE,
  stroke: colors.color_red_5,
  fill: colors.color_red_5,
});

const arrowBlue = new Arrow({
  type: ArrowType.TRIANGLE,
  stroke: colors.color_blue_5,
  fill: colors.color_blue_5,
});

export const ArrowColorTypes = {
  RED: 'red',
  BLUE: 'blue',
};

class NodeHelper {
  graphComponent;

  constructor(graphComponent) {
    this.graphComponent = graphComponent;
  }

  /** 通用公司布局大小 */
  nodeCompanyLayout() {
    return new Rect(
      (this.graphComponent.size.width - nodeConfig.width) / 2,
      (this.graphComponent.size.height - nodeConfig.height) / 2,
      nodeConfig.width,
      nodeConfig.height
    );
  }

  /** 通用人员布局大小 */
  nodePersonLayout() {
    return new Rect(this.graphComponent.size.width / 2 - 30, this.graphComponent.size.height / 2 - 30, 60, 60);
  }

  /** 根据业务类型选中特定的布局大小 */
  nodeLayoutLayoutByBusinessType(businessType) {
    switch (businessType) {
      case BusinessType.quoted_company:
        return new Rect(this.graphComponent.size.width / 2 - 65, this.graphComponent.size.height / 2 - 21, 130, 64);
      case BusinessType.disabled:
        return new Rect(this.graphComponent.size.width / 2 - 65, this.graphComponent.size.height / 2 - 21, 130, 64);
      case BusinessType.highlighted:
        return new Rect(this.graphComponent.size.width / 2 - 65, this.graphComponent.size.height / 2 - 21, 130, 64);
      default:
        return new Rect(this.graphComponent.size.width / 2 - 65, this.graphComponent.size.height / 2 - 21, 130, 64);
    }
  }

  /** 通用公司样式 */
  nodeCompanyStyleProvider(cornerRadius = 5) {
    return new RectangleNodeStyle({
      stroke: colors.color_blue_5,
      fill: colors.color_fill,
      cornerStyle: 'round',
      cornerSize: cornerSize,
    });
  }

  /** 通用公司高亮样式 */
  nodeCompanyHighlightStyleProvider() {
    return new ShapeNodeStyle({
      shape: ShapeNodeShape.ROUND_RECTANGLE,
      cssClass: 'round-rectangle',
      stroke: `2px ${colors.color_red_5}`,
      fill: 'none',
    });
  }

  /** 通用人员样式 */
  nodePersonStyleProvider() {
    return new ShapeNodeStyle({
      shape: ShapeNodeShape.ELLIPSE,
      fill: colors.color_normal_10,
      stroke: colors.color_red_5,
    });
  }

  /** 通用人员高亮样式 */
  nodePersonHighlightStyleProvider() {
    return new ShapeNodeStyle({
      shape: ShapeNodeShape.ELLIPSE,
      fill: 'none',
      stroke: 'none',
    });
  }

  /** 通用人员样式 */
  nodePersonCircleStyleProvider() {
    return new ShapeNodeStyle({
      shape: ShapeNodeShape.ELLIPSE,
      keepIntrinsicAspectRatio: true,
      stroke: blueBorderLine,
      fill: colors.color_blue_5,
    });
  }

  /** 头像类型人员 */
  nodeImageStyleProvider() {
    return new PersonNodeStyle(new Size(50, 50));
  }

  nodeDisabledStyleProvider() {
    return new ShapeNodeStyle({
      shape: ShapeNodeShape.ROUND_RECTANGLE,
      stroke: colors.color_normal_4,
      fill: colors.color_normal_4,
    });
  }

  nodeHighlightedStyleProvider() {
    return new RectangleNodeStyle({
      stroke: blueBorderLine,
      fill: colors.color_blue_5,
      cornerStyle: 'round',
      cornerSize: cornerSize,
    });
  }

  /** 通用label样式 */
  nodeLabelStyleProvider(labelColor) {
    return new DefaultLabelStyle({
      verticalTextAlignment: VerticalTextAlignment.CENTER,
      horizontalTextAlignment: HorizontalTextAlignment.CENTER,
      wrapping: TextWrapping.CHARACTER_ELLIPSIS,
      maximumSize: new Size(120, 42),
      textFill: labelColor || colors.color_normal_1,
    });
  }

  /** 通用边样式 */
  edgeStyleProvider(arrowColor) {
    let arrow = arrowDefault;
    switch (arrowColor) {
      case ArrowColorTypes.RED:
        arrow = arrowRed;
        break;
      case ArrowColorTypes.BLUE:
        arrow = arrowBlue;
        break;
      default:
        arrow = arrowDefault;
    }
    return new PolylineEdgeStyle({
      smoothingLength: 10,
      stroke: new Stroke('#e3e3e3', 2),
      targetArrow: arrow,
    });
  }

  /** 通用边高亮样式 */
  edgeHightlightStyleProvider() {
    return new PolylineEdgeStyle({
      smoothingLength: 10,
      stroke: new Stroke(colors.color_red_5, 1),
      // targetArrow: arrow
    });
  }

  /** 通用边样式 */
  edgeLabelStyleProvider() {
    return new DefaultLabelStyle({
      font: new Font({
        fontSize: 12,
      }),
      textSize: 12,
      textFill: colors.color_normal_2,
    });
  }
}

export { NodeHelper };
