/*
 * Created by chen xue song on - 2023/12/21.
 */

import { companyNodeSize } from './node-size';

// let yfiles

// if (__BROWSER__) {
//   yfiles = require('yfiles')
// }

import { DefaultLabelStyle, HorizontalTextAlignment, RectangleNodeStyle, Size, TextWrapping, VerticalTextAlignment } from 'yfiles';

/** 默认的公司类型节点 */
export const createNodeCompanyStyle = ({ stroke = '#128BED', fill = '#F6FBFE' } = {}) => {
  return new RectangleNodeStyle({
    stroke,
    fill,
    cornerStyle: 'round',
    cornerSize: 2,
  });
};

export const createNodeCompanyLabel = ({ textFill = '#333333', ownerFill = '#F6FBFE', font = null } = {}) => {
  const obj = {
    backgroundFill: ownerFill,
    verticalTextAlignment: VerticalTextAlignment.CENTER,
    horizontalTextAlignment: HorizontalTextAlignment.CENTER,
    wrapping: TextWrapping.WORD,
    maximumSize: new Size(companyNodeSize.width, Number.POSITIVE_INFINITY),
    textFill,
  };
  if (font) {
    obj.font = font;
  }
  return new DefaultLabelStyle(obj);
};
