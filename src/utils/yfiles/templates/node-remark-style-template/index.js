import VuejsNodeStyle from 'demo-utils/VuejsNodeStyle';

/**
 * Creates a new {@link VuejsNodeStyle} with a template that shows more details on higher
 * zoom levels and fewer details on lower zoom levels.
 */
export const createOrgChartNodeStyle = (graphComponent, nodeSize) => {
  const nodeStyle = new VuejsNodeStyle(nodeStyleTemplate);

  // create the drop shadow element only once
  // const defsElement = graphComponent.svgDefsManager.defs
  // if (!defsElement.querySelector('#node-dropshadow')) {
  //   defsElement.appendChild(createDropShadowElement(nodeSize))
  // }

  return nodeStyle;
};

const nodeStyleTemplate = `<g>
<rect fill='transparent' stroke='#128bed' :width='layout.width' :height='layout.height' class="round-rectangle"></rect>
<clipPath id="circle">
<circle rx="15" ry="10" cx="45" cy="40" r="30" />
</clipPath>
<template v-if="tag.imageUrl">
<image  clip-path="url(#circle)" v-if="tag.imageUrl" :xlink:href="tag.imageUrl" x='15' y='10' width='60' height='60'></image>
  <g style='font-size:10px; font-family:Roboto,sans-serif; font-weight: 300; fill: #444'>
    <svg-text x='100' y='10' :width='layout.width - 15' :content='tag.name' :line-spacing='0.2' font-size='10' font-family='Roboto,sans-serif' :wrapping='3'></svg-text>
    <text transform='translate(100 60)' v-if="tag.isKzr">实际控制人</text>
    <!-- use the VuejsNodeStyle svg-text template which supports wrapping -->
    <text transform='translate(100 40)' >{{tag.benefitText}}</text>
  </g>
</template>
<template v-else>
<g style='font-size:10px; font-family:Roboto,sans-serif; font-weight: 300; fill: #444'>
    <svg-text x='15' y='10' :width='layout.width - 15' :content='tag.name' :line-spacing='0.2' font-size='10' font-family='Roboto,sans-serif' :wrapping='3'></svg-text>
    <text transform='translate(15 60)' v-if="tag.isKzr">实际控制人</text>
    <!-- use the VuejsNodeStyle svg-text template which supports wrapping -->
    <text transform='translate(15 40)' >{{tag.benefitText}}</text>
  </g>
</template>
</g>`;

/**
 * Creates the drop shadow element for the nodes.
 */
const createDropShadowElement = (nodeSize) => {
  // pre-render the node's drop shadow using HTML5 canvas rendering
  const canvas = window.document.createElement('canvas');
  canvas.width = nodeSize.width + 30;
  canvas.height = nodeSize.height + 30;
  const context = canvas.getContext('2d');
  context.fillStyle = 'rgba(0,0,0,0.4)';
  context.filter = 'blur(4px)';
  context.globalAlpha = 0.6;
  roundRect(context, 10, 10, nodeSize.width, nodeSize.height);
  context.fill();
  const dataUrl = canvas.toDataURL('image/png');
  // put the drop-shadow in an SVG image element
  const image = window.document.createElementNS('http://www.w3.org/2000/svg', 'image');
  image.setAttribute('width', `${canvas.width}`);
  image.setAttribute('height', `${canvas.height}`);
  image.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', dataUrl);
  // switch off pointer events on the drop shadow
  image.setAttribute('style', 'pointer-events: none');
  image.setAttribute('id', 'node-dropshadow');
  return image;
};

/**
 * Helper function to draw a round rectangle on a given canvas context.
 */
const roundRect = (ctx, x, y, width, height, radius = 5) => {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
};
