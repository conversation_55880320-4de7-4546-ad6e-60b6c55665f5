import { Font, GeneralPath, NodeStyleBase, Rect, SvgVisual, TextRenderSupport } from 'yfiles';

import { personNodeSize } from '../../lib/node-size';
import { SVGNS, createControlTag, createText } from '../helper';

const gapBetweenTipAndCircle = 10;
const colorOrange5 = '#F07E0D';
const colorNormal10 = '#fff';
const font = new Font({ fontSize: 12 });
export class PersonNodeTopTip extends NodeStyleBase {
  cssClass = '';

  fillColor = colorOrange5;

  fontColor = colorNormal10;

  tipSize = { width: 0, height: 0 };

  /* eslint-disable no-undef */
  constructor(params = ({ cssClass = '', fillColor = colorOrange5, fontColor = colorNormal10 } = {})) {
    super();
    this.cssClass = params?.cssClass || this.cssClass;
    this.fillColor = params?.fillColor || this.fillColor;
    this.fontColor = params?.fontColor || this.fontColor;
  }

  get heightWithTipAndGap() {
    return this.tipSize.height + gapBetweenTipAndCircle;
  }

  get heightWithTipGapAndHalfCircle() {
    return this.tipSize.height + gapBetweenTipAndCircle + personNodeSize.height / 2;
  }

  createVisual(context, node) {
    const { x, y, width, height } = node.layout;
    // 整个节点容器
    const gContainer = document.createElementNS(SVGNS, 'g');
    gContainer.setAttribute('class', this.cssClass);

    // 实控人标签
    const labels = node?.tag?.tipLabels || [];
    if (labels.length) {
      const { controlTag, tipSize } = createControlTag({ labels });
      this.tipSize = tipSize;

      gContainer.appendChild(controlTag);

      SvgVisual.setTranslate(controlTag, 0, 0);
    }

    // 包裹头像的圆
    const wrapperEl = document.createElementNS(SVGNS, 'circle');
    wrapperEl.setAttribute('fill', this.fillColor);
    wrapperEl.setAttribute('cx', String(width / 2));
    wrapperEl.setAttribute('cy', String(this.heightWithTipGapAndHalfCircle));
    wrapperEl.setAttribute('r', String(personNodeSize.width * 0.5));

    gContainer.appendChild(wrapperEl);

    // 人员头像
    if (node.tag?.imageUrl) {
      const gAvatar = document.createElementNS(SVGNS, 'g');
      // 图片剪裁
      const imageClip = document.createElementNS(SVGNS, 'clipPath');
      imageClip.setAttribute('id', 'imageClip');
      const imgClipEllipse = document.createElementNS(SVGNS, 'circle');
      imgClipEllipse.setAttribute('cx', String(personNodeSize.width / 2));
      imgClipEllipse.setAttribute('cy', String(personNodeSize.height / 2));
      imgClipEllipse.setAttribute('r', String(personNodeSize.width * 0.5 - 1));

      imgClipEllipse.setAttribute('fill', this.fillColor);
      imageClip.appendChild(imgClipEllipse);

      // 头像
      const avatar = document.createElementNS(SVGNS, 'image');
      // avatar.setAttribute('href', node.tag?.imageUrl)
      avatar.setAttributeNS('http://www.w3.org/1999/xlink', 'href', node.tag?.imageUrl);

      avatar.setAttribute('width', String(personNodeSize.width));
      avatar.setAttribute('height', String(personNodeSize.height));

      avatar.setAttribute('clip-path', 'url(#imageClip)');
      let avatarTranslateX;
      if (this.tipSize.width > personNodeSize.width) {
        avatarTranslateX = this.tipSize.width / 2 - personNodeSize.width / 2;
      } else {
        avatarTranslateX = 0;
      }
      SvgVisual.setTranslate(avatar, avatarTranslateX, this.tipSize.height + gapBetweenTipAndCircle);

      gAvatar.appendChild(imageClip);
      gAvatar.appendChild(avatar);
      gContainer.appendChild(gAvatar);
    }

    // 人员名称
    const textEl = createText({
      text: node.tag.name,
      fontSize: '12',
      fontColor: this.fontColor,
      fontWeight: !node.tag?.imageUrl ? 'bold' : '',
    });

    const nameSize = TextRenderSupport.measureText(node.tag.name, font);
    textEl.setAttribute('x', ((width - nameSize.width) * 0.5).toString());
    if (node.tag?.imageUrl) {
      const nameClipG = document.createElementNS(SVGNS, 'g');
      const nameBgEl = document.createElementNS(SVGNS, 'ellipse');
      const nameClipPath = document.createElementNS(SVGNS, 'clipPath');
      const nameClipRect = document.createElementNS(SVGNS, 'rect');
      const nameDefs = document.createElementNS(SVGNS, 'defs');
      const nameDefLinerGradient = document.createElementNS(SVGNS, 'linearGradient');
      const stopStart = document.createElementNS(SVGNS, 'stop');
      const stopEnd = document.createElementNS(SVGNS, 'stop');

      stopStart.setAttribute('offset', '0');
      stopStart.setAttribute('stop-color', 'rgba(0,0,0,0.4)');
      stopEnd.setAttribute('offset', '50%');
      stopEnd.setAttribute('stop-color', 'rgba(0,0,0,0)');

      nameDefLinerGradient.setAttribute('id', 'linerId');
      nameDefLinerGradient.setAttribute('x1', '0');
      nameDefLinerGradient.setAttribute('y1', '100%');
      nameDefLinerGradient.setAttribute('x2', '0');
      nameDefLinerGradient.setAttribute('y2', '0');

      nameBgEl.setAttribute('cx', String(width / 2));
      nameBgEl.setAttribute('cy', String(this.heightWithTipGapAndHalfCircle));
      nameBgEl.setAttribute('rx', String(personNodeSize.width / 2));
      nameBgEl.setAttribute('ry', String(personNodeSize.height / 2));
      nameBgEl.setAttribute('clip-path', `url(#name-clip-path-${node.tag.id})`);
      nameBgEl.setAttribute('fill', 'url(#linerId)');

      nameClipRect.setAttribute('width', String(personNodeSize.width));
      nameClipRect.setAttribute('height', '20');
      nameClipRect.setAttribute('x', String((width - personNodeSize.width) / 2));
      nameClipRect.setAttribute('y', String(height - 20));

      nameClipPath.setAttribute('id', `name-clip-path-${node.tag.id}`);
      nameClipPath.appendChild(nameClipRect);
      nameClipG.appendChild(nameClipPath);
      nameClipG.appendChild(nameBgEl);
      nameDefLinerGradient.appendChild(stopStart);
      nameDefLinerGradient.appendChild(stopEnd);

      nameDefs.appendChild(nameDefLinerGradient);
      nameClipG.appendChild(nameDefs);

      gContainer.appendChild(nameClipG);
      textEl.setAttribute('y', String(height - 9));
    } else {
      textEl.setAttribute('y', (this.heightWithTipGapAndHalfCircle + nameSize.height / 2).toString());
    }

    gContainer.appendChild(textEl);

    SvgVisual.setTranslate(gContainer, x, y);

    return new SvgVisual(gContainer);
  }

  // 更新节点位置
  updateVisual(context, oldVisual, node) {
    const { x, y } = node.layout;
    const g = oldVisual.svgElement;
    g.setAttribute('class', this.cssClass);

    SvgVisual.setTranslate(g, x, y);
    return oldVisual;
  }

  getOutline(node) {
    const path = new GeneralPath();
    const { x, y, width } = node.layout;
    const bounds = new Rect(
      x + (width - personNodeSize.width) / 2,
      y + this.heightWithTipAndGap,
      personNodeSize.width,
      personNodeSize.height
    );
    path.appendEllipse(bounds, false);
    return path;
  }
}
