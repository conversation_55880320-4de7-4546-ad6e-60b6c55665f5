.person-link {
  &:hover {
    color: #0d61a6 !important;
  }
}

.node-container {
  padding: 5px;
  border-color: #128bed;
  border: 1px solid;
  border-radius: 2px;
  font-size: 12px;
  overflow: visible;
  line-height: 18px;
  height: 88px;



  .remark {
    top: -37px;
    left: calc(50% - 40px);
    background-color: #128bed;
    color: #fff;
    font-size: 12px;
    line-height: 26px;
    border-radius: 2px;
    width: 80px;
    text-align: center;
    height: 26px;
  }

  .arrow {
    top: -11px;
    left: calc(50% - 5px);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 6px solid #128bed;
  }
}