import { HtmlVisual, Insets, NodeStyleBase, Rect } from 'yfiles';

import './style.less';

const colorBlue5 = '#128bed';
const colorRed = '#F04040';
const containerStyle = `
  padding: 5px;
  width:130px;
  border: 1px solid;
  border-radius: 2px;
  font-size: 12px;
  line-height: 18px;
  border-color: ${colorBlue5};
  background-color: rgb(246,251,254);
  margin-top:5px;
  box-sizing: border-box;
  text-align:center;
  `;

const remarkStyle = `
  font-size: 12px;
  line-height: 26px;
  border-radius: 2px;
  width: 80px;
  text-align: center;
  height: 26px;
  background-color:${colorRed};
  color:#FFFFFF;
  `;
const arrowStyle = `
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid ${colorRed};
  margin-top:-1px;`;

/**
 * A custom HTML-based node style that uses both native HTML input elements and a custom
 * image selection grid to enable interactive editing of the node data.
 * The elements are styled using the CSS rules in styles.css
 */
export class NodeCombinedStyleTemplate extends NodeStyleBase {
  cssClass = '';

  defaultClass = 'html-style';

  createVisual(context, node) {
    const doc = context.canvasComponent?.div.ownerDocument;
    const { x, y, width, height } = node.layout;
    const div = doc.createElement('div');
    div.classList.add('html-style');

    const visual = HtmlVisual.from(div);
    const layout = new Rect(x, y, width, height);
    HtmlVisual.setLayout(div, layout);
    this.createContent(context, div, node);
    return visual;
  }

  updateVisual(context, oldVisual, node) {
    if (this.cssClass) {
      oldVisual.element.classList.add(this.cssClass);
    } else {
      oldVisual.element.className = this.defaultClass;
    }

    HtmlVisual.setLayout(oldVisual.element, node.layout);

    return oldVisual;
  }

  getBounds(context, node) {
    let bounds = node.layout.toRect();
    bounds = bounds.getEnlarged(new Insets(5, 0, 10, 0));

    return bounds;
  }

  /**
   * We only have to implement createContent() for this use case.
   * The base style takes care of updating the position of the container element in updateVisual().
   */
  createContent(context, element, node) {
    const tag = node.tag;
    element.innerHTML = createCompanyCombinedTemplate(tag.combinedNameList);
  }
}
const toPersonDetail = (keyNo, name) => {
  if (!keyNo) {
    return;
  }

  window.open(`/beneficaryDetail?personId=${keyNo}`);
};

window.toPersonDetail = toPersonDetail;

export const createCompanyCombinedTemplate = (combinedNameList) => {
  return `
  <div>
  <div style="display:flex;flex-direction:column;align-items:center;">
    <div style="${remarkStyle}">实际控制人</div>
    <div style="${arrowStyle} "></div>
  </div>
  <div class="combined"  style="${containerStyle}">
    ${combinedNameList
      .map((item, index) => {
        if (index !== combinedNameList.length - 1) {
          return `
            ${
              item.keyNo
                ? `<a class="person-link" style="color:${colorBlue5}"  href="javascript:;" onclick="window.toPersonDetail('${item.keyNo}', '${item.name}')"  target="_blank">${item.name}</a>`
                : `<span>${item.name}</span>`
            }
            <span>${'、'}</span>
          `;
        }
        return `
        ${
          item.keyNo
            ? `<a class="person-link" style="color:${colorBlue5}"  href="javascript:;" onclick="window.toPersonDetail('${item.keyNo}', '${item.name}')"  target="_blank">${item.name}</a>`
            : `<span>${item.name}</span>`
        }
          `;
      })
      .join('')}
  </div>
</div>
`;
};
