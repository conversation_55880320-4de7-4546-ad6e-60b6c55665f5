/*
 * Created by <PERSON> on - 2023/11/22.
 */

import { GeneralPath, Rect, <PERSON>hapeNodeShape, ShapeNodeStyle, <PERSON>ze, StringTemplateNodeStyle, TemplateNodeStyle } from 'yfiles';

export const nodeTemplate = `
    <g class="node">
      <!-- The defs section stores the clip path elements -->
      <defs>
        <clipPath id="imageClip">
          <ellipse
            cx="{TemplateBinding width, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"
            cy="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"
            rx="{TemplateBinding width, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5-5}"
            ry="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5-5}"/>
        </clipPath>
        <clipPath id="labelClip">
          <rect x="0" y="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v*0.7}"
            width="{TemplateBinding width}"
            height="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v*0.3}" />
        </clipPath>
      </defs>

      <!-- The background ellipse -->
      <ellipse fill="#ff4860"
        cx="{TemplateBinding width, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"
        cy="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"
        rx="{TemplateBinding width, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"
        ry="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"
      />

      <!-- The user avatar -->
      <image xlink:href="{Binding imageUrl, Converter=personNodeStyleConverters.icon}" clip-path="url(#imageClip)"
        x="5"
        y="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v*-0}"
        width="{TemplateBinding width, Converter=personNodeStyleConverters.calc, Parameter=$v-10}"
        height="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v-10}"
      />

      <!-- The half-circle background for the name -->
      <ellipse fill="#ff4860" clip-path="url(#labelClip)"
        cx="{TemplateBinding width, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"
        cy="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"
        rx="{TemplateBinding width, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"
        ry="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"/>

      <!-- The name text -->
      <text fill="#ffffff" data-content="{Binding name}" text-anchor="middle"
        style="font-family: Tahoma, Verdana, sans-serif"
        font-size="{TemplateBinding width, Converter=personNodeStyleConverters.fontSize}"
        x="{TemplateBinding width, Converter=personNodeStyleConverters.calc, Parameter=$v*0.5}"
        y="{TemplateBinding height, Converter=personNodeStyleConverters.calc, Parameter=$v*0.85}"/>
    </g>
  `;

TemplateNodeStyle.CONVERTERS.personNodeStyleConverters = {
  status: (val) => {
    switch (val) {
      case 'present':
        return 'rgba(231, 93, 82, 0.8)';
      case 'busy':
        return 'rgb(161,96,164, 0.8)';
      case 'unavailable':
        return 'rgb(110,165,106, 0.8)';
      default:
        return 'rgba(231, 93, 82, 0.8)';
    }
  },
  icon: (val) => `${val}`,
  // eslint-disable-next-line no-bitwise
  fontSize: (val) => ((val * 1.4) / 10) | 0,
  calc: (val, parameter) => {
    const expression = parameter.replace('$v', val);
    if (expression.match(/^[\d+-/()*]+$/g) !== null) {
      // eslint-disable-next-line no-eval
      return eval(expression);
    }
    return -1;
  },
};
export class PersonNodeStyle extends StringTemplateNodeStyle {
  constructor(minimumSize = new Size(100, 100)) {
    const outlinePath = new GeneralPath();
    outlinePath.appendEllipse(new Rect(0, 0, 1, 1), true);

    super({
      svgContent: nodeTemplate,

      minimumSize,
      normalizedOutline: outlinePath,
    });
  }

  getNodeHighlightStyle() {
    return new ShapeNodeStyle({
      stroke: 'none',
      fill: 'none',
      shape: ShapeNodeShape.ELLIPSE,
    });
  }
}
