/*
 * Created by chen xue song on - 2023/12/21.
 */

import { Font, Insets, NodeStyleBase, Size, SvgVisual, TextRenderSupport, TextWrapping } from 'yfiles';

import { createControlTag, createText } from '../helper';

const areaHGap = 10;
const areaVGap = 5;
const nameVGap = 0;

export class PersonNodeOversize extends NodeStyleBase {
  areaSize = new Size(0, 0);

  cssClass = '';

  tipSize = { width: 0, height: 0 };

  createVisual(context, node) {
    const { x, y } = node.layout;
    const { width, height } = node.tag.nodeSize;

    // 整个节点容器
    const gContainer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    gContainer.setAttribute('class', this.cssClass);

    const gInner = document.createElementNS('http://www.w3.org/2000/svg', 'g');

    // 人员节点
    const wrapperEl = document.createElementNS('http://www.w3.org/2000/svg', 'rect');

    wrapperEl.setAttribute('fill', '#F07E0D');
    wrapperEl.setAttribute('width', String(width));
    wrapperEl.setAttribute('height', String(height));
    wrapperEl.setAttribute('rx', String(20));
    wrapperEl.setAttribute('ry', String(20));
    gInner.appendChild(wrapperEl);

    const labels = node?.tag?.tipLabels || [];

    if (node.tag?.areaTag?.name) {
      this.areaSize = TextRenderSupport.measureText(node.tag.areaTag.name, new Font({ fontSize: 10 }));
    }

    // 实控人标签
    if (labels.length) {
      const { controlTag, tipSize } = createControlTag({ labels });
      this.tipSize = tipSize;
      gContainer.appendChild(controlTag);
    }

    // 人员名称
    const textEl = createText({ text: node.tag.name, fontWeight: 'bold' });

    TextRenderSupport.addText({
      targetElement: textEl,
      text: node.tag.name,
      font: new Font({ fontSize: 12, fontWeight: 'bold' }),
      wrapping: TextWrapping.WORD,
      maximumSize: new Size(120, Infinity),
    });

    gInner.appendChild(textEl);
    SvgVisual.setTranslate(textEl, (width - node.tag.labelSize.width) / 2, (height - node.tag.labelSize.height) / 2);
    SvgVisual.setTranslate(gInner, 0, this.tipSize.height + 10 + (this.areaSize.height / 3) * 2);

    if (node.tag?.areaTag?.name) {
      const rectEl = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      const areaEl = document.createElementNS('http://www.w3.org/2000/svg', 'text');

      const tagG = document.createElementNS('http://www.w3.org/2000/svg', 'g');

      areaEl.textContent = node.tag.areaTag.name;

      areaEl.setAttribute('font-size', '10');
      areaEl.setAttribute('fill', '#128BED');

      rectEl.setAttribute('fill', '#E2F1FD');
      rectEl.setAttribute('width', (this.areaSize.width + areaHGap).toString());
      rectEl.setAttribute('height', (this.areaSize.height + areaVGap).toString());
      rectEl.setAttribute('rx', String(2));
      rectEl.setAttribute('ry', String(2));

      tagG.appendChild(rectEl);
      tagG.appendChild(areaEl);
      SvgVisual.setTranslate(tagG, width - this.areaSize.width - areaHGap - 4, (-2 * (this.areaSize.height + areaVGap)) / 3);
      SvgVisual.setTranslate(areaEl, areaHGap / 2, this.areaSize.height);
      gInner.appendChild(tagG);
    }

    gContainer.appendChild(gInner);

    SvgVisual.setTranslate(gContainer, x, y);

    return new SvgVisual(gContainer);
  }

  // 更新节点位置
  updateVisual(context, oldVisual, node) {
    const { x, y } = node.layout;
    const g = oldVisual.svgElement;
    SvgVisual.setTranslate(g, x, y);
    g.setAttribute('class', this.cssClass);
    return oldVisual;
  }

  getBounds(context, node) {
    let bounds = node.layout.toRect();
    let h = this.tipSize.height + 2 * nameVGap + this.areaSize?.height;
    if (this.areaSize?.height) {
      h += (this.areaSize.height / 3) * 2;
    }
    if (this.tipSize.height) {
      bounds = bounds.getEnlarged(new Insets(0, h, 0, 0));
    }

    return bounds;
  }
}
