.node-custom {
  display: flex;
  justify-content: center;
  flex-direction: column;
  background: #fff;
  border: 1px solid #128bed;
  border-radius: 4px;
}

.node-custom .name {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  color: #333;
  font-size: 12px;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  text-align: center;
  line-height: 16px;
  padding: 5px 5px 0;
}

.node-custom .attr {
  background: #e2f1fd;
  font-size: 10px;
  text-align: center;
  color: #128bed;
  height: 24px;
  line-height: 24px;
  border-radius: 0 0 4px 4px;
}

.node-custom .tag {
  position: absolute;
  background: #e2f1fd;
  height: 18px;
  line-height: 18px;
  border-radius: 2px;
  font-size: 10px;
  padding: 0 6px;
  top: -9px;
  right: 4px;
  color: #128bed;
}

.tag-hidden .tag {
  display: none;
}
