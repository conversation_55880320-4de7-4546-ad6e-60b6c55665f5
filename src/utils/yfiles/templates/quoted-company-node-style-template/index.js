import './style.css';

/*
 * Created by chen xue song on - 2023/11/30.
 */
import {
  NodeStyleDecorationInstaller,
  ShapeNodeShape,
  ShapeNodeStyle,
  Size,
  StringTemplateNodeStyle,
  StyleDecorationZoomPolicy,
} from 'yfiles';

import { colors } from '../../lib/color';

export const nodeTemplate = `<foreignObject x="0" y="0" width="{TemplateBinding width}" height="{TemplateBinding height}">
    <body xmlns="http://www.w3.org/1999/xhtml">
      <div class="node-custom">
        <div class="name"  data-content="{Binding name}" >
        </div>
        <div class="attr" data-content="{Binding publicStatus}"></div>
        <div class="tag" data-content="{Binding tag}" ></div>
      </div>
    </body>
  </foreignObject>
  `;
export class QuotedCompanyNodeStyle extends StringTemplateNodeStyle {
  constructor({ minimumSize = new Size(100, 100), quotedDesc = '' }) {
    super({
      svgContent: nodeTemplate,
      minimumSize,
      cssClass: quotedDesc ? '' : 'hidden',
    });
  }

  getNodeHighlightStyle() {
    return new NodeStyleDecorationInstaller({
      nodeStyle: new ShapeNodeStyle({
        stroke: `2px ${colors.color_red_5}`,
        fill: 'none',
        shape: ShapeNodeShape.ROUND_RECTANGLE,
        cssClass: 'round-rectangle',
      }),
      zoomPolicy: StyleDecorationZoomPolicy.WORLD_COORDINATES,
      margins: 0,
    });
  }
}
