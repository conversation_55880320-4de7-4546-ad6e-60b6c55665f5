/*
 * Created by <PERSON> on - 2023/12/14.
 */

/* eslint-disable no-bitwise */
import { FreeNodePortLocationModel, GraphItemTypes, IPort, Point } from 'yfiles';

import './style.less';
import { TogglePortStyle } from './TogglePortStyle';

export default class TogglePortSupport {
  /**
   * 在 graphInputMode上配置相关属性和事件回调函数
   *
   * @param graphInputMode
   */
  initWithInputMode(graphInputMode) {
    // 确保PORT是可以被点击的
    graphInputMode.clickableItems = graphInputMode.clickableItems | GraphItemTypes.PORT;
    // 点击触发顺序
    graphInputMode.clickHitTestOrder = [GraphItemTypes.BEND, GraphItemTypes.PORT, GraphItemTypes.NODE, GraphItemTypes.ALL];

    // 配置点击响应函数
    graphInputMode.addItemClickedListener((_, evt) => {
      if (!(evt.item instanceof IPort)) {
        return;
      }
      const port = evt.item;
      if (port instanceof IPort && port.style instanceof TogglePortStyle) {
        const styleTag = port.style.styleTag;
        const collapsed = styleTag.collapsed ?? false;
        port.style.styleTag = { ...styleTag, collapsed: !collapsed };

        graphInputMode.graph.invalidateDisplays();
        styleTag?.onStatedChanged?.(!collapsed);

        // evt.handled = true 将终止事件继续传递
        // 假如不终止的话，Port的selected将会被激活，带来的副作用就是Port上有个Indicator
        evt.handled = true;
      }
    });
  }

  /**
   * 为node配置自定义port，以及 响应 onStatedChanged
   *
   * @param graph
   * @param node
   * @param locationParameter
   * @param action
   */
  addPort(graph, node, locationParameter = FreeNodePortLocationModel.NODE_BOTTOM_ANCHORED, onStatedChanged) {
    const style = new TogglePortStyle({ onStatedChanged });

    const { x, y, width, height } = node.layout;

    graph.addPortAt({
      owner: node,
      style,
      location: new Point(x + width / 2, y + height + 7),
    });
  }

  /**
   * 为node配置自定义port，以及 响应 onStatedChanged
   *
   * @param graph
   * @param node
   * @param locationParameter
   * @param action
   */
  addPortRight(graph, node, onStatedChanged, collapsed) {
    const style = new TogglePortStyle({ onStatedChanged, collapsed });

    const { x, y, width, height } = node.layout;

    graph.addPortAt({
      owner: node,
      style,
      location: new Point(x + width + 7, y + height / 2),
    });
  }
}
