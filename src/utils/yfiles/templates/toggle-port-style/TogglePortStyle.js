/*
 * Created by <PERSON> on - 2023/12/14.
 */

import { Size, StringTemplatePortStyle } from 'yfiles';

const colorNormal10 = '#fff';
const colorBlue5 = '#128bed';

const togglePortStyle = `
  transform: scale(1);
  transition: 0.2s ease-in-out;
`;

const togglePortEllipseExpand = `
    fill: ${colorNormal10};
    stroke: ${colorBlue5};
}`;

const togglePortEllipseCollapse = `
    fill: ${colorBlue5};
}`;

const togglePortStyleIconExpand = `
    stroke-width: 1px;
    stroke: ${colorBlue5};
}`;

const togglePortStyleIconExpandV = `
    stroke-width: 1px;
}`;

const togglePortStyleIconCollapse = `
    stroke-width: 1px;
    stroke: ${colorNormal10};

}`;

const portStyleTemplate = `
<g>
  <g class="toggle-port-style" style="${togglePortStyle}">
    <ellipse
    rx="7"
    ry="7"
    style="{TemplateBinding styleTag, Converter=togglePortSupportConverters.stateEllipseConverter}"/>
    <line x1="-3" y1="0" x2="3" y2="0" style="{TemplateBinding styleTag, Converter=togglePortSupportConverters.stateIconConverter,Parameter=h}"></line>
    <line x1="0" y1="-3" x2="0" y2="3" style="{TemplateBinding styleTag, Converter=togglePortSupportConverters.stateIconConverter,Parameter=v}"></line>
  </g>
</g>`;

StringTemplatePortStyle.CONVERTERS.togglePortSupportConverters = {
  stateIconConverter: (val, direction) => {
    if (direction === 'v') {
      return (val.collapsed ?? false) ? togglePortStyleIconCollapse : togglePortStyleIconExpandV;
    }
    return (val.collapsed ?? false) ? togglePortStyleIconCollapse : togglePortStyleIconExpand;
  },
  stateEllipseConverter: (val) => ((val.collapsed ?? false) ? togglePortEllipseCollapse : togglePortEllipseExpand),
};

export class TogglePortStyle extends StringTemplatePortStyle {
  constructor({ size, onStatedChanged, collapsed = false }) {
    super(portStyleTemplate);
    this.renderSize = size ?? new Size(14, 14);
    this.styleTag = { collapsed, collapsible: true, onStatedChanged };
  }
}
