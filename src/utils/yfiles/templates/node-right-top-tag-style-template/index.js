import _ from 'lodash';
import { Font, GeneralPath, NodeStyleBase, Size, SvgVisual, TextMeasurePolicy, TextRenderSupport, TextWrapping } from 'yfiles';

import { companyNodeSize } from '../../lib/node-size';
import { SVGNS, addText, createControlTag } from '../helper';

const hGap = 12;
const vGap = 4;
const colorBlue05 = '#F6FBFE';
const colorNormal10 = '#ffffff';
const colorBlue5 = '#128BED';
const colorBlue2 = '#E2F1FC';
const colorBlue3 = 'rgba(18, 139, 237, 0.5)';
const colorNormal1 = '#333333';
const iconSize = 15;

export class NodeRightTopTagStyle extends NodeStyleBase {
  tagSize = new Size(0, 0);

  tipSize = { width: 0, height: 0 };

  cssClass = '';

  get tagHeightWithGap() {
    if (!this.tagSize.height) {
      return 0;
    }
    return this.tagSize.height + vGap;
  }

  get tagHeightWithGapPartTwoOfThree() {
    return (2 * this.tagHeightWithGap) / 3;
  }

  get tipHeightWithGap() {
    let h = 0;
    if (this.tipSize.height) {
      h = this.tipSize.height + 10;
    }
    // 顶部存在控制人标签的时候，才需要增加这个间距
    if (h && this.tagHeightWithGap) {
      h += (this.tagHeightWithGap / 3) * 2;
    }
    return h;
  }

  createVisual(context, node) {
    const { x, y, width, height } = node.layout;

    const { labelSize, tagSize, publicSize } = node.tag;
    this.tagSize = tagSize;

    const pathElement = document.createElementNS(SVGNS, 'path');
    const g = document.createElementNS(SVGNS, 'g');
    // 实控人标签
    const labels = node?.tag?.tipLabels || [];
    if (labels.length) {
      const { controlTag, tipSize } = createControlTag({ labels });
      this.tipSize = tipSize;
      g.appendChild(controlTag);
      SvgVisual.setTranslate(controlTag, (width - tipSize.width) / 2, 0);
    }

    pathElement.setAttribute('d', createPathData(0, 0, width, height - this.tipHeightWithGap));

    pathElement.setAttribute('fill', colorBlue05);
    pathElement.setAttribute('stroke', colorBlue5);
    SvgVisual.setTranslate(pathElement, 0, this.tipHeightWithGap);
    g.setAttribute('class', this.cssClass);
    g.appendChild(pathElement);

    /** 上市信息部分 */
    if (node.tag.publicStatus) {
      const publicStatusElG = document.createElementNS(SVGNS, 'g');
      const publicStatusElRect = document.createElementNS(SVGNS, 'path');

      // 通过剪裁去除顶部边线
      const topLineClip = document.createElementNS(SVGNS, 'clipPath');

      topLineClip.setAttribute('id', `pathClip-${node.tag.id}`);
      const clipRect = document.createElementNS(SVGNS, 'rect');

      clipRect.setAttribute('x', '-1');
      clipRect.setAttribute('y', '1');
      clipRect.setAttribute('width', String(width + 2));
      clipRect.setAttribute('height', String(publicSize.height));
      topLineClip.appendChild(clipRect);

      publicStatusElRect.setAttribute('fill', colorBlue2);
      publicStatusElRect.setAttribute('d', createPathDataWithBottomRadius(0, 0, width, publicSize.height));
      publicStatusElRect.setAttribute('stroke', colorBlue5);

      publicStatusElRect.setAttribute('clip-path', `url(${`#pathClip-${node.tag.id}`})`);
      SvgVisual.setTranslate(publicStatusElG, 0, node.layout.height - publicSize.height);

      publicStatusElG.appendChild(publicStatusElRect);
      /** 处理文案 */
      _.forEach(node.tag.publicStatusTextList, (text, index) => {
        const publicStatusElText = document.createElementNS(SVGNS, 'text');
        addStatus(text, publicStatusElText);
        publicStatusElText.setAttribute('text-anchor', 'middle');
        publicStatusElText.setAttribute('fill', colorBlue5);
        SvgVisual.setTranslate(
          publicStatusElText,
          width / 2 - (node.tag?.iconText && index === node.tag.publicStatusTextList.length - 1 ? 7.5 : 0),
          8 + index * 16
        );
        publicStatusElG.appendChild(publicStatusElText);
      });

      /** 处理科创港标签 */
      if (node.tag?.iconText) {
        const publicStatusIconElG = document.createElementNS(SVGNS, 'g');
        const leftGap = width / 2 - node.tag.publicStatusGWidth[node.tag.publicStatusGWidth.length - 1] / 2 - 15;
        SvgVisual.setTranslate(
          publicStatusIconElG,
          leftGap + node.tag.publicStatusGWidth[node.tag.publicStatusGWidth.length - 1] - 7.5,
          publicSize.height - iconSize - 4
        );

        const publicStatusIconElRect = document.createElementNS(SVGNS, 'rect');
        publicStatusIconElRect.setAttribute('fill', colorBlue3);
        publicStatusIconElRect.setAttribute('width', iconSize.toString());
        publicStatusIconElRect.setAttribute('height', iconSize.toString());
        publicStatusIconElRect.setAttribute('rx', '2');
        publicStatusIconElRect.setAttribute('ry', '2');
        publicStatusIconElG.appendChild(publicStatusIconElRect);

        const publicStatusIconElText = document.createElementNS(SVGNS, 'text');
        addStatus(node.tag.iconText, publicStatusIconElText);
        publicStatusIconElText.setAttribute('text-anchor', 'middle');
        publicStatusIconElText.setAttribute('fill', colorNormal10);
        SvgVisual.setTranslate(publicStatusIconElText, 7.5, 2);
        publicStatusIconElG.appendChild(publicStatusIconElText);

        publicStatusElG.appendChild(publicStatusIconElG);
      }

      publicStatusElG.appendChild(topLineClip);
      g.appendChild(publicStatusElG);
    }

    const topRightTag = node.tag?.areaTag?.name || node.tag?.tags?.find((t) => t.type === 'top-right')?.name || '';
    if (topRightTag) {
      const name = node.tag?.areaTag?.name || node.tag.tags[0].name;
      const rectEl = document.createElementNS(SVGNS, 'rect');
      const textEl = document.createElementNS(SVGNS, 'text');

      const tagG = document.createElementNS(SVGNS, 'g');

      textEl.textContent = name;

      textEl.setAttribute('font-size', '10');
      textEl.setAttribute('fill', colorBlue5);

      rectEl.setAttribute('fill', colorBlue2);
      rectEl.setAttribute('width', (this.tagSize.width + hGap).toString());
      rectEl.setAttribute('height', (this.tagSize.height + vGap).toString());
      rectEl.setAttribute('rx', '2');
      rectEl.setAttribute('ry', '2');

      tagG.appendChild(rectEl);
      tagG.appendChild(textEl);
      SvgVisual.setTranslate(tagG, width - this.tagSize.width - hGap - 4, -this.tagHeightWithGapPartTwoOfThree + this.tipHeightWithGap);
      SvgVisual.setTranslate(textEl, hGap / 2, this.tagSize.height);
      g.appendChild(tagG);
    }

    const nameG = document.createElementNS(SVGNS, 'g');
    const nameText = document.createElementNS(SVGNS, 'text');

    nameText.setAttribute('text-anchor', 'middle');
    nameText.setAttribute('fill', colorNormal1);

    addText(node.tag.name, nameText);
    let translateY = (height - this.tipHeightWithGap - labelSize.height) / 2;
    if (node.tag.publicStatus) {
      translateY = (height - this.tipHeightWithGap - labelSize.height - publicSize.height) / 2;
    }
    SvgVisual.setTranslate(nameText, width / 2, this.tipHeightWithGap + translateY);

    nameG.appendChild(nameText);

    g.appendChild(nameG);

    SvgVisual.setTranslate(g, x, y);

    return new SvgVisual(g);
  }

  getOutline(node) {
    const { x, y, width, height } = node.layout;
    const tagWidth = this.tagSize.width + hGap;
    const path = new GeneralPath();
    path.moveTo(x, y); // 左上
    // 4 为tag距离举行右边的距离
    path.lineTo(x + width - tagWidth - 4, y); // tag左中，
    path.lineTo(x + width - tagWidth - 4, y - this.tagHeightWithGapPartTwoOfThree); // tag 左上
    path.lineTo(x + width - 4, y - this.tagHeightWithGapPartTwoOfThree); // tag右上
    path.lineTo(x + width - 4, y); // tag 右中
    path.lineTo(x + width, y); // 右上
    path.lineTo(x + width, y + height); // 右下
    path.lineTo(x, y + height); // 左下
    path.close(); // 闭合
    return path;
  }

  updateVisual(context, oldVisual, node) {
    const tag = node.tag;

    let highlightedColor = colorBlue05;
    if (tag.isHighlighted) {
      highlightedColor = colorBlue2;
    }

    // 更新pathElement的背景色
    const pathElement = oldVisual.svgElement.firstChild;
    pathElement.setAttribute('fill', highlightedColor);
    const g = oldVisual.svgElement;
    g.setAttribute('class', this.cssClass);
    g.setAttribute('transform', `translate(${node.layout.x}, ${node.layout.y})`); // 更新节点位置
    return SvgVisual.from(g); // 返回新的SvgVisual以更新节点的可视化内容
  }

  clone() {
    return this;
  }
}

/**
 * Creates the path data for the SVG path element.
 */
export const createPathData = (x, y, width, height) => {
  const radius = 2;
  // return `M ${x} ${y} h ${width} v ${height} h ${0 - width} z`
  return `M ${x + radius} ${y}
  h ${width - 2 * radius}
  a ${radius} ${radius} 0 0 1 ${radius} ${radius}
  v ${height - 2 * radius}
  a ${radius} ${radius} 0 0 1 ${0 - radius} ${radius}
  h ${0 - (width - 2 * radius)}
  a ${radius} ${radius} 0 0 1 ${0 - radius} ${0 - radius}
  v ${0 - (height - 2 * radius)}
  a ${radius} ${radius} 0 0 1 ${radius} ${0 - radius}
  z`;
};

export const createPathDataWithBottomRadius = (x, y, width, height) => {
  const borderRadius = 2;
  return `M ${x + 2 * borderRadius} ${y}
          h ${width - 2 * borderRadius}
          v ${height - borderRadius}
          a ${borderRadius} ${borderRadius} 0 0 1 -${borderRadius} ${borderRadius}
          h ${-(width - 2 * borderRadius)}
          a ${borderRadius} ${borderRadius} 0 0 1 -${borderRadius} -${borderRadius}
          v -${height - borderRadius}
          z`;
};

function addStatus(publicStatus, targetElement) {
  TextRenderSupport.addText({
    targetElement,
    text: publicStatus,
    maximumSize: new Size(companyNodeSize.width, 22),
    font: new Font({
      fontSize: 10,
    }),
    wrapping: TextWrapping.NONE,
    measurePolicy: TextMeasurePolicy.AUTOMATIC,
  });
}
