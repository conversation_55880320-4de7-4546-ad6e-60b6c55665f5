import { Font, LabelStyleBase, Point, Rect, Size, SvgVisual, TextRenderSupport } from 'yfiles';
import ImgTip from '@/assets/images/tip-icon.png';

const font = new Font({
  fontFamily: 'Arial',
  fontSize: 12,
});

const padding = 3;
const iconSize = 16;
const color_blue_5 = '#128bed';

export class EdgeLabelStyle extends LabelStyleBase {
  tips;

  cssClass = '';

  constructor({ tips }) {
    super();
    this.tips = tips;
  }

  /**
   * @param {!IRenderContext} context
   * @param {!ILabel} label
   * @returns {!CustomLabelStyleVisual}
   */
  createVisual(context, label) {
    // create an SVG text element that displays the label text
    const rectBg = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    rectBg.setAttribute('fill', '#FFF');
    const textElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    textElement.setAttribute('fill', color_blue_5);
    font.applyTo(textElement);

    const labelSize = label.layout.toSize();
    const { textSize, translateX, translateY } = this.updateText(textElement, label.text, labelSize);
    rectBg.setAttribute('width', String(textSize.width));
    rectBg.setAttribute('height', String(textSize.height));
    rectBg.setAttribute('transform', `translate(${translateX} ${translateY})`);

    const tips = this.tips;

    let imageElement;

    if (tips) {
      imageElement = document.createElementNS('http://www.w3.org/2000/svg', 'image');
      imageElement.setAttribute('href', ImgTip);

      imageElement.setAttribute('width', String(iconSize));
      imageElement.setAttribute('height', String(iconSize));
      const translateX = labelSize.width + padding / 3;
      imageElement.setAttribute('transform', `translate(${translateX} ${padding})`);
    }

    const gElement = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    gElement.setAttribute('class', this.cssClass);
    gElement.appendChild(rectBg);

    gElement.appendChild(textElement);
    if (imageElement) {
      gElement.appendChild(imageElement);
    }

    // move text to label location
    const transform = LabelStyleBase.createLayoutTransform(context, label.layout, true);
    transform.applyTo(gElement);

    return SvgVisual.from(gElement);
  }

  /**
   * @param {!IRenderContext} context
   * @param {!CustomLabelStyleVisual} oldVisual
   * @param {!ILabel} label
   * @returns {!CustomLabelStyleVisual}
   */
  updateVisual(context, oldVisual, label) {
    const gElement = oldVisual.svgElement;
    const transform = LabelStyleBase.createLayoutTransform(context, label.layout, true);
    gElement.setAttribute('class', this.cssClass);
    transform.applyTo(gElement);

    return oldVisual;
  }

  /**
   * Updates the text content of the text element using TextRenderSupport.
   *
   * @param {!SVGTextElement} textElement
   * @param {!string} text
   * @param {!Size} labelSize
   */
  updateText(textElement, text, labelSize) {
    // use a convenience method to place text content in the <text> element.
    const textContent = TextRenderSupport.addText(textElement, text, font);
    // calculate the size of the text element
    const textSize = TextRenderSupport.measureText(textContent, font);
    // calculate vertical offset for centered alignment
    const translateY = (labelSize.height - textSize.height) * 0.5;

    textElement.setAttribute('transform', `translate(${padding} ${translateY})`);
    return { textSize, translateX: padding, translateY };
  }

  /**
   * @param {!IInputModeContext} context
   * @param {!Point} location
   * @param {!ILabel} label
   * @returns {boolean}
   */
  isHit(context, location, label) {
    const labelLayout = label.layout;

    const layoutTransform = LabelStyleBase.createLayoutTransform(context.canvasComponent.createRenderContext(), labelLayout, true);
    layoutTransform.invert();
    // transform the location and subtract the tail position
    const transformedLocation = layoutTransform.transform(location).subtract(new Point(labelLayout.width, labelLayout.height / 2));

    // check the rectangular tail bounds
    const tailBounds = new Rect(0, 0, iconSize, iconSize);
    if (!tailBounds.containsWithEps(transformedLocation, context.hitTestRadius)) {
      return false;
    }

    // the location is inside the tail bounds - check if it's inside the triangle
    const tailHeightAtLocationX = iconSize * ((iconSize - transformedLocation.x) / iconSize);
    const tailHit = transformedLocation.y <= tailHeightAtLocationX + context.hitTestRadius;
    return tailHit;
  }

  /**
   * @param {!ILabel} label
   * @returns {!Size}
   */
  getPreferredSize(label) {
    let size = new Size(padding, padding);
    if (label.text && label.text.length > 0) {
      // measure the label text using the font
      const { width, height } = TextRenderSupport.measureText(label.text, font);
      size = new Size(width + padding + padding, height + padding + padding);
    }
    // return the measured size plus a small padding
    if (label.tag?.iconUrl) {
      size = new Size(size.width + iconSize + padding, Math.max(size.height, iconSize + padding + padding));
    }
    return size;
  }
}
