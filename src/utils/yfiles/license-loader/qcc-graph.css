/*
 * Created by <PERSON> on - 2024/01/05.
 */

.qcc-graph-canvascomponent {
  position: relative;
}

.qcc-graph-svgpanel {
  /*
    For exact label size measurement, use the below property
    to emphasize geometric precision over legibility and rendering speed.
  */

  /* text-rendering: geometricPrecision; */
}

.qcc-graph-resize-sensor .qcc-graph-resize-sensor-expand {
  animation-duration: 0.001s;
  animation-name: qcc-graph-dom-sensor-inserted;
}

@keyframes qcc-graph-dom-sensor-inserted {
  0% {
    opacity: 0.99;
  }

  100% {
    opacity: 1;
  }
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar {
  direction: ltr;
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar.qcc-graph-scrollbar-vertical {
  background: #eee;
  width: 15px;
  display: none;
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar.qcc-graph-scrollbar-horizontal {
  background: #eee;
  height: 15px;
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar.qcc-graph-scrollbar-vertical div {
  width: 15px;
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar.qcc-graph-scrollbar-horizontal div {
  height: 15px;
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar .qcc-graph-button.qcc-graph-button-left {
  width: 15px;
  background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2215%22%20height%3D%2215%22%3E%3Cpath%20d%3D%22M5%207.5%20L10%204%20L10%2011%20Z%22/%3E%3C/svg%3E");
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar .qcc-graph-button.qcc-graph-button-right {
  width: 15px;
  background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2215%22%20height%3D%2215%22%3E%3Cpath%20d%3D%22M5%204%20L10%207.5%20L5%2011%20Z%22/%3E%3C/svg%3E");
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar .qcc-graph-button.qcc-graph-button-up {
  height: 15px;
  background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2215%22%20height%3D%2215%22%3E%3Cpath%20d%3D%22M4%2010%20L7.5%205%20L11%2010%20Z%22/%3E%3C/svg%3E");
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar .qcc-graph-button.qcc-graph-button-down {
  height: 15px;
  background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2215%22%20height%3D%2215%22%3E%3Cpath%20d%3D%22M4%205%20L11%205%20L7.5%2010%20Z%22/%3E%3C/svg%3E");
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar
  .qcc-graph-button:not(.qcc-graph-button-disabled):hover {
  background-color: #bbb;
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar .qcc-graph-button.qcc-graph-button-disabled {
  opacity: 0.3;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar-range.qcc-graph-scrollbar-range-vertical
  .qcc-graph-scrollbar-slider {
  border: none;
  background: #ccc;
  border-radius: 0;
  width: 15px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar-range.qcc-graph-scrollbar-range-horizontal
  .qcc-graph-scrollbar-slider {
  border: none;
  background: #ccc;
  border-radius: 0;
  height: 15px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar-range.qcc-graph-scrollbar-range-horizontal
  .qcc-graph-scrollbar-slider:hover,
.qcc-graph-canvascomponent
  .qcc-graph-scrollbar-range.qcc-graph-scrollbar-range-vertical
  .qcc-graph-scrollbar-slider:hover {
  background: #bbb;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar-range.qcc-graph-scrollbar-range-horizontal
  .qcc-graph-scrollbar-slider
  .qcc-graph-scrollbar-slider-dragging,
.qcc-graph-canvascomponent
  .qcc-graph-scrollbar-range.qcc-graph-scrollbar-range-vertical
  .qcc-graph-scrollbar-slider
  .qcc-graph-scrollbar-slider-dragging {
  background: #9b9b9b;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar
  .qcc-graph-button:not(.qcc-graph-button-disabled).qcc-graph-scrollbar-button-down {
  background-color: #9b9b9b;
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar-content {
  cursor: default;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar
  .qcc-graph-scrollbar-range.qcc-graph-scrollbar-range-horizontal {
  left: 15px;
  right: 15px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar
  .qcc-graph-scrollbar-range.qcc-graph-scrollbar-range-vertical {
  top: 15px;
  bottom: 15px;
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar .qcc-graph-button {
  color: #000;
  font-size: 10px;
  line-height: 15px;
  text-align: center;
  vertical-align: middle;
  border: none;
  border-radius: 0;
  background-position-x: center;
  background-position-y: center;
  background-size: contain;
  background-repeat: no-repeat;
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic {
  opacity: 1;
  background: transparent;
  border-radius: 6px;
}

.qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic:hover,
.qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic:active {
  background: rgba(238, 238, 238, 0.85);
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic.qcc-graph-scrollbar-vertical {
  width: 12px;
  bottom: 0;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic.qcc-graph-scrollbar-vertical.qcc-graph-scrollbar-inset {
  bottom: 12px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic.qcc-graph-scrollbar-vertical
  .qcc-graph-scrollbar-range-vertical {
  top: 12px;
  bottom: 12px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic.qcc-graph-scrollbar-vertical
  .qcc-graph-scrollbar-slider {
  right: 2px;
  width: 8px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic.qcc-graph-scrollbar-horizontal {
  height: 12px;
  right: 0;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic.qcc-graph-scrollbar-horizontal.qcc-graph-scrollbar-inset {
  right: 12px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic.qcc-graph-scrollbar-horizontal
  .qcc-graph-scrollbar-range-horizontal {
  left: 12px;
  right: 12px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic.qcc-graph-scrollbar-horizontal
  .qcc-graph-scrollbar-slider {
  height: 8px;
  bottom: 2px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic
  .qcc-graph-scrollbar-slider {
  border-radius: 4px;
  background: #666;
}

.qcc-graph-canvascomponent .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic .qcc-graph-button {
  opacity: 0.6;
  background-size: 16px;
  width: 12px;
  height: 12px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic.qcc-graph-scrollbar-inactive:not(:hover, :active) {
  opacity: 0;
  transition: opacity 0.3s ease-in 0.7s, width 0.3s ease-in 0.7s, background 0.2s ease-in 0.7s;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic:not(:hover, :active) {
  transition: background 0.3s ease-in 1s;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic:not(:hover, :active)
  .qcc-graph-button {
  opacity: 0;
  transition: opacity 0.3s ease-in 1s;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic:not(:hover, :active)
  .qcc-graph-scrollbar-slider {
  transition-duration: 0.3s;
  transition-timing-function: ease-in;
  transition-delay: 1s;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic:not(:hover, :active).qcc-graph-scrollbar-vertical
  .qcc-graph-scrollbar-slider {
  transition-property: width, background, right;
  width: 4px;
}

.qcc-graph-canvascomponent
  .qcc-graph-scrollbar.qcc-graph-scrollbar-dynamic:not(:hover, :active).qcc-graph-scrollbar-horizontal
  .qcc-graph-scrollbar-slider {
  transition-property: height, background, bottom;
  height: 4px;
}

/* Tooltip styling and transition */
.qcc-graph-tooltip {
  font-size: 10pt;
  background-color: #ffffd0;
  border: 1px solid black;
  padding: 2px;
  overflow: visible;
  z-index: 1070;
  box-sizing: content-box;
  line-height: 1;
}

.qcc-graph-tooltip-entering {
  transition: opacity 0.2s ease-in;
}

.qcc-graph-tooltip-enter {
  opacity: 0;
}

.qcc-graph-tooltip-enter-to {
  opacity: 1;
}

.qcc-graph-tooltip-leaving {
  transition: opacity 0.2s ease-out;
}

.qcc-graph-tooltip-leave {
  opacity: 1;
}

.qcc-graph-tooltip-leave-to {
  opacity: 0;
}

/* Misc styling */
.qcc-graph-canvascomponent .qcc-graph-labeleditbox-container {
  border: 1px solid black;
  background-color: white;
  padding: 2px;
}

.qcc-graph-labeleditbox-container-enter {
  opacity: 0;
}

.qcc-graph-labeleditbox-container-enter-to {
  opacity: 1;
}

.qcc-graph-labeleditbox-container-entering {
  transition: opacity 0.1s ease-in;
}

.qcc-graph-labeleditbox-container-leave {
  opacity: 1;
}

.qcc-graph-labeleditbox-container-leave-to {
  opacity: 0;
}

.qcc-graph-labeleditbox-container-leaving {
  transition: opacity 0.1s ease-out;
}

.qcc-graph-canvascomponent .qcc-graph-labeleditbox {
  background-color: transparent;
  border: 0 none;
  padding: 0;
  font: normal normal 400 normal 10pt normal sans-serif;
  text-decoration: none;
  text-transform: none;
  letter-spacing: normal;
  word-spacing: 0;
}

.qcc-graph-collapsebutton {
  cursor: pointer;
}

.qcc-graph-resize-sensor ::-webkit-scrollbar {
  -webkit-appearance: none;
}
