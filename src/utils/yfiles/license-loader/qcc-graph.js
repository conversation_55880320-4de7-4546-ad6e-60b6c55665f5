/*
 * Created by <PERSON> on - 2024/01/05.
 */

import './qcc-graph.css';

import _ from 'lodash';

let started = false;

export default () => {
  if (started) return;

  const func = () => {
    try {
      const eles = $('[class*="yfiles"]');
      for (const ele of eles) {
        const $ele = $(ele);
        const classes = _.chain($ele.attr('class') || '')
          .split(' ')
          .filter((o) => _.startsWith(o, 'yfiles'))
          .value();

        for (const c of classes) {
          $ele.removeClass(c).addClass(c.replace('yfiles-', 'qcc-graph-'));
        }
      }
    } catch (err) {}
  };

  started = true;
  func();

  setInterval(func, 500);
};
