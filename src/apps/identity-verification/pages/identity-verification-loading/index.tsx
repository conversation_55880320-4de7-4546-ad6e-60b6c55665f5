import { computed, defineComponent, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

import HeroicLayout from '@/shared/layouts/heroic';
import { useRoomSocket } from '@/hooks/use-room-socket/use-room-socket';

import styles from './identity-verification-loading.module.less';
import ErrorStatus from './widgets/error-status';
import LoadingStatus from './widgets/loading-status';
import IdentityVerificationBreadcrumb from '../../components/identity-verification-breadcrumb';

enum BatchStatusEnums {
  /** 待处理 */
  Waiting = 0,
  /** 处理中 */
  Processing = 1,
  /** 处理完成 */
  Done = 2,
  /** 批量任务超时了(仍然被标记成功了，可以重试) */
  DoneTimeout = 21, // 批量任务超时了(仍然被标记成功了，可以重试)
  /** 处理失败 */
  Error = 3,
  /** 队列中排队 */
  Queuing = 4, // 队列中排队
  /** 套餐失效被冻结 */
  Suspended = 5, // 套餐失效被冻结
  /** 工商信息无效，需要人工确认 */
  invaild = 6, // 工商信息无效，需要人工确认
  /** 已中止取消 */
  Caneled = 7,
}

const IdentityVerificationLoading = defineComponent({
  name: 'IdentityVerificationLoading',
  props: {
    batchId: {
      type: Number,
    },
    type: {
      type: Number,
    },
  },
  setup(props) {
    const route = useRoute();
    const router = useRouter();
    // 模拟状态切换，实际使用时可以根据接口返回状态决定
    const status = ref('loading');
    const operationInfo = computed(() => {
      return route.query.creator ? JSON.parse(route.query.creator.toString()) : {};
    });

    const fetchData = (incomingItem) => {
      if (incomingItem.batchId !== props.batchId) return;
      if (incomingItem.batchStatus === BatchStatusEnums.Done) {
        router.push({
          name: 'identity-verification-detail',
          params: {
            pageType: 'start',
            recordId: incomingItem.batchId,
          },
          query: {
            type: 'batch',
          },
        });
        return;
      }
      if ([BatchStatusEnums.DoneTimeout, BatchStatusEnums.Error].includes(incomingItem.batchStatus)) {
        status.value = 'error';
      }
    };

    useRoomSocket('/insights/socket', {
      eventType: 'SystemMessage',
      filter: (messageData: any) => {
        return messageData.batchId === props.batchId;
      },
      refresh: fetchData,
    });

    return {
      status,
      operationInfo,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        {/* 面包屑导航 */}
        <IdentityVerificationBreadcrumb pageType={this.$route.params.type} operationInfo={this.operationInfo} />
        <HeroicLayout
          loading={false}
          innerStyle={{
            minHeight: 'calc(100vh - 52px - 60px)',
          }}
          align="center"
        >
          {this.status === 'loading' ? (
            <LoadingStatus>
              核验结果正在生成中，你可以在当前页等待，也可以稍后通过右上角
              <router-link to="/tasklist/identity-verification">"任务列表"</router-link>
              查看该结果
            </LoadingStatus>
          ) : (
            <ErrorStatus showRefreshButton={false}>服务器出错了，请稍候重试</ErrorStatus>
          )}
        </HeroicLayout>
      </div>
    );
  },
});

export default IdentityVerificationLoading;
