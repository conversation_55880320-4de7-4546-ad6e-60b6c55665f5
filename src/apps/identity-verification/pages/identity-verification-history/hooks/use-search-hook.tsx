import { computed, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { cloneDeep } from 'lodash';

import { useCacheQuery } from '@/hooks/use-cache-query';
import { stateToQuery } from '@/apps/company/pages/company-search/utils/state-to-query';
import { useFetchState } from '@/hooks/use-fetch-state';

import { getFilterConfig } from '../config';

// 是否使用上次缓存的查询条件，不使用就清除上次缓存
const DEFAULT_SEARCH_CONDITIONS = Object.freeze({
  query: {
    keywords: undefined,
    filters: {}, // 通过URL获取日期字段: `sd`
  },
  pagination: { pageSize: 10, pageIndex: 1, total: 0 },
  sort: {},
});
export const useSearchHook = (searchUrl) => {
  const route = useRoute();
  const router = useRouter();
  const operatorList = ref<{ userId: number; name: string }[]>([]);
  const { cached } = useCacheQuery(
    // Namespace
    'identity-verification-history',
    // Defaults
    DEFAULT_SEARCH_CONDITIONS,
    // Deps
    { route, router }
  );

  const filterParams = computed(() => {
    const { keywords, filters } = cached.query.value;
    const filter = stateToQuery({
      keyword: keywords,
      filters,
    });
    return {
      searchKey: filter?.searchKey,
      createDate: filter.filter?.sd ? [filter.filter?.sd] : undefined,
      ...(filter.filter || {}),
      operatorIds: filters?.t,
      ...cached.sort.value,
      needAgg: [1],
      companyTypeList: [1],
    };
  });

  const pagination = computed(() => ({
    current: cached.pagination.value.pageIndex,
    pageSize: cached.pagination.value.pageSize,
    total: cached.pagination.value.total,
  }));

  const handleFilterChange = (payload) => {
    cached.pagination.value.pageIndex = 1;
    cached.query.value = payload;
  };

  const handleSortChange = (payload) => {
    cached.pagination.value.pageIndex = 1;
    cached.sort.value = payload;
  };

  const filterStash = ref(false);
  const aggStash = ref({} as any);

  const getGroupOptions = computed(() => {
    const { verificationResult = [], creator = [], verificationType = [] } = aggStash.value;
    return {
      resultOptions: verificationResult.map((item) => ({
        label: item.verificationResult === 1 ? '匹配' : '不匹配',
        value: item.verificationResult,
        count: +item.count,
      })),
      operatorOptions: creator.map((cr) => {
        const match = operatorList.value.find((item) => item.userId === cr.creator);
        return {
          label: match?.name || '-',
          value: cr.creator,
          count: +cr.count,
        };
      }),
      recordVerificationResultOptions: verificationType
        .filter((item) => [1, 2].includes(item.verificationType))
        ?.map((item) => ({
          label: item.verificationType === 1 ? '常规核验' : '深度核验',
          value: item.verificationType,
          count: +item.count,
        })),
    };
  });

  const filterGroups = computed(() => getFilterConfig(getGroupOptions.value));
  const fetchData = (params = {}) => {
    return searchUrl({
      pageIndex: cached.pagination.value.pageIndex,
      pageSize: cached.pagination.value.pageSize,
      ...filterParams.value,
      ...params,
    }).then((res) => {
      cached.pagination.value.pageIndex = res.pageIndex;
      cached.pagination.value.pageSize = res.pageSize;
      cached.pagination.value.total = res.total;
      aggStash.value = res.aggsRes || {};
      return res;
    });
  };
  const { execute, result, isLoading } = useFetchState(fetchData);
  const dataSource = computed<any[]>(() => (result.value as any)?.data || []);

  watch(
    () => filterParams.value,
    () => {
      execute();
    },
    { immediate: true, deep: true }
  );

  return {
    dataSource,
    operatorList,
    pagination,
    isLoading,
    filterGroups,
    filterParams,
    filters: cached.query,
    handleFilterChange,
    handleSortChange,
    execute,
  };
};
