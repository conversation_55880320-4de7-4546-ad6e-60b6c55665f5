import { computed, defineComponent, PropType, unref } from 'vue';
import { Button, Divider, message, Popconfirm } from 'ant-design-vue';
import { useRouter } from 'vue-router/composables';
import { escape } from 'lodash';

import QCard from '@/components/global/q-card';
import SearchCount from '@/components/search-count';
import EmptyWrapper from '@/shared/components/empty-wrapper';
import QRichTable from '@/components/global/q-rich-table';
import { openGroupModal } from '@/shared/components/add-group';
import { monitor } from '@/shared/services';
import AddCompany from '@/components/modal/supplier/add-company-modal';
import { Permission } from '@/config/permissions.config';

import styles from './search-result.module.less';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    pagination: {
      type: Object as PropType<{ current: number; pageSize: number; total: number }>,
      required: true,
    },
    isLimited: {
      type: Boolean,
      default: false,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    searchKey: {
      type: String,
      default: undefined,
    },
    operatorList: {
      type: Array as PropType<{ userId: number; name: string }[]>,
      default: () => [],
    },
    // 分组数量上限
    limit: {
      type: Number,
      default: 50,
    },
  },
  setup(props, { emit }) {
    const router = useRouter();
    const handlePageChange = (current, pageSize) => {
      emit('changePage', current, pageSize);
    };

    const paginationProps = computed(() => ({
      ...props.pagination,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    }));

    const handleAddGroup = async (groupData?: any) => {
      const isEdit = !!groupData;
      const form: any = await openGroupModal({
        title: isEdit ? '编辑分组' : '添加分组',
        data: groupData || {},
        isEdit,
      });
      if (!form) {
        return;
      }
      const fn = isEdit ? monitor.updateGroup : monitor.addGroup;
      await fn({
        ...(groupData || {}),
        ...unref(form),
        groupName: unref(form).name.trim(),
      });
      message.success('操作成功！');
      emit('refresh');
    };

    const deleteGroup = async (groupInfo) => {
      const { monitorGroupId } = groupInfo;
      try {
        await monitor.removeGroup(monitorGroupId);
        message.success('分组删除成功！');
        emit('refresh');
      } catch (error) {
        console.log(error);
      }
    };

    const goTarget = (record) => {
      const urlObj = router.resolve({
        path: '/risk-monitor/targets',
        query: {
          monitorGroupId: record.monitorGroupId,
        },
      });
      window.open(urlObj.href);
    };
    return {
      paginationProps,
      handleAddGroup,
      deleteGroup,
      goTarget,
    };
  },
  render() {
    return (
      <QCard bodyStyle={{ padding: '15px' }}>
        <div slot="title">
          <SearchCount
            showSelects={false}
            total={this.pagination.total}
            loading={this.isLoading}
            scopedSlots={{
              message: (content) => {
                return <div class="flex items-center">共找到{content}个组别</div>;
              },
            }}
          />
        </div>
        <div slot="extra">
          <Button
            icon="plus-circle"
            type="primary"
            disabled={this.isLimited}
            v-disabletip={`当前分组数量已达上限${this.limit}个，无法继续添加`}
            onClick={() => this.handleAddGroup()}
          >
            添加分组
          </Button>
        </div>
        <EmptyWrapper dataSource={this.dataSource} loading={this.isLoading}>
          <QRichTable
            loading={this.isLoading}
            tableLayout="fixed"
            rowKey={this.rowKey}
            dataSource={this.dataSource}
            columns={this.columns}
            customScroll={{ x: 1046, y: 'calc(100vh - 288px)' }}
            pagination={this.paginationProps}
            onChange={({ sorter }) => {
              this.$emit('sorterChange', sorter);
            }}
            scopedSlots={{
              groupName: (name, record) => {
                const groupName = escape(name).replace(this.searchKey, `<em>${this.searchKey}</em>`);
                return <a class="emphasis" onClick={() => this.goTarget(record)} domPropsInnerHTML={groupName}></a>;
              },
              modelConfig: (record) => {
                const { monitorModelId, status, distributeStatus, riskModelEntity } = record;
                const isDeprecated = status === 4 || distributeStatus === 3;
                return [
                  <a href={`/app/risk-monitor/models/detail/${monitorModelId}`} target="_blank">
                    {riskModelEntity?.modelName || '未知模型'}
                  </a>,
                  isDeprecated && <span style={{ color: '#666', marginLeft: '5px' }}>（已废弃）</span>,
                ];
              },
              monitorStatus: (enabled) => {
                const statusMap = {
                  on: '开启',
                  off: '关闭',
                };
                const status = enabled === 1 ? 'on' : 'off';
                return (
                  <div class="flex items-center">
                    <i class={[styles.dot, styles[status]]}></i>
                    <span>{statusMap[status]}</span>
                  </div>
                );
              },
              operatorName: (id) => {
                if (!this.operatorList?.length) {
                  return '-';
                }
                return this.operatorList.find((operator) => operator.userId === id)?.name || '-';
              },
              operation: (record) => {
                const isDefaultGroup = record.groupType === 1;
                return (
                  <div class={styles.btnGroup}>
                    <Button type="link" onClick={() => this.handleAddGroup(record)}>
                      编辑
                    </Button>

                    <Divider type="vertical" style={{ background: '#D8D8D8' }} />
                    <AddCompany
                      v-permission={[Permission.MONITOR_ENTERPRISE_ADD]}
                      monitorGroupId={record.monitorGroupId}
                      on={{
                        refresh: () => this.$emit('refresh', true),
                      }}
                    >
                      <Button type="link">添加企业</Button>
                    </AddCompany>
                    <Divider v-show={!isDefaultGroup} type="vertical" style={{ background: '#D8D8D8' }} />
                    <Popconfirm
                      v-show={!isDefaultGroup}
                      onText="确认"
                      overlayStyle={{ width: '250px' }}
                      cancelText="取消"
                      onConfirm={() => {
                        this.deleteGroup(record);
                      }}
                    >
                      <div slot="title">您确定要删除该分组么？ 删除分组会将组内企业及动态全部删除</div>
                      <Button type="link">删除</Button>
                    </Popconfirm>
                  </div>
                );
              },
            }}
          />
        </EmptyWrapper>
      </QCard>
    );
  },
});

export default SearchResult;
