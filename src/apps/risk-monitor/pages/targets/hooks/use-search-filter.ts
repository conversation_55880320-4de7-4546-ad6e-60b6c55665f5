import { cloneDeep, sortBy } from 'lodash';
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

import { useRequest } from '@/shared/composables/use-request';
import { monitor as monitorService, user as userService } from '@/shared/services';
import { RISK_LEVEL } from '@/config/risk.config';
import { DEFAULT_DATE_RANGE, DEFAULT_DATE_RANGE_LIFE, REGISTERED_CAPITAL_RANGE } from '@/config/tender.config';

export const useSearchFilter = () => {
  const route = useRoute();

  // 获取分组数据和操作人数据
  const monitorOperators = useRequest(userService.getUserList);
  const monitorAggsSearch = useRequest(monitorService.searchCompanies);
  const monitorGroups = useRequest(monitorService.getAllGroups);

  const isInit = ref(true);

  const operators = computed(() => {
    return monitorOperators.data.value?.map((item) => ({
      value: item.userId,
      label: item.name,
    }));
  });

  const monitorAggsResMap = ref<any>({
    creator: [],
    monitorGroup: [],
    riskLevel: [],
    relatedExist: [],
  });

  const groupList = computed(() => {
    const initialAggs = monitorAggsSearch.data?.value?.aggsRes ?? {};
    const allGroups = monitorGroups.data.value?.data || [];
    return sortBy(
      initialAggs.monitorGroup?.map((item) => {
        const group = allGroups.find((v) => v.monitorGroupId === item.monitorGroupId) || {};
        return {
          monitorGroupId: item.monitorGroupId,
          name: group?.name,
          count: +item.count,
          label: group?.name,
          value: item.monitorGroupId,
        };
      }) ?? [],
      (item) => {
        return item.name === '默认分组';
      }
    ).reverse();
  });
  const operatorList = ref<any[]>([]);
  const companyStatusList = ref<any[]>([]);

  const defaultFilterValues = Object.freeze({
    keywords: undefined,
    filters: {},
  });

  const isFilterLoading = ref(false);

  // 上次搜索的参数
  const previewFilterValues = ref<any>({});

  const defaultGroupId = computed(() => {
    return groupList.value.find((item) => item.label === '默认分组')?.monitorGroupId;
  });
  const getFilterOptions = (config?, showAll = false) => {
    if (showAll) {
      isFilterLoading.value = true;
      return;
    }
    operatorList.value =
      monitorAggsResMap.value?.createBy
        ?.map((item) => {
          const hitGroup = monitorOperators.data.value?.find((op) => op.userId === item.creator);
          if (hitGroup) {
            return {
              value: item.creator,
              label: hitGroup?.name ?? '未知',
              // count: +item.count,
            };
          }
          return false;
        })
        .filter(Boolean) ?? [];
    companyStatusList.value =
      sortBy(
        monitorAggsResMap.value?.riskLevel?.map((item) => {
          const hitValue = RISK_LEVEL.find((level) => level.value === item.riskLevel);
          if (hitValue) {
            return {
              value: item.riskLevel,
              label: RISK_LEVEL.find((level) => level.value === item.riskLevel)?.label ?? '-',
              // count1: +item.count,
            };
          }
          return false;
        }),
        'value'
      ).filter(Boolean) ?? [];
  };

  /** 搜索过滤配置 */
  const filterGroups = computed(() => {
    return [
      {
        field: 'groupIds',
        type: 'button',
        label: '所属分组',
        options: groupList.value,
        meta: {
          maxLength: 15,
          defaultButton: false,
        },
      },
      {
        field: 'filters',
        label: '筛选条件',
        type: 'groups',
        children: [
          {
            field: 'shortStatus',
            type: 'multiple',
            label: '登记状态',
            options: companyStatusList.value,
            meta: {
              showFooterButton: true,
            },
          },
          // TODO 这个迭代没有
          {
            field: 'enterpriseType',
            type: 'multiple',
            label: '主体类型',
            options: [],
            meta: {
              showFooterButton: true,
            },
          },
          // TODO 这个迭代没有
          {
            field: 'recordType',
            type: 'multiple',
            label: '备案类型',
            options: [],
            meta: {
              showFooterButton: true,
            },
          },
          {
            field: 'startDate',
            type: 'multiple',
            label: '成立年限',
            options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE_LIFE],
            meta: {
              showFooterButton: true,
            },
          },
          {
            field: 'registcapiAmount',
            type: 'multiple',
            label: '注册资本',
            options: REGISTERED_CAPITAL_RANGE,
            custom: {
              type: 'number-range',
              props: {
                unit: '万元',
              },
            },
            meta: {
              showFooterButton: true,
            },
          },
          {
            field: 'operatorIds',
            type: 'multiple',
            label: '操作人',
            options: operatorList.value,
            meta: {
              showFilter: true,
              showFooterButton: true,
            },
          },
          {
            field: 'createDate',
            type: 'single',
            label: '监控时间',
            options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
            custom: {
              type: 'date-range',
            },
          },
        ],
      },
    ];
  });

  /** groupId改变时，重置其他筛选项 */
  const resetOtherFilters = (remainedKeys: string[], currentFilters = {}) => {
    const filters: any = remainedKeys.reduce((acc, key) => {
      acc[key] = currentFilters[key];
      return acc;
    }, {});
    return {
      ...defaultFilterValues,
      filters,
    };
  };

  /** 搜索过滤值 */
  const filterValues = ref({
    ...defaultFilterValues,
  });

  /** 更新搜索过滤 */
  const handleFilterChange = (values) => {
    if (values.filters?.groupIds !== previewFilterValues.value.filters?.groupIds) {
      const groupIds = cloneDeep(values.filters?.groupIds);
      const resetOther = resetOtherFilters([], values);
      resetOther.filters.groupIds = groupIds;
      filterValues.value = {
        filters: { ...resetOther.filters },
        keywords: undefined,
      };
    } else {
      filterValues.value = values;
    }
    previewFilterValues.value = cloneDeep(filterValues.value);
  };

  /** 重置搜索过滤 */
  const handleFilterReset = () => {
    filterValues.value = { ...defaultFilterValues, filters: { groupIds: defaultGroupId.value } };
  };

  const initFilterValues = () => {
    const monitorGroupId = route.query.monitorGroupId || defaultGroupId.value;
    filterValues.value.filters = {
      ...filterValues.value.filters,
      groupIds: monitorGroupId ? +monitorGroupId : undefined,
    };
    previewFilterValues.value = cloneDeep(filterValues.value);
  };

  const getGroups = async () => {
    await monitorAggsSearch.execute<any>({ needAggs: 1 });
  };

  /** 初始化 */
  onMounted(async () => {
    try {
      await monitorOperators.execute<any>();
      await monitorGroups.execute<any>({ pageIndex: 1, pageSize: 100 });
      await getGroups();
      initFilterValues();
      isInit.value = false;
      getFilterOptions();
    } catch (error) {
      console.error(error);
    }
  });

  return {
    filterGroups,
    groupList,
    filterValues,
    handleFilterChange,
    handleFilterReset,
    monitorAggsResMap,
    operators,
    isFilterLoading,
    getFilterOptions,
    getGroups,
    isInit,
  };
};
