import { computed, defineComponent, ref } from 'vue';
import { Button, Icon } from 'ant-design-vue';
import { uniqBy, escape, differenceBy } from 'lodash';

import QRichTable from '@/components/global/q-rich-table';
import QCard from '@/components/global/q-card';
import DiligenceWarningPop from '@/components/diligence-warning-pop';
import CompanyLogo from '@/components/company-logo';
import QIcon from '@/components/global/q-icon';
import TrendsContent from '@/shared/components/trends-content';
import EmptyWrapper from '@/shared/components/empty-wrapper';

import styles from './search-result.module.less';
import TableFilterDropdown from '../table-filter-dropdown';
import OpenDetailButton from '../open-detail-button';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    pagination: {
      type: Object,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    selectedItems: {
      type: Array,
      default: () => [],
    },
    groupList: {
      type: Array,
      default: () => [],
    },
    metricNameList: {
      type: Array,
      default: () => [],
    },
    riskLevelList: {
      type: Array,
      default: () => [],
    },
    operatorList: {
      type: Array,
      default: () => [],
    },
    searchKey: {
      type: String,
      default: undefined,
    },
    // 是否是关联方内容
    isRelatedTrends: {
      type: Boolean,
      default: true,
    },
    emptyText: {
      type: String,
      required: false,
    },
    emptyMinHeight: {
      type: String,
      default: 'calc(100vh - 290px)',
    },
  },
  emits: ['conditionChange', 'changePage', 'selectItems', 'update', 'openCourtNotice'],
  setup(props, { emit }) {
    const selectedIds = ref<any[]>([]);
    const selectedRowsTotal = ref<any[]>([]);

    const rowSelection = computed(() => ({
      checkStrictly: false,
      selectedRowKeys: selectedIds.value,
      onChange: (selectedRowKeys, selectedRows) => {
        selectedRowsTotal.value = uniqBy(
          [...differenceBy(selectedRowsTotal.value, selectedRows, props.rowKey), ...selectedRows],
          props.rowKey
        );
        selectedIds.value = selectedRowsTotal.value.map((item) => item[props.rowKey]);
        emit('selectItems', selectedRows.value);
      },
    }));

    const handlePageChange = (current, pageSize) => {
      emit('changePage', current, pageSize);
    };

    const paginationProps = computed(() => ({
      ...props.pagination,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    }));

    return {
      selectedIds,
      rowSelection,

      paginationProps,
    };
  },
  render() {
    const { searchKey } = this;
    return (
      <QCard class={styles.container} bodyStyle={{ padding: '0' }} headerStyle={{ padding: '0' }}>
        <EmptyWrapper
          dataSource={this.dataSource}
          loading={this.isLoading}
          emptyMinHeight={this.emptyMinHeight}
          emptyText={this.emptyText}
          size={'80px'}
        >
          <QRichTable
            loading={this.isLoading}
            rowKey={this.rowKey}
            showIndex={false}
            tableLayout={'fixed'}
            dataSource={this.dataSource}
            columns={this.columns}
            pagination={this.paginationProps}
            rowSelection={this.isRelatedTrends ? this.rowSelection : undefined}
            customScroll={{ x: false, y: 'calc(100vh - 310px)' }}
            showExpanded={(record) => record.relatedCompanyList?.length > 0}
            onChange={({ filters, sorter }) => {
              this.$emit('conditionChange', { filters, sorter });
            }}
            scopedSlots={{
              expandedTitleRender: (item) => {
                const companyName = escape(item.companyName || item.name).replace(searchKey, `<em>${searchKey}</em>`);
                const { relatedCompanydynamics, relatedCompanyList } = item;
                return (
                  <div class="flex items-center" style={{ gap: '10px' }}>
                    <CompanyLogo
                      key={item.companyId}
                      src={item.ImageUrl}
                      id={item.companyId || ''}
                      name={item.ShortName || item.name}
                      hasimage={item.HasImage}
                    />
                    <div style={{ flex: 1 }} title={item.companyName}>
                      <a
                        data-testid="company-name"
                        href={`/embed/companyDetail?keyNo=${item.companyId}&title=${item.companyName}`}
                        target="_blank"
                        domPropsInnerHTML={companyName}
                      />
                      {relatedCompanyList?.length > 0 && this.isRelatedTrends ? (
                        <div class={styles.relatedTrends}>
                          关联方 {relatedCompanydynamics ? '+' : '-'}
                          <span>{relatedCompanyList?.length}</span>
                        </div>
                      ) : null}
                    </div>
                    <Button
                      v-show={this.isRelatedTrends}
                      type="link"
                      onClick={() => {
                        this.$router.push({
                          path: `targets/detail/${item.companyId}`,
                          query: {
                            from: 'targets', // 面包屑导航
                            name: this.$route.meta?.title,
                          },
                        });
                      }}
                    >
                      动态 <Icon type="right"></Icon>
                    </Button>
                  </div>
                );
              },
              MonitorResult: (val, item) => {
                // 风险等级
                return <DiligenceWarningPop score={val} rowData={item} isMonitor={true} />;
              },
              MonitorContent: (item) => {
                return <TrendsContent key={item.uniqueHashkey} record={item} />;
              },
              Action: (record) => {
                return <OpenDetailButton record={record} />;
              },
              filterDropdown: ({ column, confirm, clearFilters, setSelectedKeys, selectedKeys }) => {
                const { dataIndex } = column;
                const options = dataIndex === 'riskLevel' ? this.riskLevelList : this.metricNameList;
                return (
                  <TableFilterDropdown
                    options={options}
                    defaultValue={selectedKeys}
                    onUpdate={(value) => {
                      setSelectedKeys(value.length > 0 ? value : undefined);
                      if (!value?.length) {
                        clearFilters();
                        return;
                      }
                      confirm();
                    }}
                  />
                );
              },
              filterIcon: () => <QIcon style={{ fontSize: '14px !important', marginBottom: '1px' }} type="icon-shaixuanicon" />,
            }}
          ></QRichTable>
        </EmptyWrapper>
      </QCard>
    );
  },
});

export default SearchResult;
