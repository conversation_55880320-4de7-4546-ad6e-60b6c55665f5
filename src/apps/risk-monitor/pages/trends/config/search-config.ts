import { DEFAULT_DATE_RANGE } from '@/config/tender.config';

export const ChangeTypeMap = {
  1: '新增受益所有人',
  2: '减少受益所有人',
  3: '受益所有人信息变更',
};

export const ChangeTypeList = [
  { label: '新增受益所有人', value: 1 },
  { label: '减少受益所有人', value: 2 },
  { label: '受益所有人信息变更', value: 3 },
];

export const StandardMap = {
  1: '标准一',
  2: '标准二',
  3: '标准三',
};

export const StandardList = [
  { label: '标准一', value: 1 },
  { label: '标准二', value: 2 },
  { label: '标准三', value: 3 },
];

export const SEARCH_RESULT_TABLE_COLUMNS = [
  {
    title: '企业名称',
    scopedSlots: {
      customRender: 'MonitorCompany',
    },
  },
  {
    title: '变更类型',
    dataIndex: 'changeType',
    width: 150,
    customRender: (val) => {
      return ChangeTypeMap[val] || '-';
    },
  },

  {
    title: '变更标准',
    width: 120,
    dataIndex: 'standard',
    customRender: (val) => {
      return StandardMap[val] || '-';
    },
  },

  // 风险内容
  {
    title: '风险内容',
    scopedSlots: {
      customRender: 'MetricsContent',
    },
  },

  // 更新时间
  {
    title: '变更日期',
    width: 110,
    dataIndex: 'createDate',
    // sorter: true,
    scopedSlots: {
      customRender: 'date',
    },
  },
];

export const getSearchFilterConfig = ({ groupId, changeTypes, standards }: { groupId: any[]; changeTypes: any[]; standards: any[] }) => {
  return [
    {
      field: 'groupId',
      type: 'button',
      label: '所属分组',
      options: groupId,
      meta: {
        maxLength: 15,
        defaultButton: false,
      },
    },
    {
      field: 'filters',
      label: '筛选条件',
      type: 'groups',
      children: [
        {
          field: 'changeTypes',
          type: 'multiple',
          label: '变更类型',
          options: changeTypes,
          meta: {
            showFooterButton: true,
          },
        },
        {
          field: 'standards',
          type: 'multiple',
          label: '变更标准',
          options: standards,
          meta: {
            showFooterButton: true,
          },
        },
        {
          field: 'createDateList',
          type: 'single',
          label: '变更日期',
          options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
          custom: {
            type: 'date-range',
          },
        },
      ],
    },
  ];
};
