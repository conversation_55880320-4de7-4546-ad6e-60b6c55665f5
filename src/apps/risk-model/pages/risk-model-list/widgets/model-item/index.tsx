import { defineComponent, ref } from 'vue';
import { Popconfirm, Spin, message, Tooltip } from 'ant-design-vue';
import { useRouter } from 'vue-router/composables';

import RiskAction from '@/shared/components/risk-action';
import { setting } from '@/shared/services';
import QIcon from '@/components/global/q-icon';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import RiskModelStatusTag from '@/shared/components/risk-model-status-tag';
import QTag from '@/components/global/q-tag';
import { Permission } from '@/config/permissions.config';

import styles from './model-item.module.less';

type ActionMode = 'VIEW' | 'EDIT' | 'DEPRECATE' | 'PUBLISH';

const ModelItem = defineComponent({
  name: 'ModelItem',
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    /**
     * 是否为嵌入页面
     */
    isExternal: {
      type: Boolean,
      default: false,
    },
    modelType: {
      type: String,
    },
    /**
     * 是否展示dragger
     */
    showDragger: {
      type: Boolean,
      default: true,
    },
  },
  setup(props, { emit }) {
    const track = useTrack();
    const router = useRouter();

    const loading = ref(false);

    const getPermissionCode = (actions: ActionMode[]) => {
      const typeName = props.modelType === '1' ? 'INVESTIGATION_MODEL' : 'MONITOR_MODEL';
      return actions.map((action) => Permission[`${typeName}_${action}`]);
    };

    const handleGoUpdate = (e) => {
      e.stopPropagation();
      track(createTrackEvent(7721, '准入排查设置', '编辑模型'));
      const type = props.modelType === '1' ? 'investigation/models' : 'risk-monitor/models';
      const modelDetailUrl = `/${type}/detail/${props.value.modelId}`;
      router.push({
        path: modelDetailUrl,
      });
    };

    const actionWapper = async (fn, params) => {
      loading.value = true;
      try {
        await fn(params);
      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
      }
    };

    const handleChangeEnabled = async (e) => {
      loading.value = true;
      try {
        await setting.updateModelStatus({
          modelId: props.value.modelId,
          status: e,
        });
        message.success('修改成功');
      } catch (error) {
        console.log(error);
      }
    };
    const handlePublishModel = async () => {
      try {
        await setting.publishModel({ riskModelId: props.value.modelId });
        message.success('发布成功');
        emit('change', {
          ...props.value,
          status: 1,
        });
      } catch (error) {
        console.log(error);
      }
    };
    // 废弃模型
    const handleDeprecate = async () => {
      await setting.deprecate(props.value.modelId);
      message.success('废弃成功');
      emit('change', {
        ...props.value,
        status: 4,
      });
    };

    // 风险洞察模型废弃的前置校验
    const checkDeprecate = async () => {
      // const res = await setting.checkModelDepreacate(props.value);
      // const { canDeprecate, monitorGroupIds } = res;
      // if (!canDeprecate) {
      //   return Modal.confirm({
      //     title: '提示',
      //     content: '模型废弃后，该模型下的历史数据将不再展示。确认废弃该模型吗？',
      //     okText: '确定',
      //     cancelText: '取消',
      //     onOk: () => {
      //       handleDeprecate();
      //     },
      //   });
      // }
      handleDeprecate();
    };

    return {
      loading,
      handleGoUpdate,
      handleDeprecate,
      checkDeprecate,
      handleChangeEnabled,
      handlePublishModel,
      actionWapper,
      getPermissionCode,
    };
  },
  render() {
    const isDeprecated = this.value.status === 4 || this.value.distributedResource?.[0]?.distributeStatus === 3;
    return (
      <div class={[styles.root]}>
        {this.showDragger ? <QIcon class="drag-handle shrink-0" type="icon-icon_tuodong" /> : null}
        <Spin spinning={this.loading} class={[styles.spin, 'flex flex-col flex-1']}>
          <div class="flex items-center justify-between">
            <div data-testid="model-name" class={styles.title} onClick={(e) => this.handleGoUpdate(e)}>
              <q-icon type="icon-icon_fengxianmoxing1" />
              <Tooltip title={this.value?.modelName || '第三方风险排查标准模型'}>
                <span class={styles.text}>{this.value?.modelName || '第三方风险排查标准模型'}</span>
              </Tooltip>
              {/* 模型类型 */}
              <QTag
                style={{
                  fontWeight: 'normal',
                  fontSize: '12px',
                  backgroundColor: '#1890ff',
                  color: '#fff',
                }}
              >
                {this.value.branchTier === 1 ? '扩展模型' : '基础模型'}
              </QTag>
            </div>
            <div class="flex items-center">
              <RiskModelStatusTag
                status={this.value.status}
                distributeStatus={this.value.distributedResource?.[0]?.distributeStatus}
                style={{ marginRight: '10px' }}
              />
              {this.value.status === 1 && !isDeprecated && (
                <Popconfirm
                  title={`废弃模型将强制关闭已关联企业分组的监控功能，存在风险敞口扩大的隐患。请确认是否要执行该操作？`}
                  placement="bottomRight"
                  overlayStyle={{ width: '250px' }}
                  onConfirm={() => this.actionWapper(this.checkDeprecate, undefined)}
                >
                  <RiskAction
                    v-permission={this.getPermissionCode(['DEPRECATE'])}
                    data-testid="deprecate-model"
                    onClick={(e) => e.stopPropagation()}
                    theme="text"
                    icon="icon-icon_yichu"
                  >
                    废弃
                  </RiskAction>
                </Popconfirm>
              )}
              {/* <RiskAction theme="text" icon="icon-gongyingshangchouqu" onClick={this.handleCopyModel}>
                  复制模型
                </RiskAction> */}
              {isDeprecated ? (
                <RiskAction
                  data-testid="view-model"
                  v-permission={this.getPermissionCode(['VIEW'])}
                  theme="text"
                  icon={'icon-liulan'}
                  onClick={this.handleGoUpdate}
                >
                  查看
                </RiskAction>
              ) : (
                <RiskAction
                  data-testid="view-model"
                  v-permission={this.getPermissionCode(['EDIT'])}
                  theme="text"
                  icon={'icon-a-bianjigenjin1x'}
                  onClick={this.handleGoUpdate}
                >
                  编辑
                </RiskAction>
              )}
              {!isDeprecated && (
                <Popconfirm
                  title={`模型发布后不可再编辑，确认发布该模型吗？`}
                  placement="bottomRight"
                  onConfirm={() => this.actionWapper(this.handlePublishModel, undefined)}
                >
                  <RiskAction
                    v-permission={this.getPermissionCode(['PUBLISH'])}
                    disabled={this.value.status === 1}
                    data-testid="publish-model"
                    theme="text"
                    icon="icon-gengxinshuomingicon"
                    onClick={(e) => e.stopPropagation()}
                  >
                    发布
                  </RiskAction>
                </Popconfirm>
              )}

              {/* <NumberSwitch */}
              {/*   disabled={![0, 1].includes(this.value.status)} */}
              {/*   value={this.value.status} */}
              {/*   onChange={this.handleChangeEnabled} */}
              {/* /> */}
            </div>
          </div>
          <div class={['text-#999 flex', styles.desc]} data-testid="model-desc" v-show={this.value.comment}>
            {this.value.comment || '-'}
          </div>
        </Spin>
      </div>
    );
  },
});

export default ModelItem;
