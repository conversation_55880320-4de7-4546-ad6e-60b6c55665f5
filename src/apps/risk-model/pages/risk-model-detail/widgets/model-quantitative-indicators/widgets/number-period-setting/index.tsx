import { defineComponent, ref } from 'vue';
import { cloneDeep } from 'lodash';
import { Input } from 'ant-design-vue';

import styles from './number-period-setting.module.less';

// 整数即可
const NumberReg = /^-?\d+$/;

// 区间打分组件
const NumberPeriodSetting = defineComponent({
  name: 'NumberPeriodSetting',
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Array,
      required: true,
    },
    isFirst: {
      type: Boolean,
      default: false,
    },
    isLast: {
      type: Boolean,
      default: false,
    },
    extra: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const values = ref(cloneDeep(props.value.map((v) => v ?? '-')));

    const valueEmit = () => {
      const emitValue = values.value.map((v) => (v === '-' ? null : v));
      emit('valueChange', emitValue);
      emit('change', emitValue);
    };

    const clearValue = () => {
      const { min, max } = props.extra;
      values.value = [min ?? '-', max ?? '-'];
      valueEmit();
    };

    const valueFormat = (value, index) => {
      const { min, max } = props.extra;
      const most = index === 0 ? min : max;
      if (most) {
        if (value === '-' || !value) {
          return most;
        }
        const resValue = NumberReg.test(value);
        if (resValue) {
          if (index === 0) {
            return Number(value) < most ? most : Number(value);
          }
          return Number(value) > most ? most : Number(value);
        }
        return resValue ? Number(value) : most;
      }
      const regValue = NumberReg.test(value);
      if ([null, undefined, '-', ''].includes(value) || (!regValue && [null, undefined, '-', ''].includes(props.value[index] as any))) {
        return '-';
      }
      return regValue ? Number(value) : props.value[index];
    };

    return {
      values,
      clearValue,
      valueEmit,
      valueFormat,
    };
  },
  render() {
    if (this.isEdit) {
      return (
        <div class={[styles.container, this.disabled ? styles.disabled : '']}>
          {this.values.map((value, index) => {
            return [
              <Input
                style={{ width: '80px', border: 'none', height: '30px', textAlign: 'center' }}
                v-model={this.values[index]}
                disabled={this.disabled || (this.isFirst && index === 1) || (this.isLast && index === 0)}
                onBlur={(e) => {
                  this.values[index] = this.valueFormat(e.target.value, index);
                  this.values = [...this.values];
                  this.valueEmit();
                }}
              ></Input>,
              index === 0 ? '~' : null,
            ];
          })}
          <q-icon class={styles.icon} data-testid="number-period-setting-clear" type="icon-shanchu3" onClick={this.clearValue}></q-icon>
        </div>
      );
    }
    return (
      <div class={[styles.container, this.disabled ? styles.disabled : '']}>
        <span class={styles.number} data-testid="number-period-setting">
          {this.value[0] || '-'}
        </span>
        ~
        <span class={styles.number} data-testid="number-period-setting">
          {this.value[1] || '-'}
        </span>
      </div>
    );
  },
});

export default NumberPeriodSetting;
