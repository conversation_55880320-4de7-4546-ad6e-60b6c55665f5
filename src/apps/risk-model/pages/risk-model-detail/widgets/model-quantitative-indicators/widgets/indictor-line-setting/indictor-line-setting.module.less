.container{
  display: flex;
  align-items: center;
  height: 32px;
  line-height: 32px;
  margin-bottom: 5px;
  gap: 50px;

  :global{
    .ant-checkbox-wrapper{
      margin-right: 10px;
    }
  }

  .trapezoidWrapper{
    width:  300px;

    .trapezoid1,
    .trapezoid2,
    .trapezoid3,
    .trapezoid4,
    .trapezoid5 {
      height: 32px;
      font-size: 18px;
      font-weight: bold;
      padding-left: 10px;
      position: relative;
      display: inline-block;

      &::after{
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 4px;
        width: 100%;
        z-index: 1;
        -webkit-transform: perspective(60px) rotateX(7deg) scaleY(1.1) translateY(6px);
        -moz-transform: perspective(60px) rotateX(7deg) scaleY(1.1) translateY(6px);
        -ms-transform: perspective(60px) rotateX(7deg) scaleY(1.1) translateY(6px);
        -o-transform: perspective(60px) rotateX(7deg) scaleY(1.1) translateY(6px);
        transform: perspective(60px)rotateX(7deg) scaleY(1.1) translateY(6px);
        -webkit-transform-origin:bottom left;
        transform-origin:bottom left;
      }
    }

    .trapezoid1::after{
      background-color: #A80000;
    }

    .trapezoid2::after{
      background-color: #FF722D;
    }

    .trapezoid3::after{
      background-color: #FFC043;
    }

    .trapezoid4::after{
      background-color: #128BED;
    }

    .trapezoid5::after{
      background-color: #00AD65;
    }

    .text{
      color: #fff;
      position: absolute;
      margin: auto 0;
      z-index: 2;
      transform: translateY(5px);
    }
  }

  .minWidth{
    width: 160px;
  }

  .unActive{
    .trapezoid1::after{
      background-color: #f3f3f3;
    }

    .trapezoid2::after{
      background-color: #f3f3f3;
    }

    .trapezoid3::after{
      background-color: #f3f3f3;
    }

    .trapezoid4::after{
      background-color: #f3f3f3;
    }

    .trapezoid5::after{
      background-color: #f3f3f3;
    }

    .text{
      z-index: 0;
    }
  }
}
