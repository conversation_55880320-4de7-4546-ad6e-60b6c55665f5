import { defineComponent, inject } from 'vue';

import styles from './model-quantitative-indicators.module.less';
import IndictorLineSetting from './widgets/indictor-line-setting';
import { openQuantitativeIndicatorsSettingModel } from './widgets/quantitative-indicators-setting-model';

const ModelQuantitativeIndicators = defineComponent({
  name: 'ModelQuantitativeIndicators',
  props: {
    detail: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const editModelQualities = async () => {
      const res = await openQuantitativeIndicatorsSettingModel(props.detail);
      emit('update', res);
    };

    const isMonitor = inject('isMonitor', false);
    const disable = inject('isDisabled', false);

    return {
      editModelQualities,
      isMonitor,
      disable,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.title}>
          <div>定量指标</div>
          {!this.isMonitor && !this.disable && this.detail.status === 2 && (
            <div class={styles.extra} onClick={this.editModelQualities} data-testid="edit-model-qualities">
              <q-icon type="icon-shezhizhongxin" color="#999"></q-icon>
              设置模型
            </div>
          )}
        </div>
        <div>
          {this.detail?.resultSetting?.map((item: any, index) => (
            <IndictorLineSetting isEdit={false} lineData={{ ...item, color: index + 1 }} />
          ))}
        </div>
        {/* <div class={styles.tip}>
          <div class={styles.content}>
            若您的评分卡中任一「
            <q-icon type="icon-icon_hongsedengji" />
            红色等级」 被命中，模型结果将直接更新为
            <span class={styles.highRisk}> 「高风险」</span>
            ，请密切关注指标设置
          </div>
        </div> */}
      </div>
    );
  },
});

export default ModelQuantitativeIndicators;
