import { useToggle } from '@vueuse/core';
import { defineComponent, nextTick, watch } from 'vue';

import DimensionContent from '@/apps/investigation/pages/investigation-detail/widgets/dimension-content';
import { openDimensionContentModal } from '@/apps/investigation/pages/investigation-detail/widgets/dimension-content/dimension-content-modal';

import styles from './dynamic-collapse-block.module.less';

const DynamicCollapseBlock = defineComponent({
  name: 'DynamicCollapseBlock',
  props: {
    defaultCollapse: {
      type: Boolean,
      default: true,
    },
    arrow: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    metaList: {
      type: Array,
      default: () => [],
    },
    hitDetail: {
      type: Object,
      default: () => ({}),
    },
    dimensionStrategies: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['toggle'],
  setup(props) {
    const [isCollapse, setCollapse] = useToggle(props.defaultCollapse);

    const handleCollapse = (val?) => {
      setCollapse(val ?? !isCollapse.value);
    };

    // 兼容非蔡司的时候点击维度预览打开对应的维度详情
    watch(
      () => props.defaultCollapse,
      (val) => {
        handleCollapse(val);
      }
    );

    const handleOpenRiskTypeModal = (params) => {
      openDimensionContentModal({
        meta: params,
        title: params.title,
      });
    };

    return {
      isCollapse,
      setCollapse,
      handleCollapse,
      handleOpenRiskTypeModal,
    };
  },
  render() {
    return (
      <div
        class={{
          [styles.container]: true,
          [styles.disabled]: this.disabled,
        }}
      >
        <header
          class={styles.header}
          onClick={async () => {
            if (this.disabled) {
              return;
            }
            this.handleCollapse();
            await nextTick();
            this.$emit('toggle', this.isCollapse);
          }}
        >
          {this.$scopedSlots.header?.(this.isCollapse)}
        </header>
        <div v-show={!this.isCollapse}>
          {/* <div style="height: 160px" v-show={this.isLoading && this.result == null}>
            <QLoading size="fullsize" />
          </div> */}

          <div>
            {this.metaList.map((meta: any) => {
              return (
                <DimensionContent
                  key={`${meta.key}_${meta.strategyId}`}
                  hitDetail={this.hitDetail}
                  dimensionStrategies={this.dimensionStrategies}
                  meta={meta}
                  needRefresh={!this.isCollapse}
                  onOpenRiskTypeModal={this.handleOpenRiskTypeModal}
                />
              );
            })}
          </div>
        </div>
      </div>
    );
  },
});

export default DynamicCollapseBlock;
