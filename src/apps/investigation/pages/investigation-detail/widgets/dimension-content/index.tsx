import { computed, defineComponent, PropType, ref, watch } from 'vue';
import { isEmpty, isFunction } from 'lodash';

import RiskTableNext from '@/apps/investigation/pages/investigation-detail/widgets/risk-table-next';
import { useFetchRiskDimension } from '@/hooks/use-fetch-risk-dimension';
import QLoading from '@/components/global/q-loading';
import CommonSearchFilter from '@/components/common/common-search-filter';
import { filterConfigMap } from '@/apps/investigation/pages/investigation-detail/widgets/dimension-content/config';
import RiskHitReasonWrapper from '@/shared/components/risk-hit-reason-wrapper';

import styles from './dimension-content.module.less';

type MetaType = {
  snapshotId: string;
  key: string; // dimensionKey
  strategyId: string;
};

const DimensionContent = defineComponent({
  name: 'DimensionContent',
  props: {
    hitDetail: {
      type: Object,
      default: () => ({}),
    },
    needRefresh: {
      type: <PERSON>olean,
      default: false,
    },
    // 请求参数
    meta: {
      type: Object as PropType<MetaType>,
      default: () => ({}),
    },
    dimensionStrategies: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const isInit = ref(true);

    const hasFilter = computed(() => Object.keys(filterConfigMap).includes(props.meta.key));

    const { filterParams, initialAggs, pagination, setPagination, sort, setSort, result, isLoading, isLoaded, search } =
      useFetchRiskDimension(props.meta, hasFilter.value); // 风险维度 hook

    const refreshFilterConfig = ref<boolean>(false);
    const currentAggs = computed(() => {
      if (isEmpty(initialAggs.value)) {
        return {};
      }
      // 记录初始筛选项，防止选到空数据后筛选项消失只能刷新页面重新加载
      const newData = Object.keys(initialAggs.value).reduce((acc, cur) => {
        const oldAgg = initialAggs.value[cur] || [];
        const newAgg = result.value?.aggs?.[cur] || [];
        acc[cur] = oldAgg.map((v) => newAgg.find((agg) => agg.key === v.key) || { ...v, doc_count: 0 });
        return acc;
      }, {});
      return newData;
    });

    const filterConfig = ref([]);
    const getFilterConfig = () => {
      if (!hasFilter.value) return;
      if (isLoading.value) {
        // 等接口加载完了再渲染，否则选项数据是上一次的
        refreshFilterConfig.value = true;
        return;
      }
      filterConfig.value = isFunction(filterConfigMap[props.meta.key])
        ? filterConfigMap[props.meta.key](currentAggs.value)
        : filterConfigMap[props.meta.key];
    };
    getFilterConfig();

    watch(
      () => result.value?.aggs,
      () => {
        if (refreshFilterConfig.value) {
          getFilterConfig();
          refreshFilterConfig.value = false;
        }
      }
    );

    const handleFilterChange = (payload) => {
      filterParams.value = payload;
      setPagination({
        current: 1,
        pageSize: pagination.pageSize,
      });
    };

    const handleSortChange = (payload) => {
      setSort(payload);
      setPagination({
        current: 1,
        pageSize: pagination.pageSize,
      });
    };

    watch(
      () => props.needRefresh,
      (val) => {
        if (val && isInit.value) {
          isInit.value = false;
          search();
        }
      },
      { immediate: true }
    );

    /** 仅显示当前命中的条件 */
    const filteredDimensionStrategies = computed(() => {
      return props.dimensionStrategies.filter(({ dimensionStrategyId }: any) => dimensionStrategyId === props.meta.strategyId);
    });

    return {
      pagination,
      setPagination,
      sort,
      setSort,
      result,
      isLoading,
      isLoaded,
      search,
      hasFilter,
      filterConfig,
      getFilterConfig,
      handleFilterChange,
      filterParams,
      initialAggs,
      handleSortChange,
      filteredDimensionStrategies,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div data-testid="el-loading" style="height: 160px" v-show={this.isLoading && this.result == null}>
          <QLoading size="fullsize" />
        </div>
        <RiskTableNext
          hitDetail={this.hitDetail}
          tkey={(this as any).meta.tkey}
          loading={this.isLoading}
          meta={this.meta}
          displayKey={this.result?.displayKey}
          dataSource={this.result?.data}
          sort={this.sort}
          onSortChange={this.handleSortChange}
          pagination={this.result?.pagination}
          onPageChange={this.setPagination}
          onUpdate={this.search}
          onOpenRiskTypeModal={(params) => this.$emit('openRiskTypeModal', params)}
        >
          <span class={styles.hint} slot="extra">
            <RiskHitReasonWrapper
              placement="right"
              hitDetails={this.hitDetail.hitDetails}
              dimensionStrategies={this.filteredDimensionStrategies}
            />
          </span>

          <CommonSearchFilter
            v-show={this.hasFilter && !isEmpty(this.initialAggs)}
            showSearch={false}
            needReset={false}
            checkSelectBorder={true}
            defaultValue={this.filterParams as any}
            filterConfig={this.filterConfig}
            onChange={this.handleFilterChange}
            onGetOptions={this.getFilterConfig}
          />
        </RiskTableNext>
      </div>
    );
  },
});

export default DimensionContent;
