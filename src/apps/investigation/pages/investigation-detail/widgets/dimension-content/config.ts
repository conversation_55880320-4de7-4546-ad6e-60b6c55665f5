/* eslint-disable camelcase */
import { isArray } from 'lodash';

import AREA_OPTIONS from '@/shared/constants/area.constant';
import { CaseStatusList, CaseTypesList, CourtLevelList, DocTypeList } from '@/shared/constants/lawsuit-result-code-map.constant';
import { DEFAULT_DATE_RANGE } from '@/config/tender.config';

type AggDataType = {
  key: string;
  doc_count: number;
  [prop: string]: any;
};

type SelectOption = {
  label: string;
  value: any;
};

type FilterConfig = {
  defaultLabel?: string;
  defaultValue?: string | number | undefined;
  dict?: SelectOption[];
};

const getFilterOptions = (data: AggDataType[] = [], needDefaultOption = true, option?: FilterConfig): SelectOption[] => {
  let res: any[] = [];
  const formatData = data.filter((item) => item.key);
  if (isArray(option?.dict) && option?.dict.length) {
    res = formatData.map((item) => {
      const text = option?.dict?.find((d) => d.value === item.key)?.label;
      return {
        label: text || item.key,
        value: item.key,
        disabled: item.doc_count === 0,
        count: item.doc_count,
      };
    });
  } else {
    res = formatData.map((item) => {
      return {
        label: item.key,
        value: item.key,
        disabled: item.doc_count === 0,
        count: item.doc_count,
      };
    });
  }
  if (needDefaultOption) {
    return [{ label: option?.defaultLabel ?? '不限', value: option?.defaultValue ?? undefined }, ...res];
  }
  return res;
};

const JudgementGroups = ({
  principals = [] as AggDataType[],
  reason = [] as AggDataType[],
  area = [] as AggDataType[],
  caseType = [] as AggDataType[],
  judgmentType = [] as AggDataType[],
  courtLevel = [] as AggDataType[],
  courtName = [] as AggDataType[],
  dimensionStatus = [] as AggDataType[],
  year = [] as AggDataType[],
  trialRound = [] as AggDataType[],
} = {}) => {
  return [
    {
      field: 'principalRole',
      type: 'multiple',
      placeholder: '身份',
      options: getFilterOptions(principals, false),
      meta: {
        mode: 'inline',
        style: {
          marginTop: 0,
          marginRight: '10px',
        },
        placement: 'bottomRight',
      },
    },
    {
      field: 'reason',
      type: 'single',
      placeholder: '案由',
      options: getFilterOptions(reason, true),
      meta: {
        mode: 'inline',
        style: {
          marginTop: 0,
          marginRight: '10px',
        },
        placement: 'bottomRight',
      },
    },
    {
      field: 'filter-group',
      label: '更多筛选',
      type: 'fold-groups',
      children: [
        {
          field: 'year',
          type: 'button',
          label: '发布年份',
          options: getFilterOptions(year),
          meta: {
            maxLength: 15,
            defaultButton: false,
            zeroDisable: true,
          },
        },
        {
          field: 'dimensionStatus',
          type: 'button',
          label: '案件状态',
          options: getFilterOptions(dimensionStatus, true, { dict: CaseStatusList }),
          meta: {
            maxLength: 15,
            defaultButton: false,
            zeroDisable: true,
          },
        },
        {
          field: 'courtLevel',
          type: 'button',
          label: '法院层级',
          options: getFilterOptions(courtLevel, true, { dict: CourtLevelList }),
          meta: {
            maxLength: 15,
            defaultButton: false,
            zeroDisable: true,
          },
        },
        {
          field: 'courtName',
          type: 'button',
          label: '法院名称',
          options: getFilterOptions(courtName),
          meta: {
            maxLength: 15,
            defaultButton: false,
            zeroDisable: true,
          },
        },
        {
          field: 'caseType',
          type: 'button',
          label: '案件类型',
          options: getFilterOptions(caseType, true, { dict: CaseTypesList }),
          meta: {
            maxLength: 15,
            defaultButton: false,
            zeroDisable: true,
          },
        },
        {
          field: 'trialRound',
          type: 'button',
          label: '审理程序',
          options: getFilterOptions(trialRound),
          meta: {
            maxLength: 15,
            defaultButton: false,
            zeroDisable: true,
          },
        },
        {
          field: 'judgmentType',
          type: 'button',
          label: '文书类型',
          options: getFilterOptions(judgmentType, true, { dict: DocTypeList }),
          meta: {
            maxLength: 15,
            defaultButton: false,
            zeroDisable: true,
          },
        },
        {
          field: 'area',
          type: 'button',
          label: '地域',
          options: getFilterOptions(area, true, { dict: AREA_OPTIONS }),
          meta: {
            maxLength: 15,
            defaultButton: false,
            zeroDisable: true,
          },
        },
      ],
    },
  ];
};

const NegativeNewsGroups = ({ tags = [] as AggDataType[], sourceName = [] as AggDataType[] }) => {
  return [
    {
      field: 'tags',
      type: 'multiple',
      placeholder: '主题类型',
      options: getFilterOptions(tags, false),
      meta: {
        mode: 'inline',
        style: {
          marginTop: 0,
          marginRight: '10px',
        },
        placement: 'bottomRight',
      },
    },
    {
      field: 'sourceName',
      type: 'single',
      placeholder: '媒体类型',
      options: getFilterOptions(sourceName, true),
      meta: {
        mode: 'inline',
        style: {
          marginTop: 0,
          marginRight: '10px',
        },
        placement: 'bottomRight',
      },
    },
    {
      field: 'publishTime',
      type: 'single',
      placeholder: '发布时间',
      options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
      custom: {
        type: 'date-range',
      },
      meta: {
        mode: 'inline',
        style: {
          marginTop: 0,
          marginRight: 0,
        },
        placement: 'bottomRight',
      },
    },
  ];
};

const OutwardInvestmentAnalysisGroups = ({ status = [] as AggDataType[], totalpercent = [] as AggDataType[] }) => {
  const totalPercentOptions: SelectOption[] = totalpercent.map((v) => {
    return {
      label: v.key,
      value: { min: v.from, max: v.to },
      count: v.doc_count,
      disabled: v.doc_count === 0,
    };
  });
  return [
    {
      field: 'status',
      type: 'multiple',
      placeholder: '存续状态',
      options: getFilterOptions(status, false),
      meta: {
        mode: 'inline',
        style: {
          marginTop: 0,
          marginRight: '10px',
        },
        placement: 'bottomRight',
      },
    },
    {
      field: 'totalpercent',
      type: 'single',
      placeholder: '股权比例',
      options: [{ value: undefined, label: '不限' }].concat(totalPercentOptions),
      meta: {
        mode: 'inline',
        style: {
          marginTop: 0,
          marginRight: 0,
        },
        placement: 'bottomRight',
      },
    },
  ];
};

export const filterConfigMap = {
  Judgement: JudgementGroups, // 裁判文书
  NegativeNews: NegativeNewsGroups, // 负面新闻
  OutwardInvestmentAnalysis: OutwardInvestmentAnalysisGroups, // 近一年企业对外投资企业注销占比
};
