/* istanbul ignore file */
import moment from 'moment';
import { getCurrentInstance, VNode } from 'vue';
import { cloneDeep, intersection, isArray, isEmpty, isNil, isNumber, reverse, uniq } from 'lodash';
import { <PERSON><PERSON>, Tooltip } from 'ant-design-vue';
import Big from 'big.js';

import QIcon from '@/components/global/q-icon';
import QRoleText from '@/components/global/q-role-text';
import { dateFormat } from '@/utils/format';
import { RISK_LEVEL_CODE_THEME_MAP } from '@/shared/constants/risk-level-code-map.constant';
import QTag from '@/components/global/q-tag';
import QEntityLink from '@/components/global/q-entity-link';
import QSimpleEntityLink from '@/components/global/q-entity-link/simple';
import ClampContent from '@/components/clamp-content';
import { getTranslatePathItem } from '@/shared/services/diligence.service';
import { getRiskLevelStyle } from '@/config/risk.config';
import FileLogo from '@/components/file-logo';
import { getColor, getStyleByJob } from '@/utils/format/company-format';
import { hanlePatentStatusColor, LAWSUIT_RESULT_CODE_MAP } from '@/shared/constants/lawsuit-result-code-map.constant';
import { punishReasonTypeMap } from '@/shared/config/risk-settings.config';
import { numberToHumanWithUnit } from '@/utils/number-formatter';
import CompanyStatus from '@/components/global/q-company-status';
import AssociationPath from '@/components/association-path';
import { getDetailByType } from '@/config/risk-detail.config';
import { getContent } from '@/utils/content-helper';

import styles from './risk-table-next.module.less';
import { getHighLightDifferent } from '../../utils/string-buffer';
import JudicialCase from '@/apps/investigation/pages/investigation-detail/widgets/risk-table-next/widgets/judicial-case';
import QRoleList from '@/components/global/q-role-list';
import QGlossaryInfo from '@/components/global/q-glossary-info';

const getJobClass = (job) => {
  if (['法定代表人'].includes(job)) {
    return styles.tagBlue;
  }
  if (['实际控制人', '受益所有人', '股东'].includes(job)) {
    return styles.tagGold;
  }
  return styles.tagDefault;
};

const getJobArr = (job: string) => {
  if (!job) return [];
  const arr = job.split(',');
  const order = ['法定代表人', '实际控制人', '受益所有人', '股东'];
  return intersection(order, arr).concat(arr.filter((v) => !order.includes(v)));
};

const getFormattedDate = (text, formatter = 'YYYY-MM-DD') => {
  if (!text || text < 0 || text === '-') {
    return '';
  }
  if (isNumber(text)) {
    text *= 1000;
  }
  return moment(text).format(formatter);
};

// 与内部黑名单企业存在投资任职关联，与第三方列表企业存在投资任职关联 图谱
const preDealConnetPath = (item) => {
  const HistoryMap = {
    HISEMPLOY: '历史高管',
    HISLEGAL: '历史法人',
    HISINVEST: '历史股东',
  };
  const underStr = HistoryMap[item.typeDD] || item.job || item.stockPercent || '投资';
  const relatedUnderStr = HistoryMap[item.typeRelated] || item.relatedJob || item.relatedStockPercent;
  let pathArr = [
    {
      KeyNo: item.companyId,
      Name: item.companyName,
      Level: '1',
      isReverse: true,
      underStr,
    },
    {
      KeyNo: item.personId,
      Name: item.personName,
      Level: '2',
      underStr: relatedUnderStr,
    },
    {
      KeyNo: item.relatedCompanyId,
      Name: item.relatedCompanyName,
      Level: '3',
    },
  ];
  if (!item.personId) {
    const path = [
      {
        KeyNo: item.companyId,
        Name: item.companyName,
        Level: '1',
        underStr: item.stockPercent || item.role,
      },
      {
        KeyNo: item.relatedCompanyId,
        Name: item.relatedCompanyName,
        Level: '2',
        underStr: item.stockPercent || item.role,
      },
    ];

    pathArr = item.direction > 0 ? path : reverse(path);
  }

  // return pathArr.length ? <AssociationPath paths={pathArr} showTitle></AssociationPath> : '-';
  return pathArr;
};

const preDealControlPath = (scope, meta?, needTitle = true) => {
  let pathData = [];
  const beginData = {
    Name: scope.name,
    KeyNo: scope.keyNo,
  };
  try {
    const oriData = cloneDeep(scope.details?.path) || [];
    pathData = oriData.map((item) => {
      item.unshift(beginData);
      return item.map((data, idx) => {
        return {
          ...data,
          underStr: item[idx + 1] ? item[idx + 1].Percent : data.Percent,
        };
      });
    });
  } catch (error) {
    console.log(error);
  }

  if (pathData?.length) {
    return (
      <ClampContent line={4} clampKey={scope.companyId + meta.tkey}>
        <AssociationPath paths={pathData} showTitle={needTitle}></AssociationPath>
      </ClampContent>
    );
  }
  return '-';
};

export const getPath = (relationObj) => {
  const paths = relationObj.map((item) => {
    const record = getTranslatePathItem(item);
    return preDealConnetPath(record);
  });
  if (paths.length > 0) {
    return (
      <div class="flex">
        <AssociationPath paths={paths} showTitle></AssociationPath>
      </div>
    );
  }
  return '-';
};

/**
 * 获取关联路径
 * 由于接口返回的paths包含所有路径，需要根据 {level: '1'} 来截取路径
 * @param paths 格式：[{level: '1'}, {level: '2'}, {level: '3'}, {level: '1'}, {level: '2'}, {level: '1'}]
 * @param fromIndex 记录上次截取的下标，从上次结束的地方开始截取
 *
 * [{level: '1'}, {level: '2'}, {level: '1'}]
 */
const getPartialPath = (pathList: Record<string, any>[], fromIndex = 0): [number, Record<string, any>[]] => {
  const restPart = pathList.slice(fromIndex + 1);
  const index = restPart.findIndex((item) => item.level?.toString() === '1');
  const endIndex = index >= 0 ? index + fromIndex + 1 : pathList.length;
  return [endIndex, pathList.slice(fromIndex, endIndex)];
};

const getInvestmentPath = (startInfo: { name: string; keyNo: string }, paths = []) => {
  const startCompany = {
    Name: startInfo.name,
    KeyNo: startInfo.keyNo,
    Level: 0,
    Percent: 0,
    PercentTotal: 0,
  };

  let startIndex = 0;
  const pathData = paths.map((item) => {
    const [endIndex, realPaths] = getPartialPath(item, startIndex);
    startIndex = endIndex;
    const fullPath = [startCompany, ...realPaths];
    const percentTotal = realPaths.map((v) => new Big(parseFloat(v.percent)).div(100)).reduce((a, b) => a.times(b));
    return fullPath.map((data: any, idx) => {
      return {
        ...data,
        Name: data.Name || data.name,
        KeyNo: data.KeyNo || data.keyNo,
        Level: data.Level || data.level,
        underStr: fullPath[idx + 1] ? (fullPath[idx + 1] as any).percent : data.percent,
        PercentTotal: `${parseFloat(percentTotal.times(100).toFixed(4))}%`,
      };
    });
  });
  return pathData;
};

/**
 * FIXME: 为每个渲染函数添加测试数据用例
 * @param meta
 * @returns
 */
export const getScopedSlots = (meta) => {
  const instance = getCurrentInstance();
  const vm = instance?.proxy || ({} as any);
  const isNotDynamicDetails = vm?.params?.type !== 'dynamicDetails';

  // WARNING: 下面的h删了会报错
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const h = vm.$createElement;

  // 受益股份样式
  const renderBenifit = (content) => {
    return (
      <div class="flex">
        <QEntityLink coyObj={content} />
        {content.PercentTotal ? <span>，受益股份({content.PercentTotal})</span> : ''}
      </div>
    );
  };

  const pledgCompanyChange = (text, record, key) => {
    // 获取对应字段
    if (!text && !record[key]) {
      return '-';
    }
    let textArr = [];
    //  如果对应key的字段中有值直接用，否则去NameAndKeyNo中进行匹配筛选
    if (record[key]) {
      textArr = record[key];
    } else {
      const textNameList = typeof text === 'string' ? [text] : text;
      textArr = textNameList.reduce((textList, name) => {
        // 从NameAndKeyNo字段中筛选出textNameList对应的KeyNo
        const nameMatch = record.NameAndKeyNo.filter((item) => item.Name === name);
        textList = [...textList, ...nameMatch];
        return textList;
      }, []);
    }

    return <QEntityLink coy-arr={textArr} />;
  };
  return {
    // 给带单位的数字字符串加千分符
    thousandsNumber: numberToHumanWithUnit,
    date: (text) => {
      if (!text || text < 0 || text === '-') {
        return '-';
      }
      if (isNumber(text)) {
        text *= 1000;
      }
      return moment(text).format('YYYY-MM-DD');
    },
    name: (item) => {
      const keyNo = item.keyNo || item.KeyNo;
      const name = item.name || item.Name;
      if (!keyNo) {
        return name || '-';
      }
      // 需要判断是否为企业
      const isPerson = keyNo.startsWith('p');
      const url = isPerson
        ? `/embed/beneficaryDetail?personId=${keyNo}&title=${name}`
        : `/embed/companyDetail?keyNo=${keyNo}&title=${name}`;
      return (
        <a href={url} target="_blank">
          {item.name || item.Name}
        </a>
      );
    },
    docNo: (item) => {
      return (
        <a href={`/embed/adminpenaltydetail?id=${item.RiskId}&title=${item.docNo || item.CaseNo}`} target="_blank">
          {item.docNo || item.CaseNo}
        </a>
      );
    },
    annoName: () => {
      return (
        <a href={`/embed/beneficaryDetail?personId=${meta.keyNo}&title=${meta.companyName}`} target="_blank">
          {meta.companyName}
        </a>
      );
    },
    // 动产抵押
    ChattelMortgagedyr: (item) => {
      return <QEntityLink coyObj={item.RelatedCompanyInfo}></QEntityLink>;
    },
    ChattelMortgagebdyr: (item) => {
      return <QEntityLink coy-arr={item.MPledgeDetail && item.MPledgeDetail.PledgeeList}></QEntityLink>;
    },
    RegisterNo: (item) => {
      return item.RegisterNo ? <a onClick={() => vm.showDetail(meta.key, item)}>{item.RegisterNo}</a> : '-';
    },
    // 动产抵押-所有权或使用权归属
    MPledgeDetail: (item: any) => {
      if (item?.MPledgeDetail?.GuaranteeList) {
        const guishu: any = [];
        const guishukeyno: any = [];
        item.MPledgeDetail.GuaranteeList.forEach((gua: any) => {
          if (gua.KeyNoList) {
            gua.KeyNoList.forEach((knl: any) => {
              if (!guishu.includes(knl.Name)) {
                guishu.push(knl.Name);
                guishukeyno.push([knl.Name, knl.KeyNo]);
              }
            });
          }
        });
        if (guishukeyno?.length) {
          const filters = guishukeyno.filter((s, i) => i < 3);
          return filters.map((i, index) => {
            if (i[1]) {
              return (
                <div>
                  <a href={`/embed/companyDetail?keyNo=${i[1]}&title=${i[0] || ''}`} target="_blank">
                    {i[0] || '-'}
                  </a>
                  {index === 2 && '等'}
                </div>
              );
            }
            return i[0];
          });
        }
        return '-';
      }
      return '-';
    },
    // 土地抵押
    LandMortgagedyr: (item) => {
      return <QEntityLink coyObj={item.MortgagorNames && item.MortgagorNames[0]}></QEntityLink>;
    },
    LandMortgagebdyr: (item) => {
      return <QEntityLink coyObj={item.MortgagePeoples && item.MortgagePeoples[0]}></QEntityLink>;
    },
    startEndDate: (item, record, index, column) => {
      const startKey = column.entity?.start || 'StartDate';
      const endKey = column.entity?.end || 'EndDate';
      const start = item[startKey] && dateFormat(item[startKey]);
      const end = item[endKey] && dateFormat(item[endKey]);
      if (!start && !end) {
        return '-';
      }
      return (
        <div>
          <span>{start || '-'}</span>
          <span> 至 </span>
          <span>{end || '-'}</span>
        </div>
      );
    },
    // 行政处罚
    PenaltiesNo: (item) => {
      if (item.CaseNo) {
        return <a onClick={() => vm.gotoDetail(meta.key, item)}>{item.CaseNo}</a>;
      }
      return '-';
    },
    // 股权出质
    RegistNo: (item) => {
      if (item.RegistNo) {
        return <a onClick={() => vm.showDetail(meta.key, item)}>{item.RegistNo}</a>;
      }
      return '-';
    },
    pledgorInfo: (item) => {
      return item.PledgorInfo && item.PledgorInfo.length ? (
        <div>
          {item.PledgorInfo.map((v) => {
            return (
              <div class="flex">
                <QEntityLink coyObj={v}></QEntityLink>
                {v.Job ? (
                  <div class={styles.jobbox} style={{ marginLeft: '5px' }}>
                    {v.Job.split(',').map((job) => {
                      return <span class={styles.jobPostion}>{job}</span>;
                    })}
                  </div>
                ) : null}
              </div>
            );
          })}
        </div>
      ) : (
        '-'
      );
    },
    relatedCompanyInfo: (item) => {
      return <QEntityLink coyObj={item.RelatedCompanyInfo} />;
    },
    pledgeeInfo: (item) => {
      return (
        <div>
          {item.PledgeeInfo && item.PledgeeInfo.length
            ? item.PledgeeInfo.map((v) => {
                return <QEntityLink coyObj={{ KeyNo: v.KeyNo, Name: v.Name }}></QEntityLink>;
              })
            : '-'}
        </div>
      );
    },
    // 司法拍卖
    biaoti: (item) => {
      return (
        <div>
          {item.name || '-'}
          <span>
            <div v-show={vm.getTags(item).length > 0} class="tags" style="margin-top: 5px">
              {vm.getTags(item).map((v: any, index) => {
                return (
                  <QTag type={v.type} key={index} style={{ color: '#666' }}>
                    {v.label}
                  </QTag>
                );
              })}
            </div>
          </span>
        </div>
      );
    },
    // 担保信息
    GuaranteeInfo: (item) => {
      return <QEntityLink coy-arr={item.Guarantee}></QEntityLink>;
    },
    VoucheeInfo: (item) => {
      return <QEntityLink coy-arr={item.Vouchee}></QEntityLink>;
    },
    // 担保风险
    Creditor: (item) => {
      return <QEntityLink coy-arr={item.Creditor}></QEntityLink>;
    },
    companies: (list) => {
      if (!isArray(list) || list.length === 0) {
        return '-';
      }
      return <QEntityLink coy-arr={list}></QEntityLink>;
    },
    // 案号
    CaseNo: (item) => {
      let title;
      switch (meta.key) {
        case 'SalesContractDispute':
        case 'CompanyOrMainMembersCriminalInvolve':
        case 'CompanyOrMainMembersCriminalInvolveHistory':
        case 'MajorDispute':
          title = item.casename;
          break;
        default:
          title = `${item.Court || item.ExecuteGov || ''}${item.CaseNo}`;
      }
      if (['SalesContractDispute', 'MajorDispute', 'CompanyOrMainMembersCriminalInvolve'].includes(meta.key)) {
        return (
          <span>
            <a href={`/embed/judgementInfo?id=${String(item.id).slice(0, -1)}&title=${title}`} target="_blank">
              {item.CaseNo || item.caseno || '-'}
            </a>
          </span>
        );
      }
      // 案件风险
      // pro-front: src/routes/companyDetail/detail-pops/judicial-panorama/config.js
      if (['Judgement'].includes(meta.key)) {
        return (
          <span>
            <a href={`/embed/judgementInfo?id=${String(item.id).slice(0, -1)}&lawsuitResult=${item.lawsuitResult}`} target="_blank">
              {item.casename || '-'}
            </a>
          </span>
        );
      }

      // 被执行人信息案号弹窗处理
      if (['PersonExecution'].includes(meta.key)) {
        return <a onClick={() => vm.showDetail(meta.key, item)}>详情</a>;
      }

      const url = getDetailByType(meta.key, item);
      if (!url) {
        return item.CaseNo || item.caseno || '-';
      }
      return (
        <span>
          <a href={`${url}&title=${title}`} target="_blank">
            {item.CaseNo || item.caseno || '-'}
          </a>
        </span>
      );
    },
    // 行政处罚案号，带处罚事由
    casenoWithTag: (record) => {
      const { CaseNo, punishreasontype } = record;
      return (
        <div>
          <div> {CaseNo || '-'} </div>
          {Array.isArray(punishreasontype) && punishreasontype.length > 0 ? (
            <div class={'flex'}>
              {punishreasontype.reduce((arr, cur) => {
                const label = punishReasonTypeMap.find((op) => op.value === cur)?.label;
                if (label) {
                  arr.push(<QTag type="list">{label}</QTag>);
                }
                return arr;
              }, [])}
            </div>
          ) : null}
        </div>
      );
    },
    // 股权冻结 - 执行通知书文号
    CaseSearchId: (item) => {
      if (!item.CaseSearchId) {
        return item.CaseNo || item.caseno || '-';
      }
      const url = `/embed/courtCaseDetail?caseId=${item.CaseSearchId}&title=${item.CaseNo || ''}`;
      return (
        <span>
          <a href={url} target="_blank">
            {item.CaseNo || item.caseno || '-'}
          </a>
        </span>
      );
    },
    OrgNo: (item) => {
      if (!item.CaseSearchId) {
        return item.OrgNo || '-';
      }
      const courtName = item.Executegov || item.ExecuteGov || (Array.isArray(item.Court) ? item.Court[0] : item.Court);
      const url = `/embed/courtCaseDetail?caseId=${item.CaseSearchId}&title=${courtName}${item.OrgNo}`;
      return (
        <a href={url} target="_blank">
          {item.OrgNo || '-'}
        </a>
      );
    },
    // 限制高消费
    limitObj: (item) => {
      return (
        <div>
          <QEntityLink coyObj={{ KeyNo: item.KeyNo, Name: item.Name }}></QEntityLink>
          <span style="display: block">
            {(item.NameAndKeyNo ?? []).map((preson) => {
              return preson.KeyNo === item.KeyNo && preson.TD ? <QRoleText v-show={preson.TD} roleD={preson.TD}></QRoleText> : null;
            })}
          </span>
        </div>
      );
    },
    applicant: (item) => {
      return <div>{item.ApplicantInfo?.length ? <QEntityLink ellipsis={false} coy-arr={item.ApplicantInfo} /> : '-'}</div>;
    },
    fullNameApplicant: (record) => {
      const { SubjectInfo, ApplicantInfo } = record;
      const list = SubjectInfo.reduce((arr, cur) => {
        const { ShowName } = cur;
        const applicant = ApplicantInfo.find((app) => app.Name === ShowName);
        if (applicant) {
          arr.push(cur);
        }
        return arr;
      }, []);

      return <div>{list.length ? <QEntityLink ellipsis={false} coy-arr={list} /> : '-'}</div>;
    },
    originalSource: (item) => {
      return <FileLogo fileData={item} />;
    },
    assetsList: (list) => {
      return (
        list?.map((v) => {
          return (
            <FileLogo
              fileData={{
                OssUrl: v.url,
              }}
            />
          );
        }) || '-'
      );
    },
    // 限制出境
    limitedPerson: (item) => {
      if (item.LimitedPerson?.length) {
        return item.LimitedPerson.map((s) => {
          return (
            <QEntityLink
              coyObj={{
                Name: s.Name,
                KeyNo: s.KeyNo,
              }}
            ></QEntityLink>
          );
        });
      }
      return '-';
    },
    /**
     * 工商外链处理
     */
    entityLinks: (items) => {
      if (Array.isArray(items) && items.length > 0) {
        return items.map((item) => {
          return (
            <QEntityLink
              key={item.KeyNo}
              coyObj={{
                Name: item.Name,
                KeyNo: item.KeyNo,
              }}
            ></QEntityLink>
          );
        });
      }
      return '-';
    },
    EntityLinksAndJob: (items) => {
      if (Array.isArray(items) && items.length > 0) {
        return items.map((item) => {
          return (
            <span class="flex flex-wrap" style={{ gap: '4px' }}>
              <QSimpleEntityLink
                value={{
                  KeyNo: item.KeyNo,
                  Org: item.Org,
                  Name: item.Name,
                }}
              />
              {item.Job
                ? item.Job?.split(',').map((job) => {
                    return (
                      <span class={styles.jobPostion} style={{ marginLeft: '0' }}>
                        {job}
                      </span>
                    );
                  })
                : null}
            </span>
          );
        });
      }
      return '-';
    },
    executedPerson: (item) => {
      return (
        <div>
          {item.ExecutedPerson && item.ExecutedPerson.length
            ? item.ExecutedPerson.map((pro, index) => {
                return (
                  <span>
                    <QEntityLink coyObj={pro}></QEntityLink>
                    {pro.TD && <QRoleText roleD={pro.TD}></QRoleText>}
                    <span v-show={index + 1 !== item.executedPerson.length}>，</span>
                  </span>
                );
              })
            : '-'}
        </div>
      );
    },
    applayer: (item) => {
      return (
        <div>
          {item.applayer && item.applayer.length
            ? item.applayer.map((pro, index) => {
                return (
                  <span>
                    <QEntityLink coyObj={pro}></QEntityLink>
                    <span v-show={item.applayer.length > 1 && index !== item.applayer.length - 1}>，</span>
                  </span>
                );
              })
            : '-'}
        </div>
      );
    },
    // 破产重整
    respondentName: (list) => {
      return (
        <div>
          {list?.map((v: any) => {
            return (
              <div>
                <QEntityLink coyObj={v}></QEntityLink>
                {v.Job
                  ? getJobArr(v.Job).map((job) => {
                      return (
                        <span class={[styles.jobPostion, getJobClass(job)]} style={{ marginLeft: '0' }}>
                          {job}
                        </span>
                      );
                    })
                  : null}
              </div>
            );
          }) || '-'}
        </div>
      );
    },
    // 被执行人
    SqrInfo: (item) => {
      return item.SqrInfo?.length ? <QEntityLink coy-arr={item.SqrInfo}></QEntityLink> : '-';
    },
    blacklistCompanyName: (item) => {
      return (
        <div>
          {item.Name ? (
            <QEntityLink
              coyObj={{
                Name: item.Name,
                KeyNo: item.KeyNo,
              }}
            ></QEntityLink>
          ) : (
            '-'
          )}
        </div>
      );
    },
    // 产品召回
    NameAndKeyNo: (item) => {
      return (
        <div>
          {item.NameAndKeyNo && item.NameAndKeyNo.length
            ? item.NameAndKeyNo.map((v: any) => {
                return (
                  <div>
                    <QEntityLink coyObj={v}></QEntityLink>
                  </div>
                );
              })
            : '-'}
        </div>
      );
    },
    // 食品安全不合格
    CheckResult: (item) => {
      const { ExecuteStatus = '-', AmountDesc } = item;
      const isUnqualified = ExecuteStatus === '不合格' || ExecuteStatus === 2; // 是否是不合格
      const UnqualifiedDetails = JSON.parse(AmountDesc) || []; // 不合格的详情

      return (
        <div style={{ textAlign: 'center' }}>
          <QTag type={isUnqualified ? 'danger' : 'success'}>{ExecuteStatus}</QTag>
          {isUnqualified && UnqualifiedDetails && UnqualifiedDetails.length > 0 ? (
            <div style={{ marginTop: '4px' }}>
              <a onClick={() => vm.showInfo(meta.key, item)}>
                详情<QIcon type="icon-wenzilianjiantou"></QIcon>
              </a>
            </div>
          ) : null}
        </div>
      );
    },
    // 双随机抽查
    taskNo: (item) => {
      return <a onClick={() => vm.showInfo(meta.key, item)}>{item.OrgNo || '-'}</a>;
    },
    // 董监高变更前
    beforeEmployees: (item) => {
      return (
        <div>
          {item?.before?.Employees && item?.before?.Employees.length
            ? item?.before?.Employees.map((v: any) => {
                return (
                  <span style={{ marginRight: '5px' }}>
                    <QEntityLink
                      coyObj={{
                        Name: v.EmployeeName,
                        KeyNo: v.KeyNo,
                      }}
                    ></QEntityLink>
                    {`${v.Job ? ',' : ''} ${v.Job}`};
                  </span>
                );
              })
            : '-'}
        </div>
      );
    },
    // 董监高变更后
    Employees: (item) => {
      return (
        <div>
          {item.Employees && item.Employees.length
            ? item.Employees.map((v: any) => {
                return (
                  <span style={{ marginRight: '5px' }}>
                    <QEntityLink
                      coyObj={{
                        Name: v.EmployeeName,
                        KeyNo: v.KeyNo,
                      }}
                    ></QEntityLink>
                    {`${v.Job ? ',' : ''} ${v.Job}`};
                  </span>
                );
              })
            : '-'}
        </div>
      );
    },
    // 法定代表人变更后
    afterLegalPerson: (item) => {
      return item?.after ? (
        <QEntityLink
          ellipsis={false}
          key={item.after.KeyNo}
          coyObj={{
            Name: item.after.OperName,
            KeyNo: item.after.KeyNo,
          }}
        />
      ) : (
        '-'
      );
    },
    // 法定代表人变更前
    LegalPerson: (item) => {
      return (
        <QEntityLink
          ellipsis={false}
          key={item.KeyNo}
          coyObj={{
            Name: item.OperName,
            KeyNo: item.KeyNo,
          }}
        />
      );
    },
    afterScope: (item) => {
      const result = getHighLightDifferent(item?.Scope, item.after?.Scope);
      return <div class={styles.highlight} domPropsInnerHTML={result.afterContent}></div>;
    },
    afterAddress: (item) => {
      const result = getHighLightDifferent(item?.Address, item.after?.Address);
      return <div class={styles.highlight} domPropsInnerHTML={result.afterContent}></div>;
    },
    beforeScope: (item) => {
      const result = getHighLightDifferent(item?.Scope, item.after?.Scope);
      return <div class={styles.highlight} domPropsInnerHTML={result.beforeContent}></div>;
    },
    beforeAddress: (item) => {
      const result = getHighLightDifferent(item?.Address, item.after?.Address);
      return <div class={styles.highlight} domPropsInnerHTML={result.beforeContent}></div>;
    },
    BeforeContent: (item) => {
      const content = JSON.parse(item.BeforeContent);
      const type = content instanceof Array;
      return type ? (
        <QEntityLink coy-arr={JSON.parse(item.BeforeContent)}></QEntityLink>
      ) : (
        <QEntityLink coyObj={JSON.parse(item.BeforeContent)}></QEntityLink>
      );
    },
    AfterContent: (item) => {
      const content = item.AfterContent ? JSON.parse(item.AfterContent) : '';
      const type = content instanceof Array;
      return type ? (
        <QEntityLink coy-arr={JSON.parse(item.AfterContent)}></QEntityLink>
      ) : (
        <QEntityLink coyObj={JSON.parse(item.AfterContent)}></QEntityLink>
      );
    },
    relatedCompanyName: (item: any) => {
      return <QEntityLink coyObj={{ KeyNo: item.relatedCompanyId, Name: item.relatedCompanyName }}></QEntityLink>;
    },
    relatedCompanyNameWithTag: (item: any) => {
      // 历史标签处理，针对黑名单的需要单独做下处理
      const getHistoryRole = () => {
        switch (meta.key) {
          case 'Shareholder':
          case 'ShareholdingRelationship':
            return '历史股东';
          case 'ForeignInvestment':
          case 'InvestorsRelationship':
            return '历史对外投资';
          default:
            return item.role;
        }
      };
      if (item.history) {
        item.role = getHistoryRole();
      }
      return (
        <span>
          <a
            style={{ marginRight: '5px', verticalAlign: 'middle' }}
            href={`/embed/companyDetail?keyNo=${item.relatedCompanyId || item.companyKeynoRelated}&title=${
              item.relatedCompanyName || item.companyNameRelated
            }`}
            target="_blank"
          >
            {item.relatedCompanyName || item.companyNameRelated}
          </a>
          {item.role && item.history ? <QTag>{item.role}</QTag> : null}
        </span>
      );
    },
    companyName: (item: any) => {
      return <QEntityLink coyObj={{ KeyNo: item.companyId, Name: item.companyName }}></QEntityLink>;
    },
    personName: (item: any) => {
      return <QEntityLink coyObj={{ KeyNo: item.personId, Name: item.personName }}></QEntityLink>;
    },
    path: (item: any) => {
      const paths = preDealConnetPath(item);
      if (paths.length > 0) {
        return <AssociationPath paths={[paths]} showTitle></AssociationPath>;
      }
      return '-';
    },
    // FIXME: 临时处理, 重构为 action 传参, 控制 textContent
    annoAction: (item) => {
      return <a onClick={() => vm.showDetail(meta.key, item)}>{item.annoName}</a>;
    },
    action: (item) => {
      if (['HitOuterBlackList', 'OvsSanction'].includes(meta.key) && item.isdetails !== 1) {
        return '-';
      }
      return <a onClick={() => vm.showDetail(meta.key, item)}>详情</a>;
    },
    urlAction: (item) => {
      return <a onClick={() => vm.gotoDetail(meta.key || item.riskid, item)}>详情</a>;
    },
    allAction: (item) => {
      return <a onClick={() => vm.showInfo(meta.key, item)}>详情</a>;
    },
    // 双随机抽查详情
    drcDetail: (data) => {
      return <a onClick={() => vm.$modal.showDimension('drc', { ids: data.Id, keyNo: meta.KeyNo })}>详情</a>;
    },
    // 药品抽检
    medicineDetail: (data) => {
      return <a onClick={() => vm.$modal.showDimension('medicine', { id: data.Id })}>详情</a>;
    },
    // 产品抽查
    productCheckedDetail: (data) => {
      return <a onClick={() => vm.$modal.showDimension('productChecked', { id: data.RiskId, keyNo: data.KeyNo })}>详情</a>;
    },
    // 风险等级
    riskLevel: (level?: number) => {
      if (level === undefined || !RISK_LEVEL_CODE_THEME_MAP[level]) {
        return '-';
      }
      const schema = RISK_LEVEL_CODE_THEME_MAP[level];
      return <QTag style={getRiskLevelStyle(level)}>{schema.label}</QTag>;
    },
    // 案号
    AnNoList: (item) => {
      const url = `/embed/courtCaseDetail?caseId=${item.CaseSearchId || item.Id}&title=${item.CaseName}`;
      return (
        <ClampContent clampKey={item.Id + meta.tkey}>
          <a href={url} target="_blank" style={{ whiteSpace: 'pre' }}>
            {item.AnNoList.join('\n')}
          </a>
        </ClampContent>
      );
    },
    // 最新案件进程
    LastCaseProgress: (item) => {
      return (
        <div>
          <div>{item.LastestDate ? moment(item.LastestDate * 1000).format('YYYY-MM-DD') : '-'}</div>
          <div>{item.LatestTrialRound}</div>
        </div>
      );
    },
    CourtList: (item) => {
      return <div domPropsInnerHTML={item?.CourtList?.join('<br />')}></div>;
    },
    PersonNo: (item) => {
      // 关联人的样式
      const formRelate = () => {
        if (item.relationPersonKeyNo) {
          return (
            <span class="flex items-center" style={{ gap: '5px' }}>
              <a href={`/embed/beneficaryDetail?personId=${item.relationPersonKeyNo}&title=${item.relationPersonName}`} target="_blank">
                {item.relationPersonName}
              </a>
              {'-'}
            </span>
          );
        }
        return (
          <span class="flex items-center" style={{ gap: '5px' }}>
            {item.relationPersonName}
            {'-'}
          </span>
        );
      };
      const getPNO = () => {
        const pNo = item.personNo;
        if (item.relationPersonId && item.relationPersonId !== -1) {
          const name = pNo.split(`_${item.relationship}`)[0];
          return (
            <span class="flex items-center" style={{ gap: '5px' }}>
              <span>{name}</span>
              {'-'}
              {formRelate()}
              <span>{item.name}</span>
              <span>({item.relationship})</span>
            </span>
          );
        }
        return (
          <span>
            {pNo} - {item.name}
          </span>
        );
      };
      return (
        <span class="flex items-center" style={{ gap: '5px' }}>
          {getPNO()}
          {item.status === 1 ? (
            <QTag type="danger" style={{ color: '#666' }}>
              是本人
            </QTag>
          ) : null}
        </span>
      );
    },
    CaseIdentity: (item) => {
      let roleRL: any[] = [];
      if (item.CaseRoleSearch) {
        const arr = JSON.parse(item.CaseRoleSearch);
        if (arr?.length) {
          arr.forEach((role) => {
            if (role.N === vm.meta.keyNo || role.P === vm.meta.companyName) {
              roleRL = role.RL;
            }
          });
        }
        if (roleRL?.length) {
          const roles = roleRL.map((rm) => {
            const ldr = LAWSUIT_RESULT_CODE_MAP[rm.LR];
            return (
              <div>
                {rm.T}
                {rm.R}
                {rm.LR ? <span style={{ ...getColor(ldr) }}>[{ldr}]</span> : null}
              </div>
            );
          });
          if (item.SeriesGroupCount && item.SeriesGroupCount > 1) {
            roles.push(
              <a
                class="m-t-xs ntag-v2 ntag-v2-primary"
                onClick={() =>
                  vm.$modal.showDimension('sameCaseList', {
                    keyNo: vm.companyInfo?.KeyNo,
                    groupId: item.SeriesGroupId,
                    SeriesGroupCount: item.SeriesGroupCount,
                    companyName: vm.companyInfo?.Name,
                    infoId: '630',
                  })
                }
              >
                <span class="text-dark">
                  全部<span class="count">&nbsp;{item.SeriesGroupCount}&nbsp;</span>个系列案件
                </span>
              </a>
            );
          }
          return roles;
        }
      }
      return '-';
    },
    SpotCheck: (item) => {
      const getColorByText = (value: string) => {
        if (value !== '未发现问题') {
          return '#F04040';
        }
        return 'inherit';
      };
      return <span style={{ color: getColorByText(item.punishResult) }}>{item.punishResult || '-'}</span>;
    },
    partyContent: (dataList) => {
      const data = dataList.reduce((acc, cur) => {
        const type = cur.Desc;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(...cur.Items);
        return acc;
      }, {});
      return Object.keys(data).map((key) => {
        return (
          <div>
            <span>{key}：</span>
            {data[key]?.map((detail, index) => {
              return (
                <span>
                  {index !== 0 ? '，' : ''}
                  <q-entity-link coyObj={detail} />
                </span>
              );
            })}
          </div>
        );
      });
    },
    // 当事人
    Parties: (item) => {
      // 获取当事人信息
      const getParties = (_item) => {
        if (_item.caserolegroupbyrolename?.length) {
          return _item.caserolegroupbyrolename
            ?.filter((item2) => item2.LawyerTag === 0 || isNil(item2.LawyerTag))
            ?.flatMap((item2) =>
              item2.DetailList?.map((item3) => ({
                ...item3,
                Role: item2.Role,
                Job: _item.involveRole?.find((role) => role.KeyNo === item3.KeyNo)?.Job,
              }))
            );
        }

        if (_item.caserole?.length) {
          return _item.caserole.map((item2) => ({
            ...item2,
            Role: item2.R,
            Name: item2.P || item2.ShowName,
            KeyNo: item2.N,
            Org: item2.O,
          }));
        }
        return [];
      };
      const parties = [
        ...getParties(item),
        ...(item.involveRole || []).map((role) => ({
          ...role,
          Role: role.Tag,
        })),
      ].reduce((acc, cur) => {
        if (!acc.find((v) => v.KeyNo === cur.KeyNo && v.Role === cur.Role)) {
          acc.push(cur);
        }
        return acc;
      }, []);

      if (!parties?.length) {
        return '-';
      }
      return (
        <ClampContent clampKey={item.id + meta.tkey}>
          {parties.map((data) => {
            return (
              <div>
                <span>
                  <span>{data.Role}</span>
                  <span> - </span>
                  <QSimpleEntityLink value={data} />
                </span>{' '}
                {data.JudgeResultDescription ? (
                  <span v-show={data.JudgeResultDescription} style={{ ...getColor(data.JudgeResultDescription) }}>
                    [{data.JudgeResultDescription}]
                  </span>
                ) : null}
                {data.Job ? (
                  <span
                    style={{
                      display: 'inline-block',
                      lineHeight: '20px',
                      padding: '0px 4px',
                      borderRadius: '2px',
                      ...getStyleByJob(data.Job),
                    }}
                  >
                    {data.Job}
                  </span>
                ) : null}
              </div>
            );
          })}
        </ClampContent>
      );
    },
    // 当事人 RA-1779
    RoleAmt: (item: any) => {
      const entities = item?.RoleAmt || [];
      if (!entities.length) {
        return '-';
      }

      return (
        <ClampContent clampKey={item.Id + meta.tkey}>
          {entities.map((entity: Record<string, any>, index: number) => {
            const findTag = item.involveRole?.find((role) => role.KeyNo === entity.N);
            return (
              <div class="flex" style={{ gap: '5px' }} key={`${entity.N}-${index}`}>
                <span style={{ whiteSpace: 'nowrap' }}>{entity.R}</span>
                <span>-</span>
                <QSimpleEntityLink
                  style="margin-left: 4px;"
                  value={{
                    KeyNo: entity.N,
                    Org: entity.O,
                    Name: entity.P,
                  }}
                />
                {findTag?.Tag ? <QTag type="warning">{findTag.Tag}</QTag> : null}
                {/* 存在多个角色，需要分开展示 */}
                {entity.Job && (
                  <div class={styles.jobbox}>
                    {entity.Job.split(',').map((job) => {
                      return <span class={styles.jobPostion}>{job}</span>;
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </ClampContent>
      );
    },
    caseName: (item: Record<string, any>) => {
      const caseName = item.casename || item.CaseNameClean || item.CaseName;
      const involveTags = item.involveTags || item.CaseTypeArray || item.CaseType || [];
      if (!caseName) {
        return '-';
      }
      return (
        <div>
          <span style={{ marginRight: '4px' }}>{caseName}</span>
          {Array.isArray(involveTags) && involveTags.length > 0
            ? (involveTags || []).map((tagText, index) => {
                return (
                  <QTag style={{ marginTop: '-1px' }} type="default" key={`${tagText}-${index}`}>
                    {tagText}
                  </QTag>
                );
              })
            : null}
        </div>
      );
    },
    controllerAtlasAction: (text, record) => {
      return (
        <Button
          type="link"
          onClick={() => {
            window.open(
              `/embed/charts/actualcontroller?keyNo=${record.companyId}&ac=undefined&companyName=${record.companyName}&rc=1&show=1,3,2,5,6,11,12,7,9,21,4&allcharts=1&title=${record.companyName}`
            );
          }}
        >
          图谱
        </Button>
      );
    },
    detailPath: (scope) => preDealControlPath(scope, meta),
    investmentPath: (record) => {
      if (!record.paths?.length) {
        return '-';
      }
      const pathData = getInvestmentPath({ name: record.operName, keyNo: record.operKeyNo }, record.paths);
      return (
        <ClampContent line={4} clampKey={record.keyNo + meta.tkey}>
          <AssociationPath paths={pathData} forceShowTitle={true}></AssociationPath>
        </ClampContent>
      );
    },

    // 疑似潜在利益冲突（人员）
    interestConflictPerson: (item) => {
      const nodes: VNode[] = [];

      // 相同姓名
      if (item.isSameName) {
        const name = item.name || item.Name;
        const keyNo = item.keyNo || item.KeyNo || '';
        // 需要判断是否为企业
        const isPerson = keyNo.startsWith('p');
        const url = isPerson
          ? `/embed/beneficaryDetail?personId=${keyNo}&title=${name}`
          : `/embed/companyDetail?keyNo=${keyNo}&title=${name}`;

        const node = keyNo ? (
          <a href={url} target="_blank">
            {name}
          </a>
        ) : (
          <span>{name}</span>
        );
        nodes.push(
          <div>
            <span>疑似同名</span>
            <span>（{node}）</span>
          </div>
        );
      }

      // 相同联系方式
      if (item.isSameContact) {
        const contacts: string[] = [];
        if (!isEmpty(item.phones)) {
          const phones = item.phones.map(({ n, t }) => `${n} ${t}`);
          contacts.push(...phones);
        }
        if (!isEmpty(item.emails)) {
          const emails = item.emails.map(({ e }) => e);
          contacts.push(...emails);
        }

        if (!isEmpty(contacts)) {
          nodes.push(
            <div>
              <span>相同联系方式</span>
              <span>（{contacts.join('，')}）</span>
            </div>
          );
        }
      }

      return <div>{nodes}</div>;
    },
    BeforeChange: (scope) => {
      const ChangeExtend = JSON.parse(scope.ChangeExtend)[0];
      const BeforeContent = ChangeExtend.BeforeContent ? JSON.parse(ChangeExtend.BeforeContent) : '';
      if (!BeforeContent) {
        return '-';
      }
      const type = BeforeContent instanceof Array;
      if (type) {
        return (
          <div>
            {BeforeContent.map((content) => {
              return renderBenifit(content);
            })}
          </div>
        );
      }
      return renderBenifit(BeforeContent);
    },

    AfterChange: (scope) => {
      const ChangeExtend = JSON.parse(scope.ChangeExtend)[0];
      const AfterContent = ChangeExtend.AfterContent ? JSON.parse(ChangeExtend.AfterContent) : '';
      if (!AfterContent) {
        return '-';
      }
      const type = AfterContent instanceof Array;
      if (type) {
        return (
          <div>
            {AfterContent.map((content) => {
              return renderBenifit(content);
            })}
          </div>
        );
      }
      return renderBenifit(AfterContent);
    },

    // 国央企采购黑名单
    govProcurementIllegalDetail: (data) => {
      if (data?.newsId) {
        return (
          <a href={`/embed/post-news?newsId=${data.newsId}&title=${data.title || data.Name}`} target="_blank">
            详情
          </a>
        );
      }
      if (data?.id && data.isdetails === 1) {
        return (
          <Button type="link" onClick={() => vm.$modal.showDimension('govProcurementIllegal', { id: data.id })}>
            详情
          </Button>
        );
      }
      return '-';
    },

    /**
     * FIXME: 通过配置统一处理对象所需的字段
     */
    companyNamePascal: (item: any) => {
      return <QEntityLink coyObj={{ KeyNo: item.KeyNo, Name: item.CompanyName }}></QEntityLink>;
    },

    // 政府公告 Detail
    govNoticeDetail: (id?: string, item?: any) => {
      if (!id) {
        return '-';
      }
      return (
        <a href={`/embed/govnoticeDetail?id=${id}&title=${item?.title || ''}`} target="_blank">
          详情
        </a>
      );
    },

    // 终本案件 Detail
    endExecutionCaseDetail: (item) => {
      if (item.CaseSearchId) {
        return (
          <Button type="link" onClick={() => vm.$modal.showDimension('endExecutionCase', { id: item.Id })}>
            {item.CaseNo}
          </Button>
        );
      }
      return item.CaseNo || '-';
    },

    // 股权冻结被执行人数据源更换
    pledgName: (text, record) => {
      return pledgCompanyChange(text, record, 'SubjectInfo');
    },
    // 股权冻结冻结股权标的企业数据源更换
    pledgPledgor: (text, record) => {
      return pledgCompanyChange(text, record, 'PledgorInfo');
    },
    // 内容过长的时候折叠
    clampcontent: (text, row) => {
      return (
        <ClampContent clampKey={row.CreditCode || row.Id}>
          <span domPropsInnerHTML={text}></span>
        </ClampContent>
      );
    },
    // 内容过长的时候折叠
    clampContent: (text, row, _, column) => {
      if (!text) {
        return column.placeholder || '-';
      }
      return (
        <ClampContent clampKey={row.id || row.Id || row.recordId}>
          <span domPropsInnerHTML={text || '-'}></span>
        </ClampContent>
      );
    },
    taxOffice: (list: unknown[]) => {
      if (Array.isArray(list) && list.length > 0) {
        return (
          <div>
            {list.map((el) => (
              <div>
                <q-entity-link coy-obj={el} />
              </div>
            ))}
          </div>
        );
      }
      return '-';
    },
    companyList: (val) => {
      if (!Array.isArray(val) || val.length === 0) {
        return '-';
      }
      return <QEntityLink coy-arr={val} />;
    },
    reviewAndInvestigationName: (record) => {
      if (!record.employeesKeyNo || !record.employeeName) {
        return record.employeeName || '-';
      }
      const index = record.employeeName.indexOf('（');
      const name = index > 0 ? record.employeeName.slice(0, index) : record.employeeName;
      const url = `/embed/beneficaryDetail?personId=${record.employeesKeyNo}&title=${name}`;
      return (
        <div>
          <a href={url} target="_blank">
            {name}
          </a>
          {index > 0 ? record.employeeName.slice(index) : ''}
        </div>
      );
    },
    companyStatus: (text) => {
      if (!text) {
        return '-';
      }
      return <CompanyStatus status={text} ghost />;
    },
    companyNameLowerCamel: (record) => {
      return <QEntityLink coyObj={{ KeyNo: record.keyNo, Name: record.companyName }}></QEntityLink>;
    },
    companyNameRelated: (record) => {
      return <QEntityLink coyObj={{ KeyNo: record.companyKeynoRelated, Name: record.companyNameRelated }}></QEntityLink>;
    },
    companyLowerCamel: (record) => {
      return <QEntityLink coyObj={{ KeyNo: record.keyNo, Name: record.name }}></QEntityLink>;
    },
    benefitPerson: (record) => {
      return <QEntityLink coyObj={{ KeyNo: record.benefitKeyNo, Name: record.benefitName }}></QEntityLink>;
    },
    operPerson: (record) => {
      return <QEntityLink coyObj={{ KeyNo: record.operKeyNo, Name: record.operName }}></QEntityLink>;
    },
    investor: (record) => {
      return <QEntityLink coyObj={{ KeyNo: record.keyNo ?? record.KeyNo, Name: record.stockName ?? record.StockName }}></QEntityLink>;
    },
    violationPerson: (record) => {
      return <QEntityLink coyObj={{ KeyNo: record.markedmankey, Name: record.markedman }}></QEntityLink>;
    },
    relatedTypeDesc: (val, record) => {
      const codeList = record?.relatedTypes || [];
      const textList = record?.relatedTypeDescList || [];
      if (!codeList?.length) {
        return '-';
      }
      return textList.map((item, index) => {
        if (['HasPhone', 'HasAddress'].includes(codeList[index]) && record.contactList?.length) {
          const text = record.contactList.join('、');
          return (
            <div class={styles.overflowText}>
              {item}
              <Tooltip>
                <span style={{ cursor: 'default' }}>（{text}）</span>
                <div slot="title" domPropsInnerHTML={record.contactList.join('<br>')}></div>
              </Tooltip>
            </div>
          );
        }
        return <div>{item}</div>;
      });
    },
    riskType: (record) => {
      if (isArray(record.riskTypeInfos) && record.riskTypeInfos?.length) {
        return (
          record.riskTypeInfos.map((item) => {
            return (
              <CompanyStatus
                class={styles.riskType}
                status={item.riskTypeDesc}
                onClick={() =>
                  vm.openRiskTypeModal({
                    companyName: record.companyNameRelated,
                    keyNo: record.companyKeynoRelated,
                    ...item,
                  })
                }
              ></CompanyStatus>
            );
          }) || '-'
        );
      }
      // 兼容历史数据
      if (!isArray(record.riskTypeDescList) || !record.riskTypeDescList?.length) {
        return '-';
      }
      const data = uniq(record.riskTypeDescList).filter((item) => !isNil(item));
      return data.map((item) => {
        return <CompanyStatus class={styles.riskType} status={item as string}></CompanyStatus>;
      });
    },
    dateRange: (record) => {
      return `${record.startDate || ''} 至${record.endDate ? ` ${record.endDate}` : '今'}`;
    },
    publishDateRange: (record) => {
      const formatter = 'YYYY年MM月DD日';
      return `${getFormattedDate(record.PublicStartDate, formatter)} - ${getFormattedDate(record.PublicEndDate, formatter)}`;
    },
    MonitorDetail: (record) => {
      if (!vm.dynamicDetailJudge(record)) {
        return '-';
      }
      return (
        <Button
          type="link"
          onClick={() => {
            vm.openDimensionDetail({
              ...record,
              companyId: meta.keyNo,
              companyName: meta.companyName,
            });
          }}
        >
          详情
        </Button>
      );
    },
    MonitorContent: (item) => {
      return (
        <ClampContent class={'trends-content'} clampKey={item.recordId} line={5}>
          <span domPropsInnerHTML={getContent(item, 'RiskChange')}></span>
        </ClampContent>
      );
    },
    effectiveTag: () => {
      return <CompanyStatus status="有效" />;
    },
    actualControllerOperation: (record) => {
      return (
        <Button
          type="link"
          onClick={() => {
            vm.showDetail(meta.key, record);
          }}
        >
          详情
        </Button>
      );
    },
    patentInfoStatus: (record) => {
      return <span class={['ntag', hanlePatentStatusColor(record.Status)]}>{record.LegalStatusDesc}</span>;
    },
    caseRole: (role) => {
      if (!role?.length) return '-';
      return role
        .filter((r) => r.KeyNo === meta.keyNo)
        .map((item) => {
          return (
            <div>
              {item.Role}
              <QRoleText roleD={item.LRD} />
            </div>
          );
        });
    },
    judicialCase: (record) => {
      return <JudicialCase record={record} keyNo={meta.keyNo} />;
    },
    UnfulfilledAmt: (list) => {
      if (!list?.length) return '-';
      return list
        .filter((v) => v.KeyNo === meta.keyNo)
        .map((v) => numberToHumanWithUnit(v.FailureAmt))
        .join('\n');
    },
    RoleList: (list) => {
      return <QRoleList list={list} />;
    },
    CaseReason: (val, record) => {
      return (
        <div>
          <span>{val || '-'}</span>
          {record.CaseReasonDescription ? <QGlossaryInfo tooltip={record.CaseReasonDescription} /> : null}
        </div>
      );
    },
  };
};
