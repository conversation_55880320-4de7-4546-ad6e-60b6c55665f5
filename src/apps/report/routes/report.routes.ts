import type { RouteConfig } from 'vue-router';

import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';
import { Permission } from '@/config/permissions.config';
import { APP_MENU_CONFIG } from '@/config/menu.config';

export const reportRoutes = (): RouteConfig[] => [
  {
    path: '/report',
    component: SidebarMenuLayout,
    props: {
      pageTitle: '报告中心',
      menu: APP_MENU_CONFIG,
    },
    meta: {
      title: '报告中心',
    },
    children: [
      {
        path: '',
        redirect: '/report/history',
      },
      {
        path: 'history',
        name: 'report-history',
        component: () => import('../pages/history'),
        meta: {
          title: '报告申请记录',
          // TODO: 加入权限控制
          // permission: [Permission.INVESTIGATION_REPORT],
        },
        // props: () => {
        //   return {
        //     pageType: 'report-history',
        //   };
        // },
      },
    ],
  },
];
