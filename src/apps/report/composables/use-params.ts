import { stateToQuery } from '@/utils/search-transform/company/state-to-query';

export const getParams = (payload: Record<string, any> = {}, pagination = { current: 1, pageSize: 10 }, extra = {}) => {
  const filter = stateToQuery({
    keyword: payload.keywords,
    filters: payload.filters,
  });
  const params = {
    searchKey: payload?.keywords,
    createDate: filter.filter?.sd ? [filter.filter?.sd] : undefined,
    status: filter.filter?.ekc,
    createUsers: payload.filters?.g,
    pageIndex: pagination.current,
    pageSize: pagination.pageSize,
    ...extra,
  };
  return params;
};
