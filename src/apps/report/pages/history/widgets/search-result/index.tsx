import { computed, defineComponent, ref, unref } from 'vue';
import { Button, Icon, Popover, Tooltip } from 'ant-design-vue';
import { differenceBy, escape, uniqBy } from 'lodash';
import { useRoute, useRouter } from 'vue-router/composables';

import QRichTable from '@/components/global/q-rich-table';
import QCard from '@/components/global/q-card';
import QTag from '@/components/global/q-tag';
import SearchCount from '@/components/search-count';
import EmptyWrapper from '@/shared/components/empty-wrapper';
import QEntityLink from '@/components/global/q-entity-link';
import DropdownButtonWrapper from '@/shared/components/dropdown-button-wrapper';

import styles from './search-result.module.less';
import { ADD_MONITOR_OPTIONS, BENEFIT_OPTIONS, DOWNLOAD_OPTIONS } from '../../config';

const BeneTypeMap = BENEFIT_OPTIONS.reduce((map, cur) => {
  map[cur.value] = cur.label;
  return map;
}, {});
const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    pagination: {
      type: Object,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    searchKey: {
      type: String,
      default: '',
    },
    selectedItems: {
      type: Array,
      default: () => [],
    },
    totalCompany: {
      type: Number,
      default: 0,
    },
  },
  emits: ['removeItem', 'selectItems', 'changePage', 'changeSettings', 'refresh', 'openCourtNotice', 'sorterChange'],
  setup(props, { emit }) {
    const selectedIds = ref<string[]>([]);
    const selectedRowsList = ref<any[]>([]);

    const rowSelection = computed(() => ({
      checkStrictly: false,
      selectedRowKeys: selectedIds.value,
      onChange: (selectedRowKeys, selectedRows) => {
        selectedRowsList.value = uniqBy(
          [...differenceBy(unref(selectedRowsList), props.dataSource, props.rowKey), ...selectedRows],
          props.rowKey
        );
        selectedIds.value = selectedRowsList.value.map((item) => item[props.rowKey]);
        emit('selectItems', selectedIds.value);
      },
    }));

    const handlePageChange = (current, pageSize) => {
      emit('changePage', current, pageSize);
    };

    const paginationProps = computed(() => ({
      ...props.pagination,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    }));

    const router = useRouter();
    const route = useRoute();

    const handleGoToDetail = (record) => {
      router.push({
        path: `/risk-monitor/trends/detail/${record.companyId}`,
        params: {
          id: record.companyId,
        },
        query: {
          from: 'trends', // 面包屑导航
          name: route.meta?.title,
          groupId: record.monitorGroupId,
        },
      });
    };

    const handleExport = (key: string) => {
      console.log(key);
    };

    return {
      selectedIds,
      rowSelection,

      paginationProps,

      handleGoToDetail,

      handleExport,
    };
  },
  render() {
    const renderStatus = (status) => {
      return (
        <q-tag type="primary" status={status}>
          {status}
        </q-tag>
      );
    };
    return (
      <QCard bodyStyle={{ padding: '15px' }} class={styles.container}>
        <div slot="title" class="flex items-center">
          <SearchCount
            showSelects={false}
            total={this.pagination.total}
            loading={this.isLoading}
            scopedSlots={{
              message: (content) => {
                return <div class="flex items-center">共找到{content}条结果</div>;
              },
            }}
          />
        </div>
        <div slot="extra">
          <div class={styles.extra}>
            <Tooltip overlayClassName={styles.popTip}>
              <div slot="title">添加至风险监控列表后，新受益人的变动信息将会及时通知你</div>
              <DropdownButtonWrapper
                totalCount={this.pagination.total}
                needPopConfirm={false}
                selectIdlength={this.selectedIds.length}
                menuItems={ADD_MONITOR_OPTIONS}
                onConfirm={this.handleExport}
                class={styles.dropdown}
              >
                <span class={styles.dropdownBtn} slot="btnText">
                  <q-icon type="icon-jiankongicon" />
                  批量添加监控
                </span>
              </DropdownButtonWrapper>
            </Tooltip>
            <DropdownButtonWrapper
              totalCount={100}
              btnText="导出列表"
              needPopConfirm={false}
              selectIdlength={this.selectedIds.length}
              menuItems={DOWNLOAD_OPTIONS}
              onConfirm={this.handleExport}
            >
              <span class={styles.dropdownBtn} slot="btnText">
                <q-icon type="icon-pdf" />
                批量下载报告
              </span>
            </DropdownButtonWrapper>
          </div>
        </div>
        <EmptyWrapper dataSource={this.dataSource} loading={this.isLoading}>
          <QRichTable
            showIndex={false}
            tableLayout="fixed"
            loading={this.isLoading}
            rowKey={this.rowKey}
            dataSource={this.dataSource}
            columns={this.columns}
            pagination={this.paginationProps}
            rowSelection={this.rowSelection}
            onChange={({ sorter }) => {
              this.$emit('sorterChange', sorter);
            }}
            scopedSlots={{
              company: (record) => {
                return <QEntityLink coyObj={{ KeyNo: record.companyId, Name: record.companyName }} />;
              },
              companyStatus: (text) => {
                return (
                  <q-company-status status={text} ghost>
                    {text}
                  </q-company-status>
                );
              },
              recordType: (text) => {
                return renderStatus(text);
              },
              beneType: (type) => {
                return BeneTypeMap[type];
              },
              operateType: (text) => {
                const recordType = text === 'batch' ? '批量识别' : '单一识别';
                return <div class="flex">{renderStatus(recordType)}</div>;
              },
              action: (record) => {
                return (
                  <div class="flex" style="gap: 10px;">
                    <Button type="link">详情</Button>
                    <Button type="link">添加监控</Button>
                    <Button type="link">报告下载</Button>
                  </div>
                );
              },
            }}
          />
        </EmptyWrapper>
      </QCard>
    );
  },
});

export default SearchResult;
