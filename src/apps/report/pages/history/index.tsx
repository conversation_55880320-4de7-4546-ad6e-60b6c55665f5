import { computed, defineComponent, ref } from 'vue';

import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import CommonSearchFilter from '@/components/common/common-search-filter';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';

import { useSearchFilter } from './hooks/use-search-filter';
import SearchResult from './widgets/search-result';
import { TABLE_COLUMNS } from './config';

const ReportHistoryPage = defineComponent({
  name: 'ReportHistoryPage',
  setup() {
    const {
      handleFilterChange,
      handleFilterReset,
      getFilterOptions,
      search,
      filterGroups,
      filterValues,
      isLoading,
      totalCompany,
      dataSource,
      pagination,
      isInit,
      sortInfo,
    } = useSearchFilter();

    /** 表格配置 */
    const tableColumns = computed(() => {
      return TABLE_COLUMNS;
    });

    const selectIds = ref([]);

    return {
      filterValues,
      filterGroups,
      handleFilterChange,
      handleFilterReset,
      getFilterOptions,
      search,
      isLoading,
      isInit,
      dataSource,
      pagination,
      sortInfo,
      selectIds,

      tableColumns,
      totalCompany,
    };
  },
  render() {
    return (
      <div>
        <HeroicLayout loading={this.isInit}>
          {/* Filter */}
          <QCard
            slot="hero"
            title={this.$route.meta?.title}
            bodyStyle={{
              paddingTop: 0,
            }}
          >
            <CommonSearchFilter
              placeholder="请输入企业名称"
              isRemoteSearch={true}
              filterConfig={this.filterGroups}
              onChange={this.handleFilterChange}
              defaultValue={this.filterValues}
              onReset={this.handleFilterReset}
            />
          </QCard>

          {/* 搜索结果 */}
          <SearchResult
            totalCompany={this.totalCompany}
            isLoading={this.isLoading}
            rowKey="id"
            searchKey={this.filterValues.keywords}
            columns={this.tableColumns}
            dataSource={this.dataSource}
            pagination={this.pagination}
            on={{
              changePage: (pageIndex: number, pageSize: number) => {
                this.search({ pageIndex, pageSize });
              },
              refresh: () => {
                this.search();
              },
              sorterChange: (sorter) => {
                this.pagination.current = 1;
                this.sortInfo = convertSortStructure(sorter);
                this.search();
              },
              selectItems: (ids) => {
                this.selectIds = ids;
              },
            }}
          />
        </HeroicLayout>
      </div>
    );
  },
});

export default ReportHistoryPage;
