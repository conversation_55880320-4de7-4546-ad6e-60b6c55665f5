import { computed, defineComponent, onMounted, reactive, ref } from 'vue';
import { debounce, pick } from 'lodash';

import QCard from '@/components/global/q-card';
import HeroicLayout from '@/shared/layouts/heroic';
import CommonSearchFilter from '@/components/common/common-search-filter';
import { batchImport as batchService, user as userService } from '@/shared/services';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';
import { useRoomSocket } from '@/hooks/use-room-socket/use-room-socket';
import { useFetchState } from '@/hooks/use-fetch-state';

import SearchResult from './widgets/search-result';
import { getConfig, TASK_CONFIG_MAP } from './config';

const TaskPage = defineComponent({
  name: 'TaskPage',
  props: {
    pageType: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const init = ref(true);

    const pagination = reactive({
      pageSize: 10,
      current: 1,
      total: 0,
    });

    const setting = TASK_CONFIG_MAP[props.pageType];

    const operatorList = ref<{ label: string; value: number; name: string; userId: number }[]>([]);

    const sort = ref({});

    const filterGroups = computed(() => getConfig(pick(setting, ['taskField', 'taskStatus']), operatorList.value) as Array<any>);

    // 任务类型
    const businessTypes = computed(() => filterGroups.value[0].children[0].options.map((item) => item.value));
    const filters = ref<{ filters: any; keywords: string }>();

    const filterParams = computed(() => {
      return {
        ...(filters.value?.filters || {}),
        businessType: filters.value?.filters?.businessType || businessTypes.value,
        searchKey: filters.value?.keywords,
        createDate: filters.value?.filters?.createDate ? [filters.value?.filters?.createDate] : undefined,
      };
    });

    const getOperatorList = async () => {
      const data: { name: string; userId: number }[] = await userService.getUserList();
      operatorList.value = data.map((v) => ({ ...v, label: v.name, value: v.userId }));
    };

    const fetchData = (data) => {
      return batchService
        .search({
          batchTypes: setting.batchTypes,
          pageIndex: pagination.current,
          pageSize: pagination.pageSize,
          ...filterParams.value,
          ...data,
          ...sort.value,
        })
        .then((res) => {
          pagination.total = res.total;
          pagination.current = res.pageIndex;
          pagination.pageSize = res.pageSize;
          return res;
        });
    };
    const { execute, result, isLoading } = useFetchState(fetchData);

    const socketUpdater = debounce(() => {
      execute();
    }, 500);

    useRoomSocket('/insights/socket', {
      eventType: 'SystemMessage',
      filter: (messageData: any) => {
        return setting.batchTypes.includes(messageData.batchType);
      },
      refresh: socketUpdater,
    });
    const handleFilterChange = (payload) => {
      filters.value = payload;
      pagination.current = 1;
      execute();
    };

    const handleSortChange = (sorter) => {
      sort.value = convertSortStructure(sorter);
      pagination.current = 1;
      execute();
    };

    const handlePageChange = (pageIndex, pageSize) => {
      execute({
        pageIndex,
        pageSize,
      });
    };

    onMounted(async () => {
      await getOperatorList();
      await execute();
      init.value = false;
    });

    return {
      init,
      filters,
      filterGroups,
      pagination,
      setting,
      handleFilterChange,
      handlePageChange,
      handleSortChange,
      result,
      isLoading,
      operatorList,
    };
  },
  render() {
    return (
      <HeroicLayout loading={this.init}>
        <QCard
          slot="hero"
          title={this.setting.title}
          bodyStyle={{
            paddingTop: 0,
          }}
        >
          <CommonSearchFilter
            placeholder="请输入任务名称"
            filterConfig={this.filterGroups}
            onChange={this.handleFilterChange}
            defaultValue={this.filters}
          />
        </QCard>
        <SearchResult
          rowKey={'batchId'}
          searchKey={this.filters?.keywords}
          columns={this.setting.columns as any[]}
          dataSource={this.result?.data || []}
          pagination={this.pagination}
          isLoading={this.isLoading}
          onChangePage={this.handlePageChange}
          onSortChange={this.handleSortChange}
          operatorList={this.operatorList}
        ></SearchResult>
      </HeroicLayout>
    );
  },
});

export default TaskPage;
