import { computed, defineComponent, PropType, ref } from 'vue';
import { escape } from 'lodash';
import { Button, Tooltip } from 'ant-design-vue';

import QRichTable, { IQRichTableColumn } from '@/components/global/q-rich-table';
import QCard from '@/components/global/q-card';
import SearchCount from '@/components/search-count';
import ClampContent from '@/components/clamp-content';

import styles from './search-result.module.less';
import { TaskStatus } from '../../config';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array as PropType<Array<IQRichTableColumn>>,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    pagination: {
      type: Object,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    scroll: {
      type: Object as PropType<{ x?: string | boolean | number; y?: string | boolean | number }>,
      required: false,
    },
    operatorList: {
      type: Array as PropType<Array<{ userId: string | number; name: string }>>,
      required: true,
    },
    searchKey: {
      type: String,
      required: false,
    },
  },
  emits: ['changePage', 'sortChange'],
  setup(props, { emit }) {
    const selectedIds = ref([]);

    const rowSelection = computed(() => ({
      checkStrictly: false,
      selectedRowKeys: selectedIds.value,
      onChange: (selectedRowKeys) => {
        selectedIds.value = selectedRowKeys;
      },
    }));

    const handlePageChange = (current, pageSize) => {
      emit('changePage', current, pageSize);
    };

    const paginationProps = computed(() => ({
      ...props.pagination,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    }));

    return {
      selectedIds,
      rowSelection,
      paginationProps,
    };
  },
  render() {
    return (
      <QCard
        bodyStyle={{
          paddingBottom: '15px',
        }}
      >
        <div slot="title">
          <SearchCount
            slot="title"
            showSelects={true}
            total={this.pagination.total}
            loading={this.isLoading}
            selectedIds={this.selectedIds}
          />
        </div>
        <QRichTable
          loading={this.isLoading}
          scroll={this.scroll}
          rowKey={this.rowKey}
          dataSource={this.dataSource}
          columns={this.columns}
          emptySize={'100px'}
          emptyMinHeight={'calc(100vh - 52px - 40px - 109px - 10px - 50px - 30px - 40px - 15px )'}
          pagination={this.paginationProps}
          // rowSelection={this.rowSelection}
          onChange={({ sorter }) => {
            this.$emit('sortChange', sorter);
          }}
          scopedSlots={{
            reportName: (fileName) => {
              if (!fileName) return '-';
              const noFileType = fileName.split('.')[0];
              let name = escape(noFileType);
              if (this.searchKey) {
                name = name.replaceAll(this.searchKey, `<em>${this.searchKey}</em>`);
              }
              return <div class={styles.emphasis} domPropsInnerHTML={name}></div>;
            },
            taskFileName: (item) => {
              if (!item.fileName) return '-';
              const name = escape(item.fileName);
              if (this.searchKey) {
                name.replaceAll(this.searchKey, `<em>${this.searchKey}</em>`).replaceAll(',', ';<br>');
              }
              const link = item.originFile || item.detailFile;
              if (link) {
                return (
                  <a
                    onClick={() => {
                      const iframe = document.createElement('iframe');
                      iframe.style.display = 'none';
                      iframe.src = link; // downloadUrl是文件的下载链接
                      document.body.appendChild(iframe);
                      setTimeout(() => {
                        iframe.remove();
                      }, 500);
                    }}
                    class={[styles.taskFileName, styles.emphasis]}
                  >
                    <div class="flex items-center" style={{ gap: '5px' }}>
                      <q-icon style={{ fontSize: '18px' }} type="icon-excel"></q-icon>
                      <div domPropsInnerHTML={name}></div>
                    </div>
                  </a>
                );
              }
              return (
                <ClampContent class={styles.emphasis} clampKey={`${item.batchId}'taskFileName'`} line={3}>
                  <div domPropsInnerHTML={name}></div>
                </ClampContent>
              );
            },
            taskStatistics: (item) => {
              const textContent = [
                `导入成功${item.statisticsInfo.recordCount ?? 0}`,
                `重复${item.statisticsInfo.duplicatedCount ?? 0}`,
                `错误${item.statisticsInfo.errorCount ?? 0}`,
                `监控成功${item.statisticsInfo.successCount ?? 0}`,
                `消耗额度${item.statisticsInfo.paidCount ?? 0}`,
              ].join('/');
              return (
                <div class={styles.statistics}>
                  {/*<QIcon type="icon-a-shapeshangwu101842" class="mr-1" />*/}
                  {item.status === 2 && item.resultFile ? <a href={`${item.resultFile}`}>{textContent}</a> : <span>{textContent}</span>}
                </div>
              );
            },
            fileDownload: (item) => {
              if (!item.fileName) return '-';
              const name = escape(item.fileName);
              if (this.searchKey) {
                name.replaceAll(this.searchKey, `<em>${this.searchKey}</em>`).replaceAll(',', ';<br>');
              }
              const link = item.originFile || item.detailFile;
              return (
                <div class={styles.downloadWrapper}>
                  <div class="flex items-center" style={{ gap: '4px' }}>
                    {link ? <q-icon style={{ fontSize: '18px' }} type="icon-excel"></q-icon> : null}
                    <div domPropsInnerHTML={name}></div>
                  </div>
                  {link ? (
                    <Tooltip title="下载文件">
                      <a href={link} class={styles.downloadIcon}>
                        <q-icon type="icon-a-xiazaidaochushujuicon"></q-icon>
                      </a>
                    </Tooltip>
                  ) : null}
                </div>
              );
            },
            taskStatus: (item) => {
              const status: any = (TaskStatus as Array<any>).find((op) => Number(op.value) === Number(item.status));
              if (!status) return '-';
              switch (item.status) {
                case 0:
                  return <span class={styles.inWait}>{status.label}</span>;
                case 1:
                  return <span class={styles.inProgress}>{status.label}</span>;
                case 2:
                  return <span class={styles.success}>{status.label}</span>;
                case 3:
                  return <span class={styles.failed}>{status.label}</span>;
                default:
                  return '-';
              }
            },
            taskAction: (item) => {
              return (
                <div class="flex" style={{ gap: '10px' }}>
                  <a href={item.previewUrl} v-show={item.previewUrl} target="_blank" disabled={item.status !== 2}>
                    预览
                  </a>
                  <a href={item.detailFile} download={`${item.fileName.split('.')[0]}`} disabled={item.status !== 2}>
                    下载
                  </a>
                </div>
              );
            },
            importAction: (item) => {
              return (
                <Button
                  type="link"
                  disabled={item.status !== 2}
                  onClick={() => {
                    this.$router.push({
                      name: 'import-task-detail',
                      params: {
                        batchId: item.batchId,
                      },
                      query: {
                        moduleType: 'monitor',
                      },
                    });
                  }}
                >
                  导入结果
                </Button>
              );
            },
            operator: (userId) => {
              return this.operatorList.find((v) => v.userId === userId)?.name || '-';
            },
            detailAction: (record) => {
              if (record.status === 2) {
                const urlObj = this.$router.resolve({
                  path: `/identity-verification/history/detail/${record.batchId}`,
                  query: {
                    type: 'batch',
                  },
                });
                return (
                  <Button
                    type="link"
                    onClick={() => {
                      window.open(urlObj.href, '_blank');
                    }}
                  >
                    详情
                  </Button>
                );
              }
              return '-';
            },
          }}
        />
      </QCard>
    );
  },
});

export default SearchResult;
