import { DEFAULT_DATE_RANGE } from '@/config/tender.config';

export const getFilterGroups = ({ operators }) => {
  return [
    {
      field: 'filters',
      label: '筛选条件',
      type: 'groups',
      children: [
        {
          field: 'operatorId',
          type: 'multiple',
          label: '申请人',
          options: operators,
          meta: {
            showFooterButton: true,
          },
        },
        {
          field: 'createDate',
          type: 'single',
          label: '申请时间',
          options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
          custom: {
            type: 'date-range',
          },
        },
      ],
    },
  ];
};

export const ReportTaskStatus = [
  { value: 0, label: '生成中', color: '#ff8900' },
  { value: 1, label: '已完成', color: '#00ad65' },
  { value: 2, label: '生成失败', color: '#f04040' },
];

export const TABLE_COLUMNS = [
  {
    title: '主体名称',
    width: 448,
    scopedSlots: {
      customRender: 'company',
    },
  },
  {
    title: '报告状态',
    width: 120,
    dataIndex: 'status',
    scopedSlots: {
      customRender: 'reportTaskStatus',
    },
  },
  {
    title: '申请人',
    width: 100,
    dataIndex: 'operatorId',
    scopedSlots: {
      customRender: 'reporter',
    },
  },
  {
    title: '申请时间',
    width: 180,
    sorter: true,
    dataIndex: 'createDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '生成时间',
    width: 180,
    sorter: true,
    dataIndex: 'updateDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '操作',
    width: 92,
    scopedSlots: {
      customRender: 'action',
    },
  },
];
