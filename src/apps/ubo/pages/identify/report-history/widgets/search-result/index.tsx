import { computed, defineComponent, PropType, ref, unref } from 'vue';
import { Button } from 'ant-design-vue';
import { differenceBy, uniqBy } from 'lodash';
import { useRoute, useRouter } from 'vue-router/composables';

import QRichTable from '@/components/global/q-rich-table';
import QCard from '@/components/global/q-card';
import SearchCount from '@/components/search-count';
import EmptyWrapper from '@/shared/components/empty-wrapper';
import QEntityLink from '@/components/global/q-entity-link';

import styles from './search-result.module.less';
import { ReportTaskStatus } from '../../config';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    pagination: {
      type: Object,
      required: true,
    },
    isLoading: {
      type: <PERSON>olean,
      default: false,
    },
    operatorList: {
      type: Array as PropType<{ label: string; value: number }[]>,
      default: () => [],
    },
  },
  emits: ['changePage', 'sorterChange'],
  setup(props, { emit }) {
    const handlePageChange = (current, pageSize) => {
      emit('changePage', current, pageSize);
    };
    const paginationProps = computed(() => ({
      ...props.pagination,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    }));

    return {
      paginationProps,
    };
  },
  render() {
    return (
      <QCard bodyStyle={{ padding: '15px' }} class={styles.container}>
        <div slot="title" class="flex items-center">
          <SearchCount
            showSelects={false}
            total={this.pagination.total}
            loading={this.isLoading}
            scopedSlots={{
              message: (content) => {
                return <div class="flex items-center">共找到{content}条结果</div>;
              },
            }}
          />
        </div>
        <EmptyWrapper dataSource={this.dataSource} loading={this.isLoading}>
          <QRichTable
            showIndex={false}
            tableLayout="fixed"
            loading={this.isLoading}
            rowKey={this.rowKey}
            dataSource={this.dataSource}
            columns={this.columns}
            pagination={this.paginationProps}
            // rowSelection={this.rowSelection}
            onChange={({ sorter }) => {
              this.$emit('sorterChange', sorter);
            }}
            scopedSlots={{
              company: (record) => {
                return <QEntityLink coyObj={{ KeyNo: record.companyId, Name: record.companyName }} />;
              },
              reporter: (reportId?: number) => {
                const operator = this.operatorList.find((op) => op.value === reportId);
                if (!operator) {
                  return '-';
                }
                return operator.label;
              },
              reportTaskStatus: (statusCode: number) => {
                const status = ReportTaskStatus.find((op) => Number(op.value) === Number(statusCode));
                if (!status) {
                  return '-';
                }
                return (
                  <div class="flex items-center gap-5px">
                    <span class="rounded-full w-6px h-6px" style={{ background: status.color }}></span>
                    <span>{status.label}</span>
                  </div>
                );
              },
              action: (record) => {
                return (
                  <div class="flex" style="gap: 10px;">
                    <Button type="link" href={record.reportPdfUrl} target="_blank" disabled={!record.reportPdfUrl}>
                      预览
                    </Button>
                    <Button type="link" href={record.reportWordUrl} target="_blank" disabled={!record.reportWordUrl}>
                      下载
                    </Button>
                  </div>
                );
              },
            }}
          />
        </EmptyWrapper>
      </QCard>
    );
  },
});

export default SearchResult;
