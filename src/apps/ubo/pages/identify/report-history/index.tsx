import { computed, defineComponent, ref } from 'vue';

import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import CommonSearchFilter from '@/components/common/common-search-filter';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';

import { useSearchFilter } from './hooks/use-search-filter';
import SearchResult from './widgets/search-result';
import { TABLE_COLUMNS } from './config';

const ReportHistoryPage = defineComponent({
  name: 'ReportHistoryPage',
  setup() {
    const {
      handleFilterChange,
      handleFilterReset,
      search,
      filterGroups,
      filterValues,
      isLoading,
      dataSource,
      pagination,
      isInit,
      sortInfo,
      operatorList,
    } = useSearchFilter();

    const handleSorterChange = (sorter) => {
      pagination.value.current = 1;
      sortInfo.value = convertSortStructure(sorter);
      search();
    };

    return {
      filterValues,
      filterGroups,
      handleFilterChange,
      handleFilterReset,
      search,
      isLoading,
      isInit,
      dataSource,
      pagination,
      sortInfo,

      operatorList,
      handleSorterChange,
    };
  },
  render() {
    return (
      <div>
        <HeroicLayout loading={this.isInit}>
          {/* Filter */}
          <QCard
            slot="hero"
            title={this.$route.meta?.title}
            bodyStyle={{
              paddingTop: 0,
            }}
          >
            <CommonSearchFilter
              placeholder="请输入企业名称"
              isRemoteSearch={true}
              filterConfig={this.filterGroups}
              onChange={this.handleFilterChange}
              defaultValue={this.filterValues}
              onReset={this.handleFilterReset}
            />
          </QCard>

          {/* 搜索结果 */}
          <SearchResult
            isLoading={this.isLoading}
            rowKey="id"
            searchKey={this.filterValues.keywords}
            columns={TABLE_COLUMNS}
            dataSource={this.dataSource}
            pagination={this.pagination}
            operatorList={this.operatorList}
            on={{
              changePage: (pageIndex: number, pageSize: number) => {
                this.search({ pageIndex, pageSize });
              },
              refresh: this.search,
              sorterChange: this.handleSorterChange,
            }}
          />
        </HeroicLayout>
      </div>
    );
  },
});

export default ReportHistoryPage;
