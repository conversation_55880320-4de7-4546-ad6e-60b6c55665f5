import { ref, computed, onMounted, reactive } from 'vue';
import { cloneDeep } from 'lodash';

import { useRequest } from '@/shared/composables/use-request';
import { identify as identifyService, user as userService } from '@/shared/services';

import { getFilterGroups } from '../config';

type FilterOptions = {
  label: string;
  value: string | number;
  count?: number;
};

export const useSearchFilter = () => {
  const searchHistory = useRequest(identifyService.searchReportHistory);

  /** 初始化状态 */
  const isInit = ref(false);

  /** 表格数据 */
  const dataSource = computed(() => {
    const data = searchHistory.data.value || {};
    return data.data || [];
  });

  /** 分页信息 */
  const pagination = ref({
    pageSize: 10,
    current: 1,
    total: 0,
  });

  /** 搜索条件 */
  const defaultFilterValues = Object.freeze({
    keywords: undefined,
    filters: {},
  });
  const filterValues = ref<Record<string, any>>(cloneDeep(defaultFilterValues));

  /** 排序 */
  const sort = ref({});

  const isFilterLoading = ref(false);
  const filterOptions = reactive<{
    operators: FilterOptions[];
  }>({
    operators: [],
  });

  /** 更新搜索选项卡 */
  const getFilterOptions = (group?, showAll = false) => {};
  /** 搜索过滤配置 */
  const filterGroups = computed(() => {
    return getFilterGroups(filterOptions);
  });

  const search = async (payload?: Record<string, any>) => {
    const res = await searchHistory.execute({
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
      companyName: filterValues.value?.keywords,
      ...filterValues.value.filters, // operatorId, createDate
      createDate: filterValues.value.filters?.createDate ? [filterValues.value.filters.createDate] : undefined, // 时间区间字段特殊处理
      ...sort.value,
      ...payload,
    });
    pagination.value.total = res?.total || 0;
    pagination.value.current = res?.pageIndex || 1;
    pagination.value.pageSize = res?.pageSize || 10;
  };

  /** 更新搜索过滤 */
  const handleFilterChange = async (values) => {
    filterValues.value = values;
    pagination.value.current = 1;
    await search();
  };

  /** 重置搜索过滤 */
  const handleFilterReset = () => {
    filterValues.value = cloneDeep(defaultFilterValues);
  };

  /** 人员列表 */
  const operatorList = ref<{ label: string; value: number }[]>([]);
  const fetchOperatorList = async () => {
    try {
      const data = await userService.getUserList();
      operatorList.value = data.map((item) => ({
        label: item.name,
        value: item.userId,
      }));
    } catch (error) {
      console.error(error);
    }
  };
  // 同步FilterOptions
  const getOperatorList = async () => {
    await fetchOperatorList();
    filterOptions.operators = operatorList.value;
  };

  /** 初始化 */
  onMounted(async () => {
    try {
      isInit.value = true;
      await getOperatorList();
      await search();
    } catch (error) {
      console.error(error);
    } finally {
      isInit.value = false;
    }
  });

  return {
    isInit,
    isLoading: searchHistory.isLoading,

    search,
    filterValues,
    filterGroups,
    handleFilterChange,
    handleFilterReset,
    isFilterLoading,
    getFilterOptions,
    dataSource,
    pagination,
    sortInfo: sort,

    operatorList,
  };
};
