import { DEFAULT_DATE_RANGE } from '@/config/tender.config';

export const DOWNLOAD_OPTIONS = [
  {
    label: '下载选中',
    key: 'exportByIds',
  },
  {
    label: '下载全部',
    key: 'all',
  },
];
export const ADD_MONITOR_OPTIONS = [
  {
    label: '监控选中',
    key: 'exportByIds',
  },
  {
    label: '监控全部',
    key: 'all',
  },
];

export const BENEFIT_OPTIONS = [
  {
    label: '豁免识别',
    value: 'E',
  },
  {
    label: '简易识别',
    value: 'S',
  },
  {
    label: '正常识别',
    value: 'N',
  },
];

const Record_Options = [
  {
    label: '无需备案',
    value: '无需备案',
  },
  {
    label: '需要备案',
    value: '需要备案',
  },
  {
    label: '暂不涉及',
    value: '暂不涉及',
  },
  {
    label: '承诺免报',
    value: '承诺免报',
  },
];
export const getFilterGroups = ({ operators }) => {
  return [
    {
      field: 'filters',
      label: '筛选条件',
      type: 'groups',
      children: [
        {
          field: 'operatorId',
          type: 'multiple',
          label: '操作人员',
          options: operators.map((op) => ({ label: op.name, value: op.userId })),
          meta: {
            showFooterButton: true,
          },
        },
        {
          field: 'beneType',
          type: 'multiple',
          label: '识别结果',
          options: BENEFIT_OPTIONS,
          meta: {
            showFooterButton: true,
          },
        },
        {
          field: 'recordType',
          type: 'multiple',
          label: '备案类型',
          options: Record_Options,
          meta: {
            showFooterButton: true,
          },
        },
        {
          field: 'createDate',
          type: 'single',
          label: '更新时间',
          options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
          custom: {
            type: 'date-range',
          },
        },
      ],
    },
  ];
};

export const TABLE_COLUMNS = [
  {
    title: '企业名称',
    width: 194,
    scopedSlots: {
      customRender: 'company',
    },
  },
  {
    title: '登记状态',
    width: 80,
    dataIndex: 'companyStatus',
    scopedSlots: {
      customRender: 'companyStatus',
    },
  },
  {
    title: '备案类型',
    width: 100,
    dataIndex: 'recordType',
    scopedSlots: {
      customRender: 'recordType',
    },
  },
  {
    title: '识别结果',
    dataIndex: 'beneType',
    width: 100,
    scopedSlots: {
      customRender: 'beneType',
    },
  },
  {
    title: '操作记录',
    width: 100,
    dataIndex: 'operateType',
    scopedSlots: {
      customRender: 'operateType',
    },
  },
  {
    title: '操作人名',
    width: 100,
    scopedSlots: {
      customRender: 'opUserName',
    },
  },
  {
    title: '操作时间',
    width: 160,
    dataIndex: 'createDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    title: '操作',
    width: 182,
    scopedSlots: {
      customRender: 'action',
    },
  },
];
