import { computed, defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router/composables';
import { message } from 'ant-design-vue';

import HeroicLayout from '@/shared/layouts/heroic';
import QCard from '@/components/global/q-card';
import CommonSearchFilter from '@/components/common/common-search-filter';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';
import { identify } from '@/shared/services';

import { useSearchFilter } from './hooks/use-search-filter';
import SearchResult from './widgets/search-result';
import { TABLE_COLUMNS } from './config';

const RiskMonitorTrendsPage = defineComponent({
  name: 'RiskMonitorTrendsPage',
  setup() {
    const {
      handleFilterChange,
      handleFilterReset,
      search,
      filterGroups,
      filterValues,
      isLoading,
      dataSource,
      pagination,
      isInit,
      sortInfo,
    } = useSearchFilter();

    /** 表格配置 */
    const tableColumns = computed(() => {
      return TABLE_COLUMNS;
    });

    const selectItems = ref([]);

    const router = useRouter();
    const handleGoToDetail = (record) => {
      const href = router.resolve({
        path: `history/${record.companyId}`,
        query: {
          name: record.companyName,
          snapshotId: record.snapshotId,
        },
      }).href;
      window.open(href, '_blank');
    };

    const handleDownloadPdf = async (snapshotId: string) => {
      try {
        await identify.createPdf({
          snapshotId,
        });
        message.success('报告生成中，请耐心等待');
      } catch (error) {
        console.log(error);
        message.success('报告生成失败，请稍后再试');
      }
    };

    return {
      filterValues,
      filterGroups,
      isLoading,
      isInit,
      dataSource,
      pagination,
      sortInfo,
      selectItems,
      tableColumns,

      search,
      handleFilterChange,
      handleFilterReset,
      handleGoToDetail,
      handleDownloadPdf,
    };
  },
  render() {
    return (
      <div>
        <HeroicLayout loading={this.isInit}>
          {/* Filter */}
          <QCard
            slot="hero"
            title={this.$route.meta?.title}
            bodyStyle={{
              paddingTop: 0,
            }}
          >
            <CommonSearchFilter
              placeholder="请输入企业名称"
              filterConfig={this.filterGroups}
              onChange={this.handleFilterChange}
              defaultValue={this.filterValues}
              onReset={this.handleFilterReset}
            />
          </QCard>

          {/* 搜索结果 */}
          <SearchResult
            isLoading={this.isLoading}
            rowKey="snapshotId"
            searchKey={this.filterValues.keywords}
            columns={this.tableColumns}
            dataSource={this.dataSource}
            pagination={this.pagination}
            on={{
              changePage: (pageIndex: number, pageSize: number) => {
                this.search({ pageIndex, pageSize });
              },
              refresh: () => {
                this.search();
              },
              sorterChange: (sorter) => {
                this.pagination.current = 1;
                this.sortInfo = convertSortStructure(sorter);
                this.search();
              },
              selectItems: (rows) => {
                this.selectItems = rows;
              },
              jumpDetail: this.handleGoToDetail,
              downLoad: this.handleDownloadPdf,
            }}
          />
        </HeroicLayout>
      </div>
    );
  },
});

export default RiskMonitorTrendsPage;
