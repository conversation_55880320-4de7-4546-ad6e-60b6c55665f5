.container {
  .extra {
    display: flex;
    gap: 12px;

    .dropdownBtn {
      display: flex;
      align-items: center;

      :global {
        i {
          margin-right: 5px;
          font-size: 16px;
        }
      }
    }

    .dropdown {
      border:1px solid #128bed;
      cursor: pointer;
      color: #128bed;
      padding: 0 12px;
      height: 32px;
      line-height: 32px;
      background-color: #f2f8fe;

      &:hover {
        background-color: #E2F1FD;
      }
    }

    .disabled{
      cursor: not-allowed;
      color: #ccc;
      padding: 0 12px;
      height: 32px;
      line-height: 32px;
      border:1px solid #eee;
      background-color: #fff;

      &:hover{
        background-color: #fff;
      }
    }
  }
}

.popTip{
  :global{
    .ant-tooltip-content,
    .ant-tooltip-inner{
      background-color: #ffff;
      color: #333;
    }

    .ant-tooltip-arrow{
      width: 8px;
      height: 8px;

      &::before{
        width: 9px;
        height: 9px;
        background: #fff;
      }
    }
  }
}
