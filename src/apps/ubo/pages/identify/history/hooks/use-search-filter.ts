import { ref, computed, onMounted, reactive } from 'vue';
import { cloneDeep, isEqual } from 'lodash';

import { useRequest } from '@/shared/composables/use-request';
import { identify as identifyService } from '@/shared/services';

import { getFilterGroups } from '../config';
import { useStore } from '@/store';

type FilterOptions = {
  label: string;
  value: string | number;
  count?: number;
};

export const useSearchFilter = () => {
  const searchHistory = useRequest(identifyService.getVerificationRecord);

  const defaultFilterValues = Object.freeze({
    keywords: undefined,
    filters: {},
  });

  const isInit = ref(false);

  /** 表格数据 */
  const dataSource = computed(() => {
    const data = searchHistory.data.value || {};
    return data.data || [];
  });
  const store = useStore();

  const operatorList = computed(() => store.getters['user/personList']);
  const pagination = ref({
    pageSize: 10,
    current: 1,
    total: 0,
  });

  const previewQuery = ref<Record<string, any>>({});

  const query = ref<Record<string, any>>(cloneDeep(defaultFilterValues));

  const sort = ref({});

  const isFilterLoading = ref(false);

  /** 搜索过滤配置 */
  const filterGroups = computed(() => {
    return getFilterGroups({ operators: operatorList.value });
  });

  const search = async (payload?: Record<string, any>) => {
    const res = await searchHistory.execute({
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
      ...query.value.filters,
      companyName: query.value.keywords,
      createDate: query.value.filters?.createDate ? [query.value.filters.createDate] : undefined,
      ...sort.value,
      ...payload,
    });
    previewQuery.value = cloneDeep(query.value);
    pagination.value.total = res?.total > 50000 ? 50000 : res?.total;
    pagination.value.current = res?.pageIndex;
    pagination.value.pageSize = res?.pageSize;
  };

  /** groupId改变时，重置其他筛选项 */
  const resetOtherFilters = (remainedKeys: string[], currentFilters = {}) => {
    const filters = remainedKeys.reduce((acc, key) => {
      acc[key] = currentFilters[key];
      return acc;
    }, {});
    return {
      ...defaultFilterValues,
      filters,
    };
  };

  /** 更新搜索过滤 */
  const handleFilterChange = async (values) => {
    const { filters } = values;
    if (!isEqual(filters?.groupId, previewQuery.value.filters?.groupId)) {
      query.value = resetOtherFilters(['groupId'], filters);
    } else {
      query.value = values;
    }
    pagination.value.current = 1;
    await search();
    isFilterLoading.value = false;
  };

  /** 重置搜索过滤 */
  const handleFilterReset = () => {
    query.value = {
      ...defaultFilterValues,
    };
  };
  /** 初始化 */
  onMounted(async () => {
    try {
      await search();
      store.dispatch('user/fetchPersonList');
    } catch (error) {
      console.error(error);
    }
  });

  return {
    filterValues: query,
    filterGroups,
    handleFilterChange,
    handleFilterReset,
    isFilterLoading,
    isLoading: searchHistory.isLoading,
    dataSource,
    pagination,
    search,
    isInit,
    sortInfo: sort,
  };
};
