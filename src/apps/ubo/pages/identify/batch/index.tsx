import qs from 'querystring';
import { defineComponent, ref, computed, provide, nextTick, onMounted, reactive, unref } from 'vue';
import { Spin, message } from 'ant-design-vue';
import { useRouter } from 'vue-router/composables';
import { cloneDeep } from 'lodash';

import { identify } from '@/shared/services';
import HeroicLayout from '@/shared/layouts/heroic';
import BatchUpload from '@/apps/ubo/pages/identify/batch/widgets/batch-upload';
import { objectValuesToNumeric } from '@/utils/transform/object/object-values-to-numeric';
import { useRoomSocket } from '@/hooks/use-room-socket/use-room-socket';
import { BatchBusinessTypeEnum } from '@/shared/constants/batch-business-type.constant';
import { createTrackEvent, useTrack } from '@/config/tracking-events';

import GuideWidget from './widgets/guide';
import SearchResult from './widgets/search-result';
import styles from './batch.module.less';
import { TABLE_COLUMNS } from './config';
import { useRequest } from '@/shared/composables/use-request';
import { useStore } from '@/store';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';

const IdentifyBatchPage = defineComponent({
  name: 'IdentifyBatchPage',
  setup() {
    const init = ref(true);
    const router = useRouter();
    const track = useTrack();

    const dataSource = ref<any>([]);
    const pagination = reactive({
      total: 0,
      current: 1,
      pageSize: 10,
    });

    const sortInfo = ref<{ sortField?: string; isSortAsc?: boolean }>({
      sortField: undefined,
      isSortAsc: undefined,
    });

    const hasData = computed(() => {
      return pagination.total > 0;
    });

    // 更新单个任务状态
    const updateBatchItem = (incomingItem: Record<string, any>) => {
      const oriData = cloneDeep(dataSource.value);
      const changeIndex = oriData.findIndex((item) => +incomingItem.batchId === item.batchId);
      if (changeIndex > -1) {
        oriData[changeIndex] = {
          ...oriData[changeIndex],
          status: +incomingItem.status,
          canRetry: +incomingItem.canRetry,
          statisticsInfo: {
            ...oriData[changeIndex].statisticsInfo,
            ...objectValuesToNumeric(incomingItem.statisticsInfo),
          },
        };
        dataSource.value = oriData;
      }
    };

    provide('needMessage', false);

    const fetchData = async () => {
      const res = await identify.batchSearch({
        businessType: [BatchBusinessTypeEnum.UBO],
        pageIndex: pagination.current,
        pageSize: pagination.pageSize,
        batchType: 0,
        ...unref(sortInfo),
      });
      pagination.total = res.total;
      dataSource.value = res.data;
      return res;
    };

    const { execute, isLoading } = useRequest(fetchData);

    useRoomSocket('/rover/socket', {
      filter: (messageData) =>
        messageData.roomType === 'BatchProcessMonitor' && [BatchBusinessTypeEnum.UBO].includes(+messageData.data.businessType),
      update: updateBatchItem,
      refresh: fetchData,
    });

    const updateData = async (data) => {
      if (data.isPaste) {
        await identify.dataBatch({
          companies: data.companies,
        });
      }

      message.success('批量排查任务正在进行中！请稍后在批量排查任务中查看结果');
      return fetchData();
    };

    const gotoDetail = (record) => {
      track(createTrackEvent(6208, '批量排查', '排查详情'));
      router.push({
        path: `batch/${record.batchId}`,
      });
    };

    const store = useStore();

    const operatorList = computed(() => store.getters['user/personList']);

    onMounted(async () => {
      await execute();
      store.dispatch('user/fetchPersonList');
      init.value = false;
    });
    return {
      hasData,
      init,
      dataSource,
      isLoading,
      updateData,
      execute,
      gotoDetail,
      pagination,
      operatorList,
      sortInfo,
    };
  },
  render() {
    return (
      <HeroicLayout align={this.hasData ? undefined : 'center'}>
        <div slot="hero">
          <header class={styles.hero}>
            <div class={styles.title}>受益所有人批量识别</div>
            <div class={styles.batch}>
              <BatchUpload
                action={(file) =>
                  `/benefishield/batch/boi/import?${qs.stringify({
                    fileName: file.name,
                  })}`
                }
                // beforeFileUpload={() => this.checkUsage()}
                // 多种新建任务的最后都会去出触发这个事件
                onUpdateData={this.updateData}
              />
            </div>
          </header>
        </div>
        {/* 新手引导 */}
        <Spin spinning={this.init} class={styles.spin}>
          <GuideWidget v-show={!this.hasData} />

          {/* 因为需要调用search，用v-show */}
          <SearchResult
            v-show={this.hasData}
            isLoading={this.isLoading}
            rowKey={'batchId'}
            scroll={{ x: true }}
            dataSource={this.dataSource}
            columns={TABLE_COLUMNS}
            operatorList={this.operatorList}
            pagination={this.pagination}
            onAction={this.gotoDetail}
            onExport={() => {
              //
            }}
            onSorterChange={(sorter) => {
              this.pagination.current = 1;
              this.sortInfo = convertSortStructure(sorter);
              this.execute();
            }}
            onChangePage={(current, pageSize) => {
              this.pagination.current = current;
              this.pagination.pageSize = pageSize;
              this.execute();
            }}
          ></SearchResult>
        </Spin>
      </HeroicLayout>
    );
  },
});

export default IdentifyBatchPage;
