import { defineComponent, PropType } from 'vue';

import BatchUploadFrame from '@/shared/components/batch-upload-frame';
import { IMPORT_COMPANY_TEMPLATE_URL } from '@/config';
import { createTrackEvent } from '@/config/tracking-events';

import FileUpload from '@/shared/components/file-upload';
import TextUpload from '@/shared/components/text-upload';
import styles from './batch-upload.module.less';
import DownloadLink from '@/shared/components/download-link';
import { message } from 'ant-design-vue';

const BatchUpload = defineComponent({
  name: 'BatchUpload',
  props: {
    /**
     * 上传地址
     */
    action: {
      type: [String, Function],
      required: true,
    },
    beforeFileUpload: {
      type: Function as PropType<(file: File) => Promise<boolean>>,
      required: false,
    },
  },
  setup(props, { emit }) {
    const update = (data) => {
      emit('updateData', data);
    };

    return {
      update,
    };
  },
  render() {
    return (
      <div ref="batch_upload_frames" class={styles.container}>
        <BatchUploadFrame title="输入文本">
          <TextUpload
            height="134px"
            theme="lighter"
            onSuccess={(data) => this.update({ companies: data, isPaste: true })}
            validate={(data) => {
              if (data?.length > 100) {
                message.warn('识别企业不能超过100家');
                return false;
              }
              return true;
            }}
          >
            <div slot="placeholder">
              <div class="faker-blink"></div>
              {` 点击输入或粘贴企业名录`}
            </div>
          </TextUpload>

          <ul slot="description">
            <li>
              可直接粘贴企业名称至该栏，不超过&nbsp;<em>100</em>&nbsp;条/次
            </li>
            <li>也可输入企业名、统一社会信用代码进行识别</li>
          </ul>
        </BatchUploadFrame>
        <BatchUploadFrame title="上传文档">
          <FileUpload
            ref="fileUpload"
            action={this.action}
            height="134px"
            placeholder="点击或拖拽文件到此上传"
            showUploadList={false}
            beforeFileUpload={this.beforeFileUpload}
            onSuccess={(e) => {
              this.update(e);
              this.$track(createTrackEvent(6208, '批量排查', '上传文档成功'));
            }}
            theme="lighter"
          />
          <ul slot="description">
            <li>
              <DownloadLink
                href={IMPORT_COMPANY_TEMPLATE_URL}
                download="批量排查模版.xlsx"
                onClick={() => {
                  this.$track(createTrackEvent(6208, '批量排查', '下载模板'));
                }}
              >
                下载模板
              </DownloadLink>
              <span>请按模版样式编辑并提交数据</span>
            </li>
            <li>
              不超过&nbsp;<em>2000</em>&nbsp;条/次
            </li>
          </ul>
        </BatchUploadFrame>
      </div>
    );
  },
});

export default BatchUpload;
