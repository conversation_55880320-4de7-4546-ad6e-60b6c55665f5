import { computed, defineComponent, PropType } from 'vue';
import { Button } from 'ant-design-vue';

import QRichTable from '@/components/global/q-rich-table';
import QCard from '@/components/global/q-card';
import EmptyWrapper from '@/shared/components/empty-wrapper';

import styles from './search-result.module.less';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    pagination: {
      type: Object,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    operatorList: {
      type: Array as PropType<{ userId: number; name: string }[]>,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    // const selectedIds = ref([]);

    // const rowSelection = computed(() => ({
    //   checkStrictly: false,
    //   selectedRowKeys: selectedIds.value,
    //   onChange: (selectedRowKeys, selectedRows) => {
    //     selectedIds.value = selectedRowKeys;
    //     // emit('selectItems', selectedRows);
    //   },
    // }));

    const handlePageChange = (current, pageSize) => {
      emit('changePage', current, pageSize);
    };

    const paginationProps = computed(() => ({
      ...props.pagination,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    }));

    return {
      paginationProps,
    };
  },
  render() {
    return (
      <QCard bodyStyle={{ padding: '15px' }} class={styles.container}>
        <div slot="title">批量识别任务</div>
        <EmptyWrapper dataSource={this.dataSource} loading={this.isLoading}>
          <QRichTable
            showIndex={true}
            tableLayout="fixed"
            loading={this.isLoading}
            rowKey={this.rowKey}
            dataSource={this.dataSource}
            columns={this.columns}
            pagination={this.paginationProps}
            scroll={{ y: 'calc(100vh - 146px - 215px)' }}
            onChange={({ sorter }) => {
              this.$emit('sorterChange', sorter);
            }}
            scopedSlots={{
              fileName: (name: string) => {
                return (
                  <div class={styles.fileName} title={name}>
                    {name}
                  </div>
                );
              },
              batchTaskStatus: (item) => {
                switch (item.status) {
                  case 0:
                    return (
                      <div style={{ display: 'flex' }}>
                        <span class={styles.inWait}>待生成</span>
                      </div>
                    );
                  case 1:
                    return (
                      <div style={{ display: 'flex', gap: '10px' }}>
                        <span class={styles.inProgress}>生成中</span>
                        <span style={{ color: '#128bed' }}>
                          {`${(Number((item.statisticsInfo.successCount || 0) / (item.statisticsInfo.recordCount || 0)) * 100).toFixed(0)}%`}
                        </span>
                      </div>
                    );
                  case 2:
                    return (
                      <div style={{ display: 'flex' }}>
                        <span class={styles.success}>已完成</span>

                        {item.resultFile && (
                          <a class={styles.export} href={item.resultFile} onClick={() => this.$emit('export')}>
                            导出名单
                          </a>
                        )}
                      </div>
                    );
                  case 3:
                    return (
                      <div style={{ display: 'flex' }}>
                        <span class={styles.failed}>生成失败</span>
                      </div>
                    );
                  case 7: {
                    const isAbort = item.statisticsInfo.successCount > 0;
                    return (
                      <div style={{ display: 'flex' }}>
                        <span class={styles.expired}>{isAbort ? '已中止' : '已取消'}</span>
                        {isAbort && item.resultFile && (
                          <a class={styles.export} href={item.resultFile} onClick={() => this.$emit('export')}>
                            导出名单
                          </a>
                        )}
                      </div>
                    );
                  }
                  default:
                    return '-';
                }
              },
              batchAction: (item) => {
                const isSuccess = item.status === 2;
                const isAbort = item.status === 7 && item.statisticsInfo.successCount > 0;
                return (
                  <div style={{ whiteSpace: 'nowrap' }}>
                    <Button
                      type="link"
                      disabled={(!isSuccess && !isAbort) || item.statisticsInfo.recordCount === item.statisticsInfo.errorCount}
                      onClick={() => this.$emit('action', item)}
                    >
                      详情
                    </Button>
                  </div>
                );
              },
              operator: (val) => {
                if (val === -1) {
                  return '系统自动巡检';
                }
                const operator = this.operatorList.find((item) => item.userId === val);
                return operator ? operator.name : '-';
              },
            }}
          />
        </EmptyWrapper>
      </QCard>
    );
  },
});

export default SearchResult;
