@import '@/styles/token.less';
@import '@/styles/mixin.less';

.container {
  .title {
    font-size: 32px;
    font-weight: 500;
    line-height: normal;
    color: #3D3D3D;
    margin-bottom: 16px;
  }

  .search {
    display: flex;
    .vertical-align(center);

    .title {
      font-size: 38px;
      line-height: 54px;
      font-weight: bold;
      margin-bottom: 36px;
    }

    .noBorder {
       :global {
         .ant-input {
           border: 0;
           box-shadow: none;
         }
       }
    }

    .input {
      width: 900px;
      display: block;

      // 选项菜单开启后样式
      &:global(.ant-select-open .ant-input),
      &:global(.ant-select-open .ant-select-selection) {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }
      // 选项为空时，输入框显示圆角
      &:global(.ant-select-open.ant-select-empty .ant-input) {
        border-bottom-left-radius: 4px;
      }

      &:global(.ant-select-open.ant-select-empty .ant-select-selection) {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
      }

      &:global(.ant-select-open .ant-input-group-addon) {
        border-bottom-right-radius: 0;
      }

      :global {
        .ant-input-group-addon {
          background: transparent; // 隐藏按钮背景
          overflow: hidden;
        }

        .ant-select-selection {
          font-size: 16px;
          height: var(--input-height);
          border-radius: 4px;
          overflow: hidden;
        }

        .ant-input {
          font-size: 20px;
          height: var(--input-height);
          padding: 14px 12px;
          color: #555;
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
        }

        .ant-btn {
          width: 110px;
          height: var(--input-height);
          font-size: 20px;
          font-weight: 700;
          border-radius: 0;
        }

        .ant-btn-primary {
          background-color: @qcc-color-blue-500;
          border-color: @qcc-color-blue-500;
          font-size: 20px;

          &:hover,
          &:focus {
            background-color: @qcc-color-blue-600;
            border-color: @qcc-color-blue-600;
          }
        }
      }
    }
  }

  .minSearch {
    :global {
      .ant-input-suffix {
        right: -2px;
      }

      .ant-input {
        padding-left: 10px !important;
      }
    }
  }
}
