.container {
  .tag {
    border-radius: 2px;
    display: flex;
    flex-direction: column;
    padding: 2px 6px;
    border: 1px solid #d8d8d8;
    font-size: 12px;
    line-height: 18px;
    color: #333;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    box-shadow: inset 0 -1px 0 0 #eee;

    :global {
      .ant-button .anticon {
        font-size: 14px;
      }

      .ant-btn-background-ghost.ant-btn-primary {
        background-color: #f2f8fe !important;
      }

      .ant-btn-primary.ant-btn-background-ghost:hover {
        background-color: #E2F1FD !important;
        color: #128bed;
      }
    }
  }

  .tab {
    line-height: 50px;
    height: 50px;
  }
}
