export const mergedNormalColumns = [
  {
    title: '识别标准',
    width: 120,
    dataIndex: 'beneStandard',
    scopedSlots: { customRender: 'beneStandard' },
    customCell: () => {
      return {
        attrs: {
          colSpan: 6,
          style: 'padding: 0;',
        },
      };
    },
  },
  {
    title: '受益所有人类型',
    width: 200,
    dataIndex: 'resultType',
  },
  {
    title: '受益所有人',
    width: 140,
    scopedSlots: { customRender: 'normalBeneResult' },
  },
  {
    title: '股权/表决权比例',
    width: 160,
    scopedSlots: { customRender: 'totalStockPercent' },
  },
  {
    title: '任职类型',
    width: 180,
    scopedSlots: { customRender: 'normalRole' },
  },
  {
    title: '受益所有权形成日期',
    width: 160,
    dataIndex: 'uboDate',
  },
];

const normalColumns = [
  {
    title: '已匹配企业',
    width: 220,
    fixed: 'left',
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '备案类型',
    width: 100,
    scopedSlots: { customRender: 'recordFlag' },
  },
  {
    title: '统一社会信用代码/注册号/商业登记号码',
    width: 260,
    dataIndex: 'creditCode',
  },
  ...mergedNormalColumns.map((v) => {
    return v.customCell
      ? v
      : {
          ...v,
          customRender: () => {
            return {
              attrs: {
                colSpan: 0,
              },
            };
          },
        };
  }),
];

const exemptColumns = [
  {
    title: '已匹配企业',
    width: 220,
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '备案类型',
    width: 100,
    scopedSlots: { customRender: 'recordFlag' },
  },
  {
    title: '统一社会信用代码/注册号/商业登记号码',
    width: 260,
    dataIndex: 'creditCode',
  },
  {
    title: '识别理由',
    dataIndex: 'remark',
  },
];

const simpleColumns = [
  {
    title: '已匹配企业',
    width: 220,
    fixed: 'left',
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '备案类型',
    width: 100,
    scopedSlots: { customRender: 'recordFlag' },
  },
  {
    title: '统一社会信用代码/注册号/商业登记号码',
    width: 260,
    dataIndex: 'creditCode',
  },
  {
    title: '受益所有人',
    width: 140,
    scopedSlots: { customRender: 'simpleBeneResult' },
  },
  {
    title: '任职类型',
    width: 180,
    dataIndex: 'position',
  },
  {
    title: '识别理由',
    width: 384,
    dataIndex: 'remark',
  },
  {
    title: '受益所有权形成日期',
    width: 160,
    dataIndex: 'uboDate',
    scopedSlots: { customRender: 'date' },
  },
];
const notMatchColumns = [
  {
    title: '已匹配企业',
    width: 220,
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '备案类型',
    width: 100,
    scopedSlots: { customRender: 'recordFlag' },
  },
  {
    title: '统一社会信用代码/注册号/商业登记号码',
    width: 260,
    dataIndex: 'creditCode',
  },
  {
    title: '原因',
    // width: 224,
    dataIndex: 'comment',
  },
];

export enum BeneType {
  normal = 'N',
  exempt = 'E',
  simple = 'S',
  notMatch = 'U',
}

export const COLUMNS_MAP = {
  [BeneType.normal]: { columns: normalColumns, isFixed: true },
  [BeneType.exempt]: { columns: exemptColumns, isFixed: false },
  [BeneType.simple]: { columns: simpleColumns, isFixed: true },
  [BeneType.notMatch]: { columns: notMatchColumns, isFixed: false },
};
