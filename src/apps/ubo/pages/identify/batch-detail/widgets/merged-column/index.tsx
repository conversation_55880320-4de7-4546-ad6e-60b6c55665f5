import { defineComponent, PropType } from 'vue';
import { get } from 'lodash';
import styles from './merged-column.module.less';

type ColumnConfig = {
  title: string;
  dataIndex?: string;
  width: number;
  customRender?: (data) => any;
  scopedSlots?: {
    customRender: string;
  };
  customCell?: () => any;
};

const MergedColumn = defineComponent({
  name: 'MergedColumn',
  props: {
    dataSource: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => [],
    },
    columns: {
      type: Array as PropType<ColumnConfig[]>,
      default: () => [],
    },
  },
  render() {
    const renderColumns = () => {
      if (!this.dataSource?.length) return null;
      const result = this.dataSource.map((data) => {
        if (data.empty) {
          return <td class={[styles.cell, styles.emptyCell]}>{data.emptyMsg}</td>;
        }
        const row = this.columns.map((c) => {
          if (c.scopedSlots?.customRender && this.$scopedSlots[c.scopedSlots.customRender]) {
            return (
              <td class={styles.cell} style={{ width: `${c.width}px` }}>
                {this.$scopedSlots[c.scopedSlots.customRender]?.(c.dataIndex ? get(data, c.dataIndex) : data)}
              </td>
            );
          }
          if (c.customRender) {
            return (
              <td class={styles.cell} style={{ width: `${c.width}px` }}>
                {c.customRender(c.dataIndex ? get(data, c.dataIndex) : data)}
              </td>
            );
          }

          return (
            <td class={styles.cell} style={{ width: `${c.width}px` }}>
              {c.dataIndex ? get(data, c.dataIndex) : '-'}
            </td>
          );
        });
        return <tr class={styles.row}>{row}</tr>;
      });
      return (
        <table class={styles.container}>
          <tbody>{result}</tbody>
        </table>
      );
    };
    return renderColumns();
  },
});

export default MergedColumn;
