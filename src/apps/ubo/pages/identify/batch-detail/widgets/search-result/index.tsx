import QEntityLink from '@/components/global/q-entity-link';
import QRichTable from '@/components/global/q-rich-table';
import { getTagStyle } from '@/config/risk.config';
import { Tooltip } from 'ant-design-vue';
import { computed, defineComponent, PropType, unref } from 'vue';
import MergedColumn from '../merged-column';
import { mergedNormalColumns } from '../../batch-detail.config';
import { isEmpty } from 'lodash';

const RecordLabelMap: Record<string, string> = {
  需要备案:
    '根据《受益所有人信息管理办法》（〔2024〕3号令）第二条、第三条，《中华人民共和国反洗钱法》（主席令第38号）第十九条，该市场主体需要通过登记注册系统备案受益所有人信息。',
  无需备案:
    '根据《受益所有人信息管理办法》（〔2024〕3号令）第二条、《受益所有人信息备案指南（第一版）》，该市场主体无需备案或暂时无需备案受益所有人信息。',
  承诺免报:
    '根据《受益所有人信息管理办法》（〔2024〕3号令）第三条，《受益所有人信息备案指南（第一版）》，该市场主体同时满足相应条件，承诺后免于备案受益所有人。',
  暂不涉及: '根据相关法规要求，该市场主体暂不涉及受益所有人信息备案要求。',
};

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    dataSource: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      required: true,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    isFixed: {
      type: Boolean,
      default: false,
    },
    pagination: {
      type: Object as PropType<{ current: number; pageSize: number; total: number }>,
      required: true,
    },
  },
  setup(props, { emit }) {
    const handlePageChange = (current, pageSize) => {
      emit('pageChange', { current, pageSize });
    };
    const paginationProps = computed(() => ({
      ...unref(props.pagination),
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    }));
    return {
      paginationProps,
    };
  },
  render() {
    return (
      <div>
        <QRichTable
          showIndex
          isFixed={this.isFixed}
          rowKey={this.rowKey}
          tableLayout="fixed"
          columns={this.columns}
          dataSource={this.dataSource}
          pagination={this.paginationProps}
          loading={this.isLoading}
          customScroll={this.isFixed ? { x: true, y: 'calc(100vh - 53px - 60px - 170px)' } : undefined}
          onChange={({ sorter: antdSorter }) => {
            this.$emit('changeSort', antdSorter);
          }}
          scopedSlots={{
            companyName: (record) => {
              return <QEntityLink ellipsis={false} coyObj={{ KeyNo: record.keyNo, Name: record.companyName }} />;
            },
            recordFlag: (record) => {
              if (!record.recordType) {
                return '-';
              }
              return (
                <Tooltip title={RecordLabelMap[record.recordType]}>
                  <span
                    style={getTagStyle({
                      background: '#e2f1fd',
                      color: '#128bed',
                      cursor: 'pointer',
                    })}
                  >
                    {record.recordType}
                  </span>
                </Tooltip>
              );
            },
            beneStandard: (_, record) => {
              let dataSource: Record<string, any>[] = [];
              // 日常经营管理人员
              if (record.managePersons?.length) {
                dataSource = record.managePersons.map((v) => ({ ...v, beneStandard: '日常经营管理人员' }));
              }
              // 标准一、二、三
              if (!isEmpty(record.breakThroughList) || !isEmpty(record.criterion2ndInfo) || !isEmpty(record.criterion3rdInfo)) {
                const standard1 = record.breakThroughList?.map((v) => ({ ...v, beneStandard: '标准一', resultType: '25%以上股权' })) || [];
                const standard2 =
                  record.criterion2ndInfo?.criterion2ndDetailList?.map((v) => ({
                    ...v,
                    beneStandard: '标准二',
                    resultType: '25%以上表决权、收益权',
                    remark: record.criterion2ndInfo.remark,
                  })) || [];
                const standard3 =
                  record.criterion3rdInfo?.criterion3rdDetailList?.map((v) => ({
                    ...v,
                    beneStandard: '标准三',
                    resultType: '控制/影响自然人',
                    remark: record.criterion3rdInfo.remark,
                  })) || [];
                dataSource = [...standard1, ...standard2, ...standard3];
              }
              // 无受益人
              if (!dataSource.length) {
                dataSource = [
                  {
                    empty: true,
                    emptyMsg: '该主体当前无满足相应标准的受益所有人',
                  },
                ];
              }

              return (
                <MergedColumn
                  dataSource={dataSource}
                  columns={mergedNormalColumns}
                  scopedSlots={{
                    normalBeneResult: (record) => {
                      return (
                        <div>
                          <QEntityLink ellipsis={false} coyObj={{ KeyNo: record.keyNo, Name: record.name }} />
                          {record.nameRemark ? <span class="text-#BBB pl-1">({record.nameRemark})</span> : null}
                        </div>
                      );
                    },
                    totalStockPercent: (record) => {
                      if (record.votingPercent || record.totalStockPercent) {
                        return (
                          <div>
                            <span>{record.votingPercent || record.totalStockPercent || '-'}</span>
                            {record.mergeInd === 1 ? <span class="tex-#BBB pl-1">(共同持有)</span> : null}
                          </div>
                        );
                      }
                      return '-';
                    },
                    normalRole: (record) => {
                      return (
                        <div>
                          <div>{record.position || '-'}</div>
                          {record.remark ? <div class="tex-#BBB pl-1">(*{record.remark})</div> : null}
                        </div>
                      );
                    },
                  }}
                />
              );
            },
            simpleBeneResult: (record) => {
              return (
                <div>
                  <QEntityLink coyObj={{ KeyNo: record.operKeyNo, Name: record.operName }} />
                  {record.nameRemark ? <span class="text-#BBB pl-1">{record.nameRemark}</span> : null}
                </div>
              );
            },
          }}
        ></QRichTable>
      </div>
    );
  },
});

export default SearchResult;
