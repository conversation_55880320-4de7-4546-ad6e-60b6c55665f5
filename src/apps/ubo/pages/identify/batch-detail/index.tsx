import QCard from '@/components/global/q-card';
import QTabs from '@/components/global/q-tabs';
import HeroicLayout from '@/shared/layouts/heroic';
import { <PERSON><PERSON>crumb, <PERSON><PERSON>, Tooltip } from 'ant-design-vue';
import { computed, defineComponent, onMounted, reactive, ref, unref } from 'vue';
import SearchResult from './widgets/search-result';
import { BeneType, COLUMNS_MAP } from './batch-detail.config';
import { useRequest } from '@/shared/composables/use-request';
import styles from './batch-detail.module.less';
import { dateFormat } from '@/utils/format';
import QIcon from '@/components/global/q-icon';
import { identify } from '@/shared/services';
import { useAddCompanyModal } from '@/hooks/use-add-company-to-monitor.hook';

const IdentifyBatchDetailPage = defineComponent({
  name: 'IdentifyBatchDetailPage',
  props: {
    id: {
      type: String,
      required: true,
    },
  },
  setup(props, { emit }) {
    const init = ref(true);

    const identifyTime = ref<string>('-');

    const currentTab = ref<BeneType>(BeneType.normal);

    const cachedPageIndex = ref<Record<string, number>>({});

    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
    });

    const tabConfig = ref<{ key: BeneType; label: string; count?: number }[]>([]);
    const getTabConfig = ({ normal, exempt, simple, notMatch }) => {
      tabConfig.value = [
        {
          label: '正常识别',
          key: BeneType.normal,
          count: normal,
        },
        {
          label: '豁免识别',
          key: BeneType.exempt,
          count: exempt,
        },
        {
          label: '简易识别',
          key: BeneType.simple,
          count: simple,
        },
        {
          label: '未匹配出',
          key: BeneType.notMatch,
          count: notMatch,
        },
      ].filter((v) => v.count > 0);
      currentTab.value = tabConfig.value[0].key;
    };

    const fetchData = async () => {
      const res = await identify.batchHistory({
        pageIndex: pagination.current,
        pageSize: pagination.pageSize,
        batchId: props.id,
        beneType: currentTab.value,
      });
      const formatData = res.data?.map((v) => ({ ...v, ...v.details }));
      pagination.total = res.total;
      return { ...res, data: formatData };
    };

    const { execute, data, isLoading } = useRequest(fetchData);

    const { handleAddCompany } = useAddCompanyModal(
      {
        permission: undefined, // 权限点
        monitorGroupId: undefined, // 分组Id
        selectCompanyList: [], // 已选公司
        disabled: false, // 是否禁用
      },
      emit
    );
    const handleTabChange = (val) => {
      cachedPageIndex.value[currentTab.value] = pagination.current;
      pagination.current = cachedPageIndex.value[val] || 1;
      currentTab.value = val;
      execute();
    };

    const dataSource = computed(() => {
      const list = data.value?.data || [];
      return list;
    });

    const getStatistic = async () => {
      const { statistics, createDate } = await identify.historyStatistics({
        batchId: props.id,
      });
      const data = statistics.reduce((acc, cur) => {
        acc[cur.beneType] = cur.count;
        return acc;
      }, {});
      await getTabConfig({
        normal: data[BeneType.normal],
        exempt: data[BeneType.exempt],
        simple: data[BeneType.simple],
        notMatch: data[BeneType.notMatch],
      });
      identifyTime.value = dateFormat(createDate, { pattern: 'YYYY-MM-DD hh:mm:ss', defaultVal: '-' });
    };

    onMounted(async () => {
      await getStatistic();
      await execute();
      init.value = false;
    });

    return {
      currentTab,
      tabConfig,
      init,
      dataSource,
      isLoading,
      handleTabChange,
      identifyTime,
      pagination,
      execute,
      handleAddCompany,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class="flex items-center justify-between">
          <Breadcrumb class="sticky-breadcrumb">
            <Breadcrumb.Item>
              <router-link to={{ name: 'ubo-identify-batch' }}>
                <q-icon type="icon-mianbaoxiefanhui"></q-icon> 批量识别
              </router-link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <div class="flex items-center gap-[5px]">
                受益所有人识别<span class={styles.tag}>3号令、1号令</span>
              </div>
            </Breadcrumb.Item>
          </Breadcrumb>
          <div class="shrink-0">
            <span class="text-#999">识别时间：</span>
            <span>{this.identifyTime}</span>
          </div>
        </div>
        <HeroicLayout loading={this.init} innerStyle={{ minHeight: 'calc(100vh - 53px - 60px)' }}>
          <QCard bodyStyle={{ paddingTop: 0, paddingBottom: '15px' }}>
            <div class={styles.header}>
              <QTabs class={styles.tab} value={this.currentTab} tabs={this.tabConfig} onChange={this.handleTabChange}></QTabs>
              <div class="flex gap-2">
                <Tooltip title="添加至风险监控列表后，新受益人的变动信息将会及时通知你">
                  {/* TODO 调用的时候传入对应的表格选择项就行 */}
                  <Button ghost type="primary" onClick={() => this.handleAddCompany(false, [])}>
                    <QIcon type="icon-jiankongicon" style={{ fontSize: '16px' }} />
                    添加监控
                  </Button>
                </Tooltip>
                <Button>
                  <QIcon type="icon-pdf1" />
                  报告下载
                </Button>
                <Button>
                  <QIcon type="icon-excel1" />
                  导出全部企业
                </Button>
              </div>
            </div>
            <SearchResult
              columns={COLUMNS_MAP[this.currentTab].columns}
              dataSource={this.dataSource}
              isLoading={this.isLoading}
              isFixed={COLUMNS_MAP[this.currentTab].isFixed}
              pagination={this.pagination}
              on={{
                pageChange: ({ current, pageSize }) => {
                  this.pagination.current = current;
                  this.pagination.pageSize = pageSize;
                  this.execute();
                },
              }}
            />
          </QCard>
        </HeroicLayout>
      </div>
    );
  },
});

export default IdentifyBatchDetailPage;
