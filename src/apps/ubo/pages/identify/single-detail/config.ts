export interface StatusItem {
  type: string;
  status: string;
  text: string;
}

export const StatusList: Record<string, StatusItem> = {
  N: {
    type: 'N',
    status: 'green',
    text: '可正常识别',
  },
  S: {
    type: 'S',
    status: 'blue',
    text: '可简易识别',
  },
  E: {
    type: 'E',
    status: 'red',
    text: '可豁免识别',
  },
  NE: {
    type: 'NE',
    status: 'red',
    text: '识别结果为空',
  },
};

export const getCompanyFields = (keyNo): string[] => {
  const publicFeilds = [
    'CreditCode', // 统一社会信用编码
    'RegistCapi', // 注册资本
    'ImageUrl',
    'Name', // 企业名称
    'KeyNo',
    'ShortStatus',
  ];
  if (keyNo.startsWith('w')) {
    return [
      'StartDate', // 成立日期
      'Oper', // 负责人信息
      'LawyerCount', // 律师人数
      'Area', // 所属地区
      'ContactInfo', // 联系信息
      'Abstract', // 机构简介
    ].concat(publicFeilds);
  } else if (keyNo.startsWith('H')) {
  }

  const feilds = [
    'CommonList', // 通用列表数据
    'OriginalName', // 曾用名
    'Area', // 地区信息
    'Oper', // 法定代表人/经营者信息
    'Status', // 登记状态
    'CorpAnnRptStatus', // 企业年报状态
    'RegistCapi', // 注册资本
    'RecCap', // 实缴资本
    'RealCapiSource', // 实缴资本来源
    'StartDate', // 成立日期
    'OrgNo', // 组织机构代码
    'No', // 工商注册号
    'TaxNo', // 纳税人识别号
    'EconKind', // 企业类型
    'TermStart', // 营业期限开始
    'TeamEnd', // 营业期限结束
    'TaxpayerType', // 纳税人资质
    'StaffScale', // 人员规模
    'InsuredCount', // 参保人数
    'StaffsOfBranch', // 分支机构参保人数
    'CheckDate', // 核准日期
    'BelongOrg', // 登记机关
    'IndustryV3', // 行业信息V3
    'EnglishName', // 英文名
    'Address', // 注册地址
    'LatestAnnualReportAddrInfo', // 最新年报地址信息
    'Scope', // 经营范围
  ].concat(publicFeilds);
  return feilds;
};
