import { defineComponent, getCurrentInstance, reactive } from 'vue';
import StandardListBase from '../standard-list-base';
import { IQRichTableColumn } from '@/components/global/q-rich-table';
import RelationalPath from '@/components/relational-path';
import { convertToRelationalPathData } from './util';
import QIcon from '@/components/global/q-icon';
import { Button } from 'ant-design-vue';
import { openShareholdingDraw } from '../shareholding-draw';

interface IRecordDetail {
  level: number;
  shouldCapi: number;
  capitalType: number;
  breakthroughStockPercent: string;
  stockType: string;
  path: string;
  stockPercent: number;
}
interface IRecord {
  id: string;
  name: string;
  keyNo: string;
  benifitType: number;
  totalStockPercent: string;
  position: string;
  uboDate: string;
  calculation: string;
  detailInfoList: IRecordDetail[];
  number?: number;
  rowSpan?: number;
}

export default defineComponent({
  name: 'StandardListOne',
  props: {
    list: {
      type: Array as () => IRecord[],
      default: () => [],
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const vm = getCurrentInstance()?.proxy;
    const targetList =
      props.list?.flatMap?.((item, index) => {
        return item.detailInfoList.map((detail, childrenIndex) => {
          return {
            ...item,
            ...detail,
            number: index + 1,
            rowSpan: childrenIndex === 0 ? item.detailInfoList.length : 0,
          };
        });
      }) || [];

    // 模拟数据
    const tableData = reactive({
      list: targetList,
    });
    const getCellRowSpan = (record: IRecord & IRecordDetail & { rowSpan?: number }) => {
      return { rowSpan: record?.rowSpan, colSpan: 1 };
    };
    // 列定义
    const columns: IQRichTableColumn[] = [
      {
        title: '序号',
        width: 58,
        dataIndex: 'number',
        fixed: true,
        customRender: (text: any, record: any, index: number) => {
          const spanOptions = getCellRowSpan(record);
          return {
            children: text,
            attrs: spanOptions,
          };
        },
      },
      {
        title: '受益所有人',
        dataIndex: 'name',
        width: 120,
        fixed: true,
        customRender: (text, record, index) => {
          const spanOptions = getCellRowSpan(record);
          return {
            children: <q-entity-link coyObj={{ KeyNo: record.keyNo, Name: record.name }}></q-entity-link>,
            attrs: spanOptions,
          };
        },
      },
      {
        title: '持股类型',
        dataIndex: 'benifitType',
        width: 150,
        customRender: (text, record, index) => {
          const spanOptions = getCellRowSpan(record);
          return {
            children: text || '-',
            attrs: spanOptions,
          };
        },
      },
      {
        title: '任职类型',
        dataIndex: 'position',
        width: 100,
        customRender: (text, record, index) => {
          const spanOptions = getCellRowSpan(record);
          return {
            children: text || '-',
            attrs: spanOptions,
          };
        },
      },
      {
        title: '持股比例',
        dataIndex: 'totalStockPercent',
        key: 'totalStockPercent',
        width: 102,
        customRender: (text, record, index) => {
          const spanOptions = getCellRowSpan(record);
          return {
            children: (
              <div class="flex items-center gap-1">
                {text}
                <span class="text-#FF722D">
                  <QIcon
                    type="icon-guquanlian"
                    onClick={() => {
                      vm?.$modal.showDimension('benefitChart', { ...record, companyInfo: { name: props.name, keyNo: props.keyNo } });
                    }}
                  />
                </span>
              </div>
            ),
            attrs: spanOptions,
          };
        },
      },
      {
        title: '计算过程',
        dataIndex: 'calculation',
        width: 170,
        customRender: (text, record, index) => {
          const spanOptions = getCellRowSpan(record);

          return {
            children: text || '-',
            attrs: spanOptions,
          };
        },
      },
      {
        title: '持股方式',
        dataIndex: 'stockType',
        width: 80,
        customRender: (text, record, index) => {
          return <span style={{ color: text !== '直接' ? '#2574A9' : '#019875' }}>{text}</span>;
        },
      },
      {
        title: '占比',
        dataIndex: 'stockPercent',
        width: 100,
      },
      {
        title: '层级关系',
        dataIndex: 'path',
        width: 220,
        customRender: (text, record, index) => {
          if (!text) return '-';
          return <RelationalPath type="investment" elements={convertToRelationalPathData(record)} />;
        },
      },
      {
        title: '受益所有权形成日期',
        dataIndex: 'uboDate',
        customRender: (text, record, index) => {
          const spanOptions = getCellRowSpan(record);

          return {
            children: text || '-',
            attrs: spanOptions,
          };
        },
      },
    ];

    const showShareholding = () => {
      openShareholdingDraw({
        keyNo: props.keyNo,
        name: props.name,
      });
    };

    return {
      tableData,
      columns,
      showShareholding,
    };
  },
  render() {
    return (
      <StandardListBase title="标准一：25%以上股权" columns={this.columns} list={this.tableData.list} showTableIndex={false}>
        <Button slot="tip" type="link" class="absolute! group right-0 top-4" onClick={this.showShareholding}>
          <QIcon type="icon-structure" style={{ color: '#128bed' }}></QIcon>
          <span class="text-#333 group-hover:text-#128bed!">股权架构</span>
        </Button>
      </StandardListBase>
    );
  },
});
