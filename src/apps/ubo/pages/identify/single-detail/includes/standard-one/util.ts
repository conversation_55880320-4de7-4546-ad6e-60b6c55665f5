import { type PathElement, type PathNode, type PathEdge } from '@/components/relational-path';

/**
 * 将原始的字符串格式的关系路径数据转换为 RelationalPath 组件需要的数组格式
 * 原始格式示例："舟山晨光电机股份有限公司@4.8387->舟山恒晨企业管理咨询合伙企业(有限合伙)@8.8679->吴永宽"
 *
 * @param relationshipData 原始的关系路径字符串
 * @param originData 原始数据对象，包含 keyNo、name 等信息
 * @returns RelationalPath 组件需要的数组数据
 */
export function convertToRelationalPathData(originData: any = {}): PathElement[] {
  const relationshipData = originData.path;
  if (!relationshipData) return [];

  const INVEST = '->';
  const elements: PathElement[] = [];
  const companies = relationshipData.split(INVEST);

  // 处理每个公司/个人节点和它们之间的关系
  for (let i = 0; i < companies.length; i++) {
    const item = companies[i];
    let name = '';
    let percentage = '';

    // 解析名称和持股比例
    if (item.lastIndexOf('@') !== -1) {
      name = item.substring(0, item.lastIndexOf('@'));
      percentage = item.substring(item.lastIndexOf('@') + 1);
    } else {
      name = item;
    }
    const nodeId = getEntityId(name, originData);
    // 创建节点
    const node: PathNode = {
      type: 'node',
      id: nodeId,
      nodeClick: (val) => {
        console.log('点击了节点', val);
      },
      name,
      label: name,
    };

    elements.push(node);

    // 如果不是最后一个节点，添加一条边
    if (i < companies.length - 1) {
      const edge: PathEdge = {
        type: 'edge',
        roles: percentage ? [`${percentage}`] : [],
        direction: 'right',
        data: percentage
          ? [
              {
                role: '',
                data: [`${percentage}`],
              },
            ]
          : [],
      };

      elements.push(edge);
    }
  }

  return elements;
}

/**
 * 获取实体ID，如果是当前实体则使用originData中的keyNo
 * @param name 实体名称
 * @param originData 原始数据
 * @returns 实体ID
 */
function getEntityId(name: string, originData: any): string {
  // 如果是当前实体，使用originData中的keyNo
  if (name === originData.name) {
    return originData.keyNo || '';
  }

  // 如果originData中有rootCorpName和rootCorpKeyNo，且名称匹配，则使用rootCorpKeyNo
  if (originData.rootCorpName && originData.rootCorpKeyNo && name === originData.rootCorpName) {
    return originData.rootCorpKeyNo;
  }

  // 如果是批量处理模式，检查operName
  if (originData.isBatch && originData.operName && name === originData.operName) {
    return originData.operKeyNo || '';
  }

  // 默认返回空字符串
  return '';
}
