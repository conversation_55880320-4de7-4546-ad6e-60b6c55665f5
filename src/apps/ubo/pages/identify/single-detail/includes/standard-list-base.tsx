import { defineComponent, reactive, ref, PropType, watch } from 'vue';
import QRichTable, { IQRichTableColumn } from '@/components/global/q-rich-table';

import styles from './standard-list-base.module.less';

/**
 * 标准列表基础组件
 * 基于QRichTable实现，替代原有的app-table组件
 * 支持标题、展开收起、分页等功能
 */
const StandardListBase = defineComponent({
  name: 'StandardListBase',
  props: {
    // 列表标题
    title: {
      type: String,
      default: '',
    },
    // 表格列定义
    columns: {
      type: Array as PropType<IQRichTableColumn[]>,
      default: () => [],
    },
    // 数据列表
    list: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    // 自定义行属性函数
    customRow: {
      type: Function as PropType<(record: any, index: number) => any>,
      default: () => ({}),
    },
    // 请求分页数据的函数
    requestPageData: {
      type: Function as PropType<(page: number, pagination: any) => Promise<any>>,
      default: undefined,
    },
    // 表格配置选项
    options: {
      type: Object,
      default: () => ({}),
    },
    // 是否需要展开收起功能
    needExpand: {
      type: Boolean,
      default: false,
    },
    // 是否隐藏表格内容
    hideBodyTable: {
      type: Boolean,
      default: false,
    },
    // 隐藏表格时的提示文本
    hideBodyTip: {
      type: String,
      default: '点击展开查看更多',
    },
    showTableIndex: {
      type: Boolean,
      default: true,
    },
    tableRowKey: {
      type: [Function, String],
      default: 'id',
    },
  },
  setup(props) {
    // 数据控制
    const listControlData = reactive({
      showDatas: [] as any[], // 当前页显示的数据
      allDatas: [] as any[], // 所有数据
      spanMethod: false,
    });

    // 分页配置
    const paginationProp = reactive({
      show: false,
      isNet: false, // 是否网络分页，false表示本地分页
      total: 0,
      current: 1,
      pageSize: 5,
      pageSizeOptions: ['5', '10', '25', '50', '100'],
    });

    // 是否展开
    const isNoExpand = ref(true);

    /**
     * 判断是否显示表格内容
     * 当有数据且未设置hideBodyTable时显示表格
     * @returns {boolean} 是否显示表格
     */
    const showBodyTable = () => {
      return (
        (listControlData.showDatas && listControlData.showDatas.length > 0) ||
        (listControlData.allDatas && listControlData.allDatas.length > 0)
      );
    };

    /**
     * 初始化数据
     * 设置所有数据并计算本地分页
     */
    const initData = () => {
      listControlData.allDatas = props.list || [];
      paginationProp.total = listControlData.allDatas.length;

      // 判断是否需要显示分页
      if (listControlData.allDatas.length <= paginationProp.pageSize) {
        paginationProp.show = false;
        listControlData.showDatas = listControlData.allDatas;
      } else {
        paginationProp.show = true;
        // 计算当前页数据
        updateCurrentPageData();
      }
    };

    /**
     * 更新当前页显示的数据
     * 根据当前页码和每页条数计算显示数据
     */
    const updateCurrentPageData = () => {
      const startIndex = (paginationProp.current - 1) * paginationProp.pageSize;
      const endIndex = startIndex + paginationProp.pageSize;
      listControlData.showDatas = listControlData.allDatas.slice(startIndex, endIndex);
    };

    /**
     * 分页处理函数
     * @param page 当前页码
     * @param pageSize 每页条数
     */
    const handleCurrentChange = (page: number, pageSize: number) => {
      const oldPageSize = paginationProp.pageSize;
      paginationProp.current = page;
      paginationProp.pageSize = pageSize;

      // 如果有网络分页函数，使用网络分页
      if (props.requestPageData) {
        paginationProp.isNet = true;
        props.requestPageData(page, pageSize).then((res: any) => {
          listControlData.showDatas = res.resultList || [];
          paginationProp.total = res.totalCount || 0;
          paginationProp.show = paginationProp.total > paginationProp.pageSize;
        });
      } else {
        // 本地分页逻辑
        paginationProp.isNet = false;

        // 如果每页条数改变，重置到第一页
        if (oldPageSize !== pageSize) {
          paginationProp.current = 1;
        }

        // 重新计算分页显示状态
        // 只要总数据量大于默认页面大小(5条)，就应该显示分页器
        if (listControlData.allDatas.length <= 5) {
          paginationProp.show = false;
          listControlData.showDatas = listControlData.allDatas;
        } else {
          paginationProp.show = true;
          updateCurrentPageData();
        }
      }
    };

    // 监听 list 属性变化
    watch(
      () => props.list,
      () => {
        initData();
      },
      { deep: true, immediate: true }
    );

    return {
      listControlData,
      paginationProp,
      isNoExpand,
      showBodyTable,
      initData,
      updateCurrentPageData,
      handleCurrentChange,
    };
  },
  render() {
    // 确保columns属性始终有值
    const safeColumns = this.columns || [];

    // 提取共用的表格配置
    const tableProps = {
      columns: safeColumns,
      // 根据分页类型选择数据源：网络分页使用showDatas，本地分页也使用showDatas（已经过滤）
      dataSource: this.paginationProp.isNet ? this.listControlData.showDatas : this.listControlData.showDatas,
      rowKey: this.tableRowKey,
      customRow: this.customRow,
      pagination: this.paginationProp.show
        ? {
            total: this.paginationProp.total,
            current: this.paginationProp.current,
            pageSize: this.paginationProp.pageSize,
            onChange: this.handleCurrentChange,
            onShowSizeChange: this.handleCurrentChange,
            pageSizeOptions: this.paginationProp.pageSizeOptions,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total: number, range: [number, number]) => `共 ${total} 条`,
          }
        : false,
      ...this.options,
    };

    // 判断是否显示表格
    const shouldShowTable = this.showBodyTable() && (!this.needExpand || !this.isNoExpand);
    // 判断是否有有效的列定义
    const hasValidColumns = safeColumns.length > 0;

    return (
      <div
        class={{
          [styles.nuboDetailItem]: true,
          [styles.nuboDetailItemNoData]: !this.showBodyTable() && !this.$slots.default,
          [styles.nuboDetailItemNoExpand]: this.needExpand && this.isNoExpand,
        }}
      >
        {this.title && (
          <div class={styles.nuboDetailItemTitle}>
            <span domPropsInnerHTML={this.title} />
          </div>
        )}

        {/* 插槽：提示信息 */}
        {this.$slots.tip}

        <div class={styles.nuboDetailItemContent}>
          {this.$slots.default || (
            <div v-show={this.showBodyTable()}>
              {this.needExpand && (
                <div class={styles.nuboDetailItemTip}>
                  {this.hideBodyTip}
                  <a onClick={() => (this.isNoExpand = !this.isNoExpand)} class="ml-2">
                    {this.title}
                    <q-icon
                      type="icon-a-xianduanxia"
                      class={{
                        [styles.iconExpand]: true,
                        [styles.rotate]: !this.isNoExpand,
                      }}
                    ></q-icon>
                  </a>
                </div>
              )}

              {shouldShowTable && hasValidColumns && (
                <QRichTable
                  class={this.needExpand ? styles.nuboDetailItemTable : ''}
                  columns={tableProps.columns}
                  dataSource={tableProps.dataSource}
                  rowKey={tableProps.rowKey}
                  customRow={tableProps.customRow}
                  pagination={tableProps.pagination}
                  showIndex={this.showTableIndex}
                  {...this.options}
                />
              )}
            </div>
          )}
        </div>
      </div>
    );
  },
});

export default StandardListBase;
