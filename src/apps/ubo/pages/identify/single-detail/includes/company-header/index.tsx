import { defineComponent, PropType, ref } from 'vue';
import CompanyLogo from '@/components/company-logo';
import { getRegisteredCapitalLabel, toDash } from '../business-info/utils';
import { Button, Tooltip, message as Message } from 'ant-design-vue';
import QIcon from '@/components/global/q-icon';
import { throttle } from 'lodash';
import CompanyStatus from '@/components/global/q-company-status';
import { getCreditCodeLabel } from '../business-info/utils';
import { identify as identifyService } from '@/shared/services';
/**
 * 公司头部组件
 * 显示公司基本信息，包括公司名称、统一社会信用代码、注册资本等
 */
const CompanyHeader = defineComponent({
  name: 'CompanyHeader',
  props: {
    /**
     * 公司信息对象
     */
    businessInfo: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    /**
     * 公司KeyNo
     */
    keyNo: {
      type: String,
      default: '',
    },
    recordType: {
      type: String,
      default: '',
    },
    beneType: {
      type: String,
      default: '',
    },
    subjectType: {
      type: String,
      default: '',
    },
    snapshotId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const disabledReportBtn = ref(false);
    const addToMonitor = throttle(() => {}, 1000, { trailing: false, leading: true });
    const downloadReport = throttle(
      () => {
        disabledReportBtn.value = true;
        identifyService
          .createPdf({
            snapshotId: props.snapshotId,
          })
          .then((res) => {
            Message.success('报告下载成功');
          })
          .catch((err) => {
            disabledReportBtn.value = false;
          });
      },
      5000,
      { trailing: false, leading: true }
    );
    const monitorBtnTip = ref('添加至风险监控列表后，新受益人的变动信息将会及时通知你');

    return {
      addToMonitor,
      downloadReport,
      disabledReportBtn,
      monitorBtnTip,
    };
  },
  render() {
    const { businessInfo, keyNo, recordType, beneType, subjectType } = this;
    const tagArr = [
      subjectType ? `主体类型：${subjectType}` : '',
      recordType ? `备案类型：${recordType}` : '',
      `是否适用简易识别：${beneType === 'S' ? '' : '不'}适用`,
    ].filter((item) => item);
    const tagDom = tagArr.map((str) => {
      return (
        <div class="bg-#EEEEEE pl-1.5 pr-1.5 flex items-center mr-1 rounded-0.5 h-22px text-12px text-#808080 leading-12px">{str}</div>
      );
    });

    return (
      <div class="w-full bg-white p-4 relative">
        <div class="flex">
          <CompanyLogo
            src={businessInfo?.ImageUrl}
            id={keyNo}
            name={businessInfo?.Name}
            hasimage={businessInfo?.ImageUrl ? 1 : 0}
            size="58px"
            class="shrink-0"
          ></CompanyLogo>
          <div class="ml-3">
            <div class="flex items-center leading-7.5">
              <h1 class="text-22px font-bold text-#333 mr-2 mb-0">{businessInfo.Name}</h1>
              {businessInfo?.ShortStatus ? <CompanyStatus status={businessInfo?.ShortStatus} /> : ''}
            </div>
            <div class="flex items-center mt-2">{tagDom}</div>
            <div class="flex items-center mt-2 text-14px">
              <span class="text-#999">{getCreditCodeLabel(keyNo)}：</span>
              <span class=" text-#333 mr-7.5">{businessInfo.CreditCode || '-'}</span>
              <span class="text-#999">{getRegisteredCapitalLabel(businessInfo)}：</span>
              <span class=" text-#333 mr-7.5">{toDash(businessInfo?.RegistCapi)}</span>
            </div>
          </div>
        </div>
        <div class="absolute right-4 top-4">
          <Tooltip title={this.monitorBtnTip} arrowPointAtCenter={true} trigger="click" overlayStyle={{ backgroundColor: '#fff' }}>
            <Button type="normal" class="border-#128bed! text-#128bed!" onClick={this.addToMonitor}>
              <QIcon type="icon-jiankongicon" style={{ color: '#128bed' }} /> 添加监控
            </Button>
          </Tooltip>
          <Button type="normal" class="ml-3" onClick={this.downloadReport} disabled={this.disabledReportBtn}>
            <QIcon type="icon-pdf1" style={{ color: '#FF6060' }} /> 报告下载
          </Button>
        </div>
      </div>
    );
  },
});

export default CompanyHeader;
