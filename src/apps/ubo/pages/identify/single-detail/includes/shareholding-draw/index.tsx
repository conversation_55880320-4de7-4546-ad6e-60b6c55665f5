import { createPromiseDialog } from '@/components/promise-dialogs';
import EquityChain from '@/shared/charts/equity-chain';
import { Drawer } from 'ant-design-vue';
import { defineComponent, onMounted, PropType, ref } from 'vue';

const ShareholdingDraw = defineComponent({
  name: 'ShareholdingDraw',
  props: {
    params: {
      type: Object as PropType<{ name: string; keyNo: string }>,
      default: () => ({}),
    },
  },
  setup() {
    const visible = ref(false);

    onMounted(() => {
      visible.value = true;
    });
    return {
      visible,
    };
  },
  render() {
    return (
      <Drawer
        visible={this.visible}
        width={'78%'}
        title="股权穿透图谱"
        destroyOnClose
        onClose={() => {
          this.visible = false;
        }}
      >
        <div style={{ height: 'calc(100vh - 54px)' }}>
          <EquityChain containerId="equity-2" companyInfo={this.params} />
        </div>
      </Drawer>
    );
  },
});

export default ShareholdingDraw;

export const openShareholdingDraw = createPromiseDialog(ShareholdingDraw);
