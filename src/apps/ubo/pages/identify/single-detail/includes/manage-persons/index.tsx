import { computed, defineComponent, reactive } from 'vue';
import StandardListBase from '../standard-list-base';
import { IQRichTableColumn, IQRichTablePagination } from '@/components/global/q-rich-table';

interface IRecord {
  appointDate: string;
  keyNo: string;
  name: string;
  job: string;
  remark: string;
}

export default defineComponent({
  name: 'ManagePersons',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    keyNo: {
      type: String,
      default: '',
    },
    hideDailyManager: {
      type: Boolean,
      default: false,
    },
    hideBodyTip: {
      type: String,
      default: '根据法规要求，已识别标准一至标准三项下受益所有人，仍需继续展示',
    },
  },
  setup(props) {
    // 模拟数据
    const tableData = reactive({
      list: props.list,
    });
    // 列定义
    const columns: IQRichTableColumn[] = [
      {
        title: '受益所有人',
        dataIndex: 'name',
        width: 180,
        align: 'center',
        customRender: (text, row) => {
          // 使用自定义渲染函数处理原配置中的 format 函数逻辑
          const remark = row.nameRemark ? ` <span style="color:#BBB">${row.nameRemark}</span>` : '';
          return (
            <div>
              <q-entity-link coyObj={{ KeyNo: row.keyNo, Name: row.name }}></q-entity-link>
              {remark}
            </div>
          );
        },
      },
      {
        title: '任职类型',
        dataIndex: 'position',
        width: 375,
        align: 'center',
        customRender: (text, row) => {
          // 使用自定义渲染函数处理原配置中的format函数逻辑
          const roleString = `<div>${row.position || '-'}</div>`;
          let remarkString = `<div style="color: #BBBBBB">${row.remark ? '*' + row.remark : ''}</div>`;

          if (row.govControlFlag) {
            remarkString = '';
          }

          // 使用domPropsInnerHTML渲染HTML内容
          return <div domPropsInnerHTML={`${roleString} ${remarkString}`} />;
        },
      },
    ];

    return {
      tableData,
      columns,
    };
  },
  render() {
    return (
      <StandardListBase
        title="日常经营管理人员"
        hideBodyTip={this.hideBodyTip}
        columns={this.columns}
        list={this.tableData.list}
        isNet={false}
        needExpand={true}
        hideBodyTable={this.hideDailyManager}
      />
    );
  },
});
