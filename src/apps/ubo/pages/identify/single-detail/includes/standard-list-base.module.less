.nuboDetailItem {
  margin-bottom: 30px;

  .nuboDetailItemTitle {
    font-size: 16px;
    font-weight: bold;
    line-height: 32px;
    letter-spacing: 0;

    /* 0中性/中性1-#333333 */
    color: #333;
    margin-bottom: 10px;

    &::before {
      content: "";
      display: inline-block;
      width: 2px;
      height: 16px;
      background: #128bed;
      margin-right: 5px;
      position: relative;
      top: 2px;
    }
  }

  &.nuboDetailItemNoData {
    margin-bottom: 20px;

    .nuboDetailItemTitle {
      color: #999;
    }
  }

  .nuboDetailItemTip {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0;

    .iconExpand {
      transition: all 0.3s linear;

      &.rotate {
        transform: rotate(180deg);
      }
    }
  }

  .nuboDetailItemTable {
    margin-top: 10px;
  }

  .nuboDetailItemContent {
    width: 100%;
  }

  &.nuboDetailItemNoExpand {
    .nuboDetailItemTitle {
      color: #999;
    }

    .nuboDetailItemTip {
      color: #999;
    }
  }
}
