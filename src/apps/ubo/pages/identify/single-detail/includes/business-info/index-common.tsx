/**
 * 正常的企业信息
 */
import { defineComponent, computed, PropType } from 'vue';
import QPlainTable from '@/components/global/q-plain-table';
import QEntityLink from '@/components/global/q-entity-link';
import moment from 'moment';
import CompanyLogo from '@/components/company-logo';
import { operTypeName, getRegisteredCapitalLabel, getRegisteredAddressLabel, toDash, timeStamp2Date } from './utils';
import QIcon from '@/components/global/q-icon';
import { Tooltip } from 'ant-design-vue';
import styles from './businessinfo.module.less';
import ClampContent from '@/components/clamp-content';

const BusinessInfo = defineComponent({
  name: 'BusinessInfo',
  props: {
    businessInfo: {
      type: Object as PropType<Record<string, any>>,
      default: () => {},
    },
    keyNo: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    // 计算属性
    const isBranch = computed(() => {
      let isB = false;
      if (props.businessInfo?.CommonList?.length) {
        props.businessInfo.CommonList.forEach((cml: any) => {
          if (+cml.Key === 10) {
            // 是否分公司
            isB = true;
          }
        });
      }
      return isB;
    });

    const registeredAddressLabel = computed(() => {
      const result = isBranch.value ? '营业场所' : '注册地址';
      return getRegisteredAddressLabel(props.businessInfo, result);
    });

    const originalNameHtml = computed(() => {
      if (props.businessInfo?.OriginalName?.length) {
        const originalNames = [...props.businessInfo.OriginalName];
        originalNames.forEach((v: any) => {
          if (v.Name && v.StartDate && v.ChangeDate) {
            v.text =
              v.Name + ' (' + moment.unix(v.StartDate).format('YYYY-MM') + ' 至 ' + moment.unix(v.ChangeDate).format('YYYY-MM') + ')';
          }
          if (v.Name && !v.StartDate && v.ChangeDate) {
            v.text = v.Name + ' (' + '日期不明 至 ' + moment.unix(v.ChangeDate).format('YYYY-MM') + ')';
          }
          if (v.Name && v.StartDate && !v.ChangeDate) {
            v.text = v.Name + ' (' + moment.unix(v.StartDate).format('YYYY-MM') + ' 至 日期不明' + ')';
          }
          if (v.Name && !v.StartDate && !v.ChangeDate) {
            v.text = v.name;
          }
        });
        return originalNames
          .map((oname: any) => oname.text)
          .slice(0, 10)
          .join('<br/>');
      }
      return '';
    });

    const fullArea = computed(() => {
      return `${props.businessInfo?.Area?.Province || ''}${props.businessInfo?.Area?.City || ''}${props.businessInfo?.Area?.County || ''}`;
    });

    const parentName = computed(() => {
      if (props.businessInfo?.Oper) {
        const ty = operTypeName(props.businessInfo?.Oper?.OperType);
        return ty;
      }
      return '法定代表人';
    });

    const registeredCapitalLabel = computed(() => {
      return getRegisteredCapitalLabel(props.businessInfo);
    });

    return {
      isBranch,
      registeredCapitalLabel,
      registeredAddressLabel,
      originalNameHtml,
      fullArea,
      parentName,

      toDash,
      timeStamp2Date,
    };
  },
  render() {
    const { businessInfo } = this;
    const statusTip =
      businessInfo.CorpAnnRptStatus?.StdStatus && businessInfo.CorpAnnRptStatus?.YearStr
        ? '企业经营状态源于国家企业信用信息公示系统公示的企业年报'
        : '经营状态源于国家企业信用信息公示系统，暂未获取到该企业近三年年报公示的经营状态信息';
    //参保人数年份
    const insured = {
      from: '',
      count: '',
    };
    const findItem = businessInfo?.CommonList?.find((item) => item.Key === 25 && item.Value);
    if (findItem) {
      try {
        const obj = JSON.parse(findItem.Value);
        insured.from = obj.s;
      } catch (err) {}
    }
    const findCountItem = businessInfo?.CommonList?.find((item) => item.Key === 3 && item.Value);
    if (findItem) {
      insured.count = findCountItem.Value;
    }

    //进出口代码
    let ixCode = '';
    const findIXItem = businessInfo?.CommonList?.find((item) => item.Key === 8 && item.Value);
    try {
      const ixObjV = JSON.parse(findIXItem.Value);
      ixCode = ixObjV.IxCode;
    } catch (error) {}
    //注册地址
    const industryList: string[] = [];
    if (businessInfo.IndustryV3) {
      if (businessInfo.IndustryV3.Industry) {
        industryList.push(businessInfo.IndustryV3.Industry + ' > ');
      }
      if (businessInfo.IndustryV3.SubIndustry && industryList.findIndex((v) => v === businessInfo.IndustryV3.SubIndustry + ' > ') === -1) {
        industryList.push(businessInfo.IndustryV3.SubIndustry + ' > ');
      }
      if (
        businessInfo.IndustryV3.MiddleCategory &&
        industryList.findIndex((v) => v === businessInfo.IndustryV3.MiddleCategory + ' > ') === -1
      ) {
        industryList.push(businessInfo.IndustryV3.MiddleCategory + ' > ');
      }
      if (
        businessInfo.IndustryV3.SmallCategory &&
        industryList.findIndex((v) => v === businessInfo.IndustryV3.SmallCategory + ' > ') === -1
      ) {
        industryList.push(businessInfo.IndustryV3.SmallCategory);
      }
      if (industryList[industryList.length - 1].endsWith('> ')) {
        industryList[industryList.length - 1] = industryList[industryList.length - 1].replace('> ', '');
      }
    }
    const addressDom = industryList.length
      ? industryList.map((str, index) => {
          if (index === industryList.length - 1) {
            return str;
          }
          return <span class="text-#999">{str}</span>;
        })
      : '';

    return (
      <QPlainTable class={styles.businessInfo}>
        <colgroup>
          <col width="13%" />
          <col width="21%" />
          <col width="13%" />
          <col width="20%" />
          <col width="13%" />
          <col width="20%" />
        </colgroup>
        <tbody>
          <tr>
            <td class="tb">统一社会信用代码</td>
            <td>
              <div class="flex items-center">
                <span>{toDash(businessInfo?.CreditCode)}</span>
              </div>
            </td>
            <td class="tb">企业名称</td>
            <td colspan="3">
              <div class="flex items-center">
                <span>{businessInfo?.Name || '-'}</span>
                {businessInfo?.Name && <q-copy class="ml-2" copy-value={businessInfo.Name}></q-copy>}
              </div>
              {this.originalNameHtml && (
                <div class="text-gray-400 flex leading-5.5">
                  曾用名：
                  <div class="display-inline-block leading-5.5">
                    <ClampContent line={1}>
                      <span domPropsInnerHTML={this.originalNameHtml}></span>
                    </ClampContent>
                  </div>
                </div>
              )}
            </td>
          </tr>
          <tr>
            <td class="tb">{this.parentName}</td>
            <td>
              {businessInfo?.Oper?.Name ? (
                <div class="flex">
                  <CompanyLogo
                    src={businessInfo?.Oper.ImageUrl}
                    id={businessInfo.Oper.KeyNo}
                    name={businessInfo.Oper.Name}
                    hasimage={businessInfo?.Oper.HasImage ? 1 : 0}
                    size="40px"
                    class="shrink-0"
                  ></CompanyLogo>
                  <div class="ml-2.5 flex flex-col">
                    <QEntityLink
                      coyObj={{
                        KeyNo: businessInfo.Oper.KeyNo,
                        Name: businessInfo.Oper.Name,
                      }}
                      ellipsis={false}
                    />
                    {businessInfo?.Oper?.CompanyCount ? (
                      <a
                        class="text-#FF722D mt-1 cursor-pointer"
                        target="_blank"
                        href={`/embed/beneficaryDetail?personId=${businessInfo.Oper.KeyNo}&title=${encodeURIComponent(businessInfo.Oper.Name)}`}
                      >
                        关联{businessInfo.Oper.CompanyCount}家企业<QIcon type="icon-wenzilianjiantou"></QIcon>
                      </a>
                    ) : (
                      ''
                    )}
                  </div>
                </div>
              ) : (
                <div>-</div>
              )}
            </td>
            <td class="tb">登记状态</td>
            <td colspan="3">{toDash(businessInfo?.Status)}</td>
            {/* <td class="tb">
              <div class="flex items-center">
                <span class="tb-content__text">经营状态</span>
                <Tooltip title={statusTip}>
                  <QIcon type="icon-icon_zhushi" style={{ color: '#D8D8D8' }} class="ml-1 cursor-pointer" />
                </Tooltip>
              </div>
            </td> */}
            {/* <td>
              {businessInfo?.CorpAnnRptStatus ? businessInfo.CorpAnnRptStatus.StdStatus || '-' : '-'}
              {businessInfo?.CorpAnnRptStatus?.StdStatus && businessInfo?.CorpAnnRptStatus?.YearStr && (
                <span>（{businessInfo.CorpAnnRptStatus.YearStr}年报）</span>
              )}
            </td> */}
          </tr>
          <tr>
            <td class="tb">{this.registeredCapitalLabel}</td>
            <td>{toDash(businessInfo?.RegistCapi)}</td>
            <td class="tb">实缴资本</td>
            <td>
              <div class="flex items-center">
                <span class="leading-3.5">{toDash(businessInfo?.RecCap)}</span>
                {businessInfo?.RealCapiSource?.Year ? (
                  <Tooltip title={`实缴资本信息来源于：${businessInfo.RealCapiSource.Year}年报，结果仅供参考`}>
                    <QIcon type="icon-icon_zhushi" style={{ color: '#D8D8D8' }} class="ml-1 cursor-pointer" />
                  </Tooltip>
                ) : (
                  ''
                )}
              </div>
            </td>
            <td class={`tb ${!timeStamp2Date(businessInfo?.StartDate) ? 'org-color' : ''}`}>成立日期</td>
            <td class={!timeStamp2Date(businessInfo?.StartDate) ? 'org-color' : ''}>
              <div class="flex items-center">
                <span>{toDash(timeStamp2Date(businessInfo?.StartDate))}</span>
                {timeStamp2Date(businessInfo?.StartDate) && (
                  <q-copy class="ml-2" copy-value={timeStamp2Date(businessInfo.StartDate)}></q-copy>
                )}
              </div>
            </td>
          </tr>
          <tr>
            <td class="tb">组织机构代码</td>
            <td>
              <div class="flex items-center">
                <span>{toDash(businessInfo?.OrgNo)}</span>
                {businessInfo?.OrgNo && <q-copy class="ml-2" copy-value={businessInfo.OrgNo}></q-copy>}
              </div>
            </td>
            <td class="tb">工商注册号</td>
            <td>
              <div class="flex items-center">
                <span>{toDash(businessInfo?.No)}</span>
                {businessInfo?.No && <q-copy class="ml-2" copy-value={businessInfo.No}></q-copy>}
              </div>
            </td>
            <td class="tb">纳税人识别号</td>
            <td>
              <div class="flex items-center">
                <span>{toDash(businessInfo?.TaxNo)}</span>
                {businessInfo?.TaxNo && <q-copy class="ml-2" copy-value={businessInfo.TaxNo}></q-copy>}
              </div>
            </td>
          </tr>
          <tr>
            <td class="tb">企业类型</td>
            <td>{toDash(businessInfo?.EconKind)}</td>
            <td class="tb">营业期限</td>
            <td>
              {businessInfo?.TermStart ? (
                <span>
                  {businessInfo.TermStart ? timeStamp2Date(businessInfo.TermStart) : '***'}
                  <span>至</span>
                  {businessInfo.TeamEnd ? timeStamp2Date(businessInfo.TeamEnd) : '无固定期限'}
                </span>
              ) : (
                '-'
              )}
            </td>
            <td class="tb">
              <div class="flex item-center">
                <span class="leading-3.5">纳税人资质</span>
                <Tooltip title="一般指企业或个人所拥有的税务方面的权利或名誉，通过拥有某种资质可以享受某些税务方面的权利，主要分为一般纳税人或小规模纳税人。">
                  <QIcon type="icon-icon_zhushi" style={{ color: '#D8D8D8' }} class="ml-1 cursor-pointer" />
                </Tooltip>
              </div>
            </td>
            <td>{businessInfo?.TaxpayerType || '-'}</td>
          </tr>
          <tr>
            <td class="tb" rowspan={2}>
              人员规模
            </td>
            <td rowspan={2}>{businessInfo?.StaffScale || '-'}</td>
            <td class="tb" rowspan={businessInfo?.StaffsOfBranch ? 1 : 2}>
              参保人数
            </td>
            <td rowspan={businessInfo?.StaffsOfBranch ? 1 : 2}>
              {toDash(insured.count)}
              {insured.from ? `（${insured.from}年报）` : ''}
            </td>
            <td class="tb" rowspan={2}>
              核准日期
            </td>
            <td rowspan="2">
              <div class="flex items-center">
                <span>{toDash(timeStamp2Date(businessInfo?.CheckDate))}</span>
                {timeStamp2Date(businessInfo?.CheckDate) && (
                  <q-copy class="ml-2" copy-value={timeStamp2Date(businessInfo.CheckDate)}></q-copy>
                )}
              </div>
            </td>
          </tr>
          {businessInfo?.StaffsOfBranch ? (
            <tr>
              <td class="tb" rowspan={businessInfo?.StaffsOfBranch ? 1 : 0}>
                分支机构参保人数
              </td>
              <td rowspan={businessInfo?.StaffsOfBranch ? 1 : 0}>
                {businessInfo.StaffsOfBranch?.c || businessInfo.StaffsOfBranch?.c === 0 ? businessInfo.StaffsOfBranch?.c : '-'}
                {businessInfo.StaffsOfBranch?.s && <span class="text-gray-400">（{businessInfo.StaffsOfBranch?.s}年报）</span>}
              </td>
            </tr>
          ) : (
            <tr></tr>
          )}
          <tr>
            <td class="tb">所属地区</td>
            <td>{this.fullArea}</td>
            <td class="tb">登记机关</td>
            <td>{toDash(businessInfo?.BelongOrg)}</td>
            <td class="tb">进出口企业代码</td>
            <td>
              <div class="flex items-center">{toDash(ixCode)}</div>
            </td>
          </tr>
          <tr>
            <td class="tb">所属行业</td>
            <td colspan="3">{addressDom}</td>
            <td class="tb">英文名</td>
            <td>
              <div class="flex items-center">
                <span>{toDash(businessInfo?.EnglishName)}</span>
                {businessInfo?.EnglishName && <q-copy class="ml-2" copy-value={businessInfo.EnglishName}></q-copy>}
              </div>
            </td>
          </tr>
          <tr>
            <td class="tb">
              <div class="flex item-center">
                <span class="leading-3.5">{this.registeredAddressLabel}</span>
                <Tooltip title="企业注册地址是在企业营业执照上登记的“住所”，一般情况下，企业以其主要办事机构所在地为住所，不同的城市对注册地址的要求也不一样，具体应以当地工商局要求为准。">
                  <QIcon type="icon-icon_zhushi" style={{ color: '#D8D8D8' }} class="ml-1 cursor-pointer" />
                </Tooltip>
              </div>
            </td>
            <td colspan="5">{businessInfo?.Address ? <div class="flex items-center">{businessInfo.Address}</div> : <span>-</span>}</td>
          </tr>
          {businessInfo?.LatestAnnualReportAddrInfo?.length > 0 && (
            <tr>
              <td class="tb">
                <div class="flex item-center">
                  <span class="leading-3.5">通信地址</span>
                  <Tooltip title="通信地址来源近两年年报披露的最新通信地址。">
                    <QIcon type="icon-icon_zhushi" style={{ color: '#D8D8D8' }} class="ml-1 cursor-pointer" />
                  </Tooltip>
                </div>
              </td>
              <td colspan="5">
                {businessInfo.LatestAnnualReportAddrInfo.map((reportAddr) => (
                  <div class="flex items-center mb-1">
                    <span>{reportAddr.Address}</span>
                    {reportAddr.Year ? `（${reportAddr.Year}年报）` : ''}
                  </div>
                ))}
              </td>
            </tr>
          )}
          <tr>
            <td class={`tb ${!businessInfo?.Scope ? 'org-color' : ''}`}>经营范围</td>
            <td colspan="5" class={`break-word ${!businessInfo?.Scope ? 'org-color' : ''}`}>
              <div class="flex items-center">
                <span>{toDash(businessInfo?.Scope)}</span>
                {businessInfo?.Scope && <q-copy class="ml-2" copy-value={businessInfo.Scope}></q-copy>}
              </div>
            </td>
          </tr>
        </tbody>
      </QPlainTable>
    );
  },
});

export default BusinessInfo;
