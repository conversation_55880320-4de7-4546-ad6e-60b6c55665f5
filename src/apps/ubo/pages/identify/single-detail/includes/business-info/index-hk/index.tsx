import { defineComponent, computed, ref, PropType, getCurrentInstance, VNode } from 'vue';
import OverseaInfo from './oversea-info';
import styles from './hkregisterinfo.module.less';
import type { DataItem, SortItem, BusinessInfoProps, ProcessedItem } from './types';
import QIcon from '@/components/global/q-icon';
import QEntityLink from '@/components/global/q-entity-link';
import PreviousNames from './previous-names/index';

export default defineComponent<BusinessInfoProps>({
  name: 'HkRegisterInfo',
  props: {
    businessInfo: {
      type: Object as PropType<Record<string, any>>,
      required: true,
      default: () => {},
    },
    keyNo: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const vm = getCurrentInstance()?.proxy;
    const hasStock = ref(false);
    const sortList = ref<SortItem[]>([]);

    const dataList = computed(() => {
      return props.businessInfo?.Data || [];
    });

    const customRenderLEI = (item: ProcessedItem, data: Record<string, DataItem>): VNode | string | null => {
      if (!item.Value) return null;
      return (
        <div>
          {item.Value}
          <a
            class="ml-2.5 text-#128bed"
            onClick={() => {
              vm?.$modal.showDimension('LEIDetail', { keyNo: props.keyNo });
            }}
          >
            详情<QIcon type="icon-wenzilianjiantou"></QIcon>
          </a>
        </div>
      );
    };
    // 获取无股票版本的排序列表
    const getSortListUnStock = (): SortItem[] => {
      const ctd = dataList.value?.find((item) => item.Name === 'CompTypeDetail');
      const fund = ctd?.Value === '有限合伙基金';
      const dissStatus = ['HK-DISS', 'HK-EXAD'].includes(props.businessInfo?.Status || '');

      const width = (item: DataItem, data: Record<string, DataItem>) => {
        const disableList = [
          'RegNo',
          'CompName',
          'CompNo',
          'CompStatusDetail',
          'CompStartDate',
          'CompTypeDetail',
          'ContactPhones',
          'ContactWebsite',
          'ContactEmail',
          'RegPlaceDetail',
          'CompDisDate',
          'RegAddress',
          'LEICode',
        ]
          .map((key) => data[key])
          ?.filter((val) => !val.disable);

        const last = disableList[disableList.length - 1];
        console.log('getSortList========>', disableList?.length, last?.Name, item.Name);
        if (disableList?.length % 2 > 0 && last?.Name === item.Name) {
          return '100%';
        }
        return '50%';
      };

      return [
        {
          key: 'RegNo',
          cnName: '商业登记号码',
          width,
        },
        {
          key: 'CompName',
          cnName: fund ? '基金名称' : '企业名称',
          width,
          value: (item: DataItem, data: Record<string, DataItem>) => {
            const names = [item?.Value, data.CompNameEn?.Value].filter((val) => val);
            return names?.map((v) => <div>{v}</div>) || '';
          },
          customRender: (item: ProcessedItem, data: Record<string, DataItem>): VNode | string => {
            const names = [item?.Value, data.CompNameEn?.Value].filter((val) => val);
            return names?.length > 1 ? <div>{names?.map((v) => <div>{v}</div>)}</div> : names?.[0] || '';
          },
        },
        {
          key: 'CompNo',
          cnName: '企业编号',
          disable: (item: DataItem) => !item?.Value,
          width,
        },
        {
          key: 'CompStatusDetail',
          cnName: fund ? '基金状态' : '企业状态',
          width,
        },
        {
          key: 'CompStartDate',
          cnName: '成立日期',
          width,
        },
        {
          key: 'CompTypeDetail',
          cnName: fund ? '基金类型' : '企业类型',
          width,
        },
        {
          key: 'ContactPhones',
          cnName: '电话',
          value: (item: DataItem, data: Record<string, DataItem>) => {
            let contacti = {};
            try {
              contacti = JSON.parse(data.ContactInfo?.Value || '{}');
            } catch (error) {}
            const now = (contacti as any)?.PhoneNumber;

            let contacth: any[] = [];
            try {
              contacth = JSON.parse(item?.Value || '[]');
            } catch (error) {}
            const his = contacth.map((val) => val.Tel) || [];

            return [now, ...his].filter((val) => val)?.join('<br>') || '-';
          },
          disable: (item: DataItem, data?: Record<string, DataItem>) => {
            let contacti = {};
            try {
              contacti = JSON.parse(data?.ContactInfo?.Value || '{}');
            } catch (error) {}
            const now = (contacti as any)?.PhoneNumber;

            let contacth: any[] = [];
            try {
              contacth = JSON.parse(item?.Value || '[]');
            } catch (error) {}
            const his = contacth.map((val) => val.Tel) || [];

            const list = [now, ...his].filter((val) => val);
            return !list.length;
          },
          ellipsis: 3,
          width,
        },
        {
          key: 'ContactWebsite',
          cnName: '官网',
          disable: (item: DataItem) => !item?.Value,
          width,
        },
        {
          key: 'ContactEmail',
          cnName: '邮箱',
          disable: (item: DataItem) => !item?.Value,
          width,
        },
        {
          key: 'RegPlaceDetail',
          cnName: '成立地方',
          disable: (item: DataItem) => !item?.Value,
          width,
        },
        {
          key: 'CompDisDate',
          cnName: '解散/不再是独立实体日期',
          disable: !dissStatus,
          width,
        },
        {
          key: 'RegAddress',
          cnName: '办事处地址',
          disable: (item: DataItem) => !item?.Value,
          width,
        },
        {
          key: 'RegImpmatters',
          cnName: '重要事项',
          ellipsis: 2,
          disable: !dataList.value?.find((item) => item.Name === 'RegImpmatters')?.Value,
          width: '100%',
        },
        {
          key: 'LEICode',
          cnName: '全球法人识别编码（LEI）',
          disable: (item: DataItem) => !item?.Value,
          customValue: 'lei',
          value: (item: DataItem) => ({
            keyNo: props.keyNo,
            value: item?.Value,
          }),
          customRender: customRenderLEI,
          width,
        },
        {
          key: 'CompNameHis',
          cnName: fund ? '基金曾用名' : '企业曾用名',
          customValue: 'CompNameHis',
          width: '100%',
          disable: (item: DataItem) => {
            try {
              return !JSON.parse(item?.Value || '[]')?.length;
            } catch {
              return true;
            }
          },
          customRender: (item: ProcessedItem, data: Record<string, DataItem>) => {
            return <PreviousNames value={item?.Value} />;
          },
        },
        {
          key: 'Remarks',
          cnName: '备注',
          ellipsis: 3,
          width: '100%',
          disable: (item: DataItem) => !item?.Value,
        },
      ];
    };

    // 获取有股票版本的排序列表
    const getSortList = (): SortItem[] => {
      const width = (item: DataItem, data: Record<string, DataItem>) => {
        const disableList = [
          'CompNo',
          'CompRepresentList',
          'CompStatusDetail',
          'CompStartDate',
          'RegisteredCapital',
          'CompTypeDetail',
          'RegPlaceDetail',
          'Mortgage',
          'Remarks',
          'LiquidationMode',
          'LEICode',
        ]
          .map((key) => data[key])
          ?.filter((val) => !val.disable);

        const last = disableList[disableList.length - 1];
        if ((disableList?.length === 10 || disableList?.length === 7) && last?.Name === item.Name) {
          return '100%';
        } else if (disableList?.length === 8 && last?.Name === item.Name) {
          return '66.667%';
        } else {
          return '33.333%';
        }
      };

      return [
        {
          key: 'RegNo',
          cnName: '商业登记号码',
          width: '33.333%',
        },
        {
          cnName: '企业名称',
          key: 'CompName',
          value: (item: DataItem, data: Record<string, DataItem>) => {
            const names = [item?.Value, data.CompNameEn?.Value].filter((val) => val);
            return names.length ? names.join('<br/>') : '-';
          },
          width: '66.667%',
        },
        {
          cnName: '企业编号',
          key: 'CompNo',
          disable: (item: DataItem) => item && !item?.Value,
          width,
        },
        {
          cnName: '董事长',
          key: 'CompRepresentList',
          value: (item: DataItem) => {
            return item?.Value || '';
          },
          customRender: (item: ProcessedItem) => {
            try {
              const list = JSON.parse(item?.Value || '[]');

              if (list.length) {
                return (
                  <div>
                    {list.map((val: any, index: number) => {
                      return (
                        <span>
                          <QEntityLink
                            coyObj={{
                              KeyNo: val.KeyNo,
                              Name: val.Name,
                            }}
                            ellipsis={false}
                          />
                          {index < list.length - 1 && '、'}
                        </span>
                      );
                    })}
                  </div>
                );
              } else {
                return '-';
              }
            } catch {
              return '-';
            }
          },
          width,
        },
        {
          cnName: '企业状态',
          key: 'CompStatusDetail',
          width,
        },
        {
          cnName: '成立日期',
          key: 'CompStartDate',
          width,
        },
        {
          cnName: '股本',
          key: 'RegisteredCapital',
          width,
        },
        {
          cnName: '企业类型',
          key: 'CompTypeDetail',
          width,
        },
        {
          cnName: '成立地方',
          key: 'RegPlaceDetail',
          width,
        },
        {
          cnName: '押记登记册',
          key: 'Mortgage',
          width,
          disable: (item: DataItem) => item && !item?.Value,
        },
        {
          cnName: '备注',
          key: 'Remarks',
          disable: (item: DataItem) => item && !item?.Value,
          width,
        },
        {
          cnName: '清盘模式',
          key: 'LiquidationMode',
          disable: (item: DataItem) => item && !item?.Value,
          width,
        },
        {
          key: 'LEICode',
          cnName: '全球法人识别编码（LEI）',
          disable: (item: DataItem) => !item?.Value,
          customValue: 'lei',
          value: (item: DataItem) => ({
            keyNo: props.keyNo,
            value: item?.Value,
          }),
          width,
        },
        {
          cnName: '企业曾用名',
          key: 'CompNameHis',
          width: '100%',
          customValue: 'CompNameHis',
          disable: (item: DataItem) => {
            try {
              return item && !JSON.parse(item?.Value || '[]')?.length;
            } catch {
              return true;
            }
          },
          customRender: (item: ProcessedItem, data: Record<string, DataItem>) => {
            return <PreviousNames value={item?.Value} />;
          },
        },
        {
          cnName: '企业地址',
          key: 'ContactAddress',
          width: '100%',
          disable: (item: DataItem) => item && !item?.Value,
        },
        {
          cnName: '重要事项',
          key: 'RegImpmatters',
          width: '100%',
          disable: (item: DataItem) => item && !item?.Value,
        },
      ];
    };

    // 监听数据变化，更新排序列表
    const updateSortList = () => {
      //6, 401（H股）,10（港股终止上市）
      const findListedFlag =
        props.businessInfo?.HeadInfo?.Tags?.find((v) => [6, 401, 10].includes(+v?.Type)) ||
        props.businessInfo?.HeadInfo?.TagsInfo?.find((v) => [6, 401, 10].includes(+v?.Type));
      if (findListedFlag) {
        sortList.value = getSortList();
        hasStock.value = true;
      } else {
        sortList.value = getSortListUnStock();
        hasStock.value = false;
      }
    };

    // 初始化
    updateSortList();

    return () => (
      <OverseaInfo sortList={sortList.value} dataList={dataList.value} lang="cn" class={styles.hkStockInfo}>
        {/* 自定义插槽内容 */}
        {/* <PreviousNames />
        <LeiValue lang="cn" /> */}
      </OverseaInfo>
    );
  },
});
