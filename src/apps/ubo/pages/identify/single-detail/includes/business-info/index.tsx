import { defineComponent, PropType, computed } from 'vue';
import CommonInfo from './index-common';
import HKInfo from './index-hk/index';
import LawFirmInfo from './index-lawfirm';
import SocialOrgInfo from './index-social-org';

export default defineComponent({
  name: 'BusinessInfo',
  props: {
    businessInfo: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    keyNo: {
      type: String,
      default: '',
      required: true,
    },
  },
  setup(props) {
    return {
      isHK: computed(() => props.keyNo.startsWith('h')),
      isLawFirm: computed(() => props.keyNo.startsWith('w')), //是否律所
      isSocialOrg: computed(() => props.keyNo.startsWith('s')), //是否社会组织
    };
  },

  render() {
    const { keyNo, isHK, isLawFirm, businessInfo, isSocialOrg } = this;
    return isHK ? (
      <HKInfo businessInfo={businessInfo} keyNo={keyNo} />
    ) : isLawFirm ? (
      <LawFirmInfo businessInfo={businessInfo} keyNo={keyNo} />
    ) : isSocialOrg ? (
      <SocialOrgInfo businessInfo={businessInfo} keyNo={keyNo} />
    ) : (
      <CommonInfo businessInfo={businessInfo} keyNo={keyNo} />
    );
  },
});
