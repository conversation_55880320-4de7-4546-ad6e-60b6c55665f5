import { defineComponent, computed, PropType } from 'vue';
import styles from './index.module.less';
/**
 * 企业曾用名组件相关类型定义
 */

/**
 * 名称项接口定义
 */
export interface NameItem {
  /** 开始日期 */
  startDate: string;
  /** 英文历史名称 */
  nameHisEn: string;
  /** 中文历史名称 */
  nameHis: string;
  /** 结束日期 */
  endDate: string;
  /** 格式化后的日期字符串 */
  date?: string;
  /** 格式化后的文本内容 */
  text?: string;
}

/**
 * 企业曾用名组件Props接口定义
 */
export interface PreviousNamesProps {
  /** 国家类型 */
  countryType?: string;
  /** 原始数据值 */
  value?: string;
  /** 语言设置 */
  lang?: string;
}

/**
 * 企业曾用名组件
 * 用于显示企业的历史名称信息
 */
export default defineComponent<PreviousNamesProps>({
  name: 'PreviousNames',
  props: {
    countryType: {
      type: String as PropType<string>,
      default: 'hkStock',
    },
    value: {
      type: String as PropType<string>,
      default: '',
    },
  },
  setup(props) {
    /**
     * 解析曾用名数据
     * @param value - 原始数据字符串
     * @param countryType - 国家类型
     * @returns 解析后的名称列表
     */
    const getPreviousNames = (value?: string): NameItem[] => {
      if (!value) {
        return [];
      }

      let list: NameItem[] = [];
      const regex = /\[[^\]]*\]/;

      if (regex.test(value)) {
        // JSON格式数据
        let data: any[] = [];
        try {
          data = JSON.parse(value);
        } catch (error) {
          console.error('解析JSON数据失败:', error);
        }

        list = data.map((item: any) => {
          const i = item || {};
          return {
            startDate: i.startDate || i.StartDate || '',
            nameHisEn: i.nameHisEn || i.NameHisEn || '',
            nameHis: i.nameHis || i.historyValue || i.NameHis || i.HistoryValue || '',
            endDate: i.endDate || i.EndDate || '',
          };
        });
      } else {
        // 普通字符串格式
        list = value.split('。').map((item: string) => ({
          startDate: '',
          nameHisEn: item || '',
          nameHis: '',
          endDate: '',
        }));
      }

      return list.map((item: NameItem) => {
        let date = '';
        if (item.startDate) {
          date = `（${item.startDate}生效）`;
        }

        const nameList = [item.nameHis, item.nameHisEn].filter((name) => name);
        const n = nameList && nameList.length ? nameList.join('<br/>') : '';
        const text = `<span class="text-dk">${n}</span> ${date}`;

        return {
          ...item,
          date,
          text,
        };
      });
    };

    /**
     * 计算后的名称列表
     */
    const nameList = computed(() => {
      return getPreviousNames(props.value);
    });

    /**
     * 显示全部名称的弹窗
     */
    const showNamesModal = () => {
      // TODO: 实现弹窗逻辑，需要根据项目实际的弹窗服务进行调整
      console.log('显示全部曾用名:', nameList.value);
    };

    return () => {
      if (!nameList.value.length) {
        return <div>-</div>;
      }

      const firstItem = nameList.value[0];
      if (!firstItem) {
        return <div>-</div>;
      }

      return (
        <div class={styles.previousNames}>
          {firstItem.nameHisEn && firstItem.nameHis ? (
            <div>
              <div class={styles.name} domPropsInnerHTML={firstItem.nameHis}></div>
              <div class={styles.nameEn}>
                {firstItem.nameHisEn}
                {firstItem.date || ''}
                {nameList.value.length > 1 && (
                  <a onClick={showNamesModal}>
                    {' '}
                    查看全部
                    <i class="iconfont icon-wenzilianjiantou font-14"></i>
                  </a>
                )}
              </div>
            </div>
          ) : (
            <div class={styles.name}>
              <span domPropsInnerHTML={firstItem.nameHis || firstItem.nameHisEn}></span>
              <span domPropsInnerHTML={firstItem.date || ''}></span>
              {nameList.value.length > 1 && (
                <a onClick={showNamesModal}>
                  {' '}
                  查看全部
                  <i class="iconfont icon-wenzilianjiantou font-14"></i>
                </a>
              )}
            </div>
          )}
        </div>
      );
    };
  },
});
