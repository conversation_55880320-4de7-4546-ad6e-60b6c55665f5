.overseaInfo {
  .listRow {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    border-top: 1px solid #E4EEF6;
    border-right: 1px solid #E4EEF6;

    .item {
      display: flex;
      width: 50%;

      .itemLabel {
        width: 240px;
        padding: 10px;
        text-align: left;
        background: #F2F9FC;
        border-right: 1px solid #E4EEF6;
        border-left: 1px solid #E4EEF6;
        border-bottom: 1px solid #E4EEF6;
        display: flex;
        flex-direction: column;
        justify-content: center;
        color: #333;
        line-height: 22px;
        font-size: 13px;

        .enName {
          color: #999;
          font-size: 12px;
          line-height: 18px;
          margin-top: 2px;
        }
      }

      .itemValue {
        flex: 1;
        font-size: 13px;
        line-height: 22px;
        display: flex;
        align-items: center;
        text-align: left;
        color: #333;
        position: relative;
        padding: 10px;
        word-break: break-word;
        border-bottom: 1px solid #E4EEF6;

        .ellipsisMore {
          color: #1890ff;
          cursor: pointer;
          margin-left: 4px;
        }
      }

      .tipsContent {
        max-width: 400px;
        word-break: break-word;
      }
    }
  }

  // 响应式布局
  @media (max-width: 1200px) {
    .listRow {
      .item {
        width: 100%;

        .itemLabel {
          width: 200px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .listRow {
      .item {
        flex-direction: column;

        .itemLabel {
          width: 100%;
          border-right: none;
        }

        .itemValue {
          border-left: 1px solid #E4EEF6;
        }
      }
    }
  }
}
