/**
 * 企业曾用名组件样式
 */

.previousNames {
  line-height: 26px;

  .name {
    color: #333;
    line-height: 22px;

    a {
      color: #1890ff;
      cursor: pointer;
      text-decoration: none;

      &:hover {
        color: #40a9ff;
      }

      i {
        margin-left: 4px;
      }
    }
  }

  .nameEn {
    color: #808080;
    line-height: 22px;

    a {
      color: #1890ff;
      cursor: pointer;
      text-decoration: none;

      &:hover {
        color: #40a9ff;
      }

      i {
        margin-left: 4px;
      }
    }
  }
}

// 全局样式补充
:global {
  .text-dk {
    color: #333;
  }

  .font-14 {
    font-size: 14px;
  }
}
