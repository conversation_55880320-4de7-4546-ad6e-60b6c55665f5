.hkregisterinfo {
  .hkStockInfo {
    // 继承 overseaInfo 的样式

    // 特定于 hk 股票信息的样式调整
    .listRow {
      .item {
        // 三列布局时的宽度调整
        &.width33 {
          width: 33.333%;
        }

        &.width66 {
          width: 66.667%;
        }

        &.width100 {
          width: 100%;
        }

        .itemValue {
          // 链接样式
          a {
            color: #1890ff;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }

          // 多行文本处理
          .multiLine {
            line-height: 1.5;
          }
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 1400px) {
  .hkregisterinfo {
    .hkStockInfo {
      .listRow {
        .item {
          &.width33 {
            width: 50%;
          }

          &.width66 {
            width: 100%;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .hkregisterinfo {
    .hkStockInfo {
      .listRow {
        .item {
          &.width33,
          &.width66,
          &.width100 {
            width: 100%;
          }
        }
      }
    }
  }
}
