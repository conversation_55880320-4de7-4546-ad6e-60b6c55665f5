import { defineComponent, computed, PropType } from 'vue';
import { Tooltip } from 'ant-design-vue';
import styles from './oversea-info.module.less';
import type { DataItem, SortItem, ProcessedItem, OverseaInfoProps } from './types';

/**
 * 海外信息组件
 */
export default defineComponent<OverseaInfoProps>({
  name: 'OverseaInfo',
  props: {
    sortList: {
      type: Array as PropType<SortItem[]>,
      default: () => [],
    },
    dataList: {
      type: [Array, Object] as PropType<DataItem[] | Record<string, DataItem>>,
      default: () => [],
    },
    lang: {
      type: String,
      default: '',
    },
  },
  setup(props, { slots }) {
    /**
     * 将 dataList 转换为对象格式
     */
    const dataObj = computed(() => {
      if (Array.isArray(props.dataList)) {
        return props.dataList.reduce(
          (obj, item) => {
            obj[item.Name || ''] = item;
            return obj;
          },
          {} as Record<string, DataItem>
        );
      }
      return {};
    });

    /**
     * 处理省略号逻辑
     */
    const handleEllipsis = (val: string, ellipsis?: number) => {
      if (!ellipsis || !val) return val;

      const lines = val.split('<br>').filter((line) => line.trim());
      if (lines.length <= ellipsis) {
        return <span domPropsInnerHTML={val} />;
      }

      const visibleLines = lines.slice(0, ellipsis);
      const visibleContent = visibleLines.join('<br>');
      const tooltipContent = lines.join('\n');

      return (
        <Tooltip title={tooltipContent} placement="topLeft">
          <span>
            <span domPropsInnerHTML={visibleContent} />
            <span class={styles.ellipsisMore}>...等{lines.length}项</span>
          </span>
        </Tooltip>
      );
    };

    /**
     * 处理列表数据
     */
    const processedList = computed((): ProcessedItem[] => {
      return props.sortList
        .map((item) => {
          const data = dataObj.value?.[item.key];
          if (data) {
            data.disable = typeof item.disable === 'function' ? item.disable(data, dataObj.value) : item.disable || false;
          }
          const width = typeof item.width === 'function' && data ? item.width(data, dataObj.value) : item.width || '50%';
          const value = typeof item.value === 'function' && data ? item.value(data, dataObj.value) : data?.Value;
          const cnName = typeof item.cnName === 'function' && data ? item.cnName(data, dataObj.value) : item.cnName || data?.DisplayCNName;
          const enName = item.enName || data?.DisplayName;
          const labelName = props.lang === 'en' ? enName : cnName;

          return {
            ...item,
            ...data,
            key: item.key,
            value: value || '-',
            cnName,
            enName,
            labelName: labelName || '',
            width,
          };
        })
        .filter((item) => !item.disable) as ProcessedItem[];
    });

    /**
     * 渲染自定义插槽内容
     */
    const renderCustomValue = (item: ProcessedItem) => {
      if (item.customRender) {
        return item.customRender(item, dataObj.value);
      }
      return null;
    };
    console.log(processedList.value);

    return () => (
      <div class={styles.overseaInfo}>
        <div class={styles.listRow}>
          {processedList.value.map((item, index) => {
            const customContent = renderCustomValue(item);
            const displayValue = customContent || handleEllipsis(item.Value, item.ellipsis);

            return (
              <div key={`${item.key}-${index}`} class={styles.item} style={{ width: item.width }}>
                <div class={styles.itemLabel}>
                  <div>{item.cnName}</div>
                </div>
                <div class={styles.itemValue}>{displayValue}</div>
              </div>
            );
          })}
        </div>
      </div>
    );
  },
});
