import moment from 'moment';
import { isString } from 'lodash';

export const operTypeName = (operType) => {
  const iOperType = +operType;
  if (iOperType === 1) {
    return '法定代表人';
  } else if (iOperType === 2) {
    return '执行事务合伙人';
  } else if (iOperType === 3) {
    return '负责人';
  } else if (iOperType === 4) {
    return '经营者';
  } else if (iOperType === 5) {
    return '投资人';
  } else if (iOperType === 6) {
    return '董事长';
  } else if (iOperType === 7) {
    return '理事长';
  } else if (iOperType === 8) {
    return '代表人';
  }
  return '';
};

const getBusinessKind = (businessInfo) => {
  // 工商信息根据企业类型字段调整
  // kind=1合伙企业，2个体户，3个人独资企业，4合作社
  let kind = 0;
  if (businessInfo?.CommonList?.length) {
    businessInfo.CommonList.forEach((item) => {
      if (+item.Key === 46) {
        kind = parseInt(item.Value);
      }
    });
  }
  return kind;
};

export const getRegisteredAddressLabel = (businessInfo, result) => {
  const kind = getBusinessKind(businessInfo);
  switch (kind) {
    case 1:
      result = '主要经营场所';
      break;
    case 2:
      result = '经营场所';
      break;
    case 3:
    case 4:
      result = '住所';
      break;
  }
  return result;
};

export const getRegisteredCapitalLabel = (businessInfo) => {
  const kind = getBusinessKind(businessInfo);
  let result = '注册资本';
  switch (kind) {
    case 1:
    case 3:
      result = '出资额';
      break;
    case 4:
      result = '成员出资总额';
      break;
  }
  return result;
};
export const toDash = (str: string | number) => {
  return str || str === 0 ? str : '-';
};
export const timeStamp2Date = (time) => {
  if (!time || time === '0' || time === 0 || time === -1) {
    return '';
  }
  return moment.unix(time).format('YYYY-MM-DD');
};

export const rmRMB = (str) => {
  if (str && isString(str)) {
    return str.replace(/人民币/g, '');
  }
  return str;
};

export const getCreditCodeLabel = (keyNo: string): string => {
  console.log('=====>', keyNo);
  if (keyNo.startsWith('w')) {
    return '统一社会信用编码';
  }
  return '统一社会信用代码';
};

export const getBaseInfoTitle = (keyNo): string => {
  switch (true) {
    case keyNo?.startsWith?.('w'):
      return '基本信息';
    case keyNo?.startsWith?.('s'):
    case keyNo?.startsWith?.('h'):
      return '登记信息';
    default:
      return '工商信息';
  }
};
