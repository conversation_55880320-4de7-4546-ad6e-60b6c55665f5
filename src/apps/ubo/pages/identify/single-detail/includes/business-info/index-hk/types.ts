/**
 * 香港注册信息相关类型定义
 */

import { VNode } from 'vue';

/**
 * 数据项接口定义
 * 统一数据项的结构，支持大小写兼容
 */
export interface DataItem {
  /** 主键 */
  Key?: string;
  /** 名称 */
  Name?: string;
  /** 值 */
  Value: any;
  /** 中文显示名称 */
  DisplayCNName?: string;
  /** 英文显示名称 */
  DisplayName?: string;
  TitleName?: string;
  disable?: boolean;
}

/**
 * 排序项接口定义
 * 用于定义列表项的显示和行为配置
 */
export interface SortItem {
  /** 字段键名 */
  key: string;
  /** 中文名称，可以是字符串或函数 */
  cnName?: string | ((data: DataItem, dataObj: Record<string, DataItem>) => string);
  /** 英文名称 */
  enName?: string;
  /** 值处理函数 */
  value?: (data: DataItem, dataObj: Record<string, DataItem>) => any;
  /** 宽度，可以是字符串或函数 */
  width?: string | ((data: DataItem, dataObj: Record<string, DataItem>) => string);
  /** 禁用状态，可以是布尔值或函数 */
  disable?: boolean | ((data: DataItem, dataObj: Record<string, DataItem>) => boolean);
  /** 省略号显示行数 */
  ellipsis?: number;
  /** 自定义值类型 */
  customValue?: string;
  customRender?: (data: ProcessedItem, dataObj: Record<string, DataItem>) => VNode | string | null;
}

/**
 * 处理后的项目接口
 * 继承自SortItem，添加了运行时计算的属性
 */
export interface ProcessedItem extends SortItem {
  Value?: any;
  /** 标签名称 */
  labelName?: string;
  /** 计算后的宽度 */
  width?: string;
  /** 计算后的禁用状态 */
  disable?: boolean;
  /** 其他动态属性 */
  [key: string]: any;
}

/**
 * 组件Props接口定义
 */
export interface BusinessInfoProps {
  /** 业务信息数据 */
  businessInfo: Record<string, any>;
  /** 关键编号 */
  keyNo: string;
}

/**
 * 海外信息组件Props接口定义
 */
export interface OverseaInfoProps {
  /** 排序列表 */
  sortList: SortItem[];
  /** 数据列表 */
  dataList: DataItem[] | Record<string, DataItem>;
  /** 语言设置 */
  lang?: string;
}
