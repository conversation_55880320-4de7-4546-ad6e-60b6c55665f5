/**
 * 律所信息
 */
import { defineComponent, PropType } from 'vue';
import QPlainTable from '@/components/global/q-plain-table';
import { toDash, timeStamp2Date, rmRMB } from './utils';
import QEntityLink from '@/components/global/q-entity-link';
import CompanyLogo from '@/components/company-logo';
import QIcon from '@/components/global/q-icon';

const resetArea = (area?: string | any) => {
  if (area) {
    if (area.Province || area.City || area.County) {
      return `${area.Province || ''}${area.City || ''}${area.County || ''}` || '-';
    }
    return area
      ?.replace?.('北京市北京市', '北京市')
      .replace?.('上海市上海市', '上海市')
      .replace?.('天津市天津市', '天津市')
      .replace?.('重庆市重庆市', '重庆市');
  } else {
    return '';
  }
};

export default defineComponent({
  name: 'BusinessInfo',
  props: {
    businessInfo: {
      type: Object as PropType<Record<string, any>>,
      default: () => {},
    },
    keyNo: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    return {};
  },
  render() {
    const { businessInfo } = this;

    return (
      <div>
        <QPlainTable>
          <colgroup>
            <col width="13%" />
            <col width="21%" />
            <col width="13%" />
            <col width="20%" />
            <col width="13%" />
            <col width="20%" />
          </colgroup>
          <tbody>
            <tr>
              <td class="tb">统一社会信用编码</td>
              <td>{toDash(businessInfo?.CreditCode)}</td>
              <td class="tb">成立日期</td>
              <td>{toDash(timeStamp2Date(businessInfo?.StartDate))}</td>
              <td class="tb">注册资本</td>
              <td>{rmRMB(toDash(businessInfo?.RegistCapi))}</td>
            </tr>
            <tr>
              <td class="tb">负责人</td>
              <td>
                {businessInfo?.Oper?.Name ? (
                  <div class="flex">
                    <CompanyLogo
                      src={businessInfo?.Oper.ImageUrl}
                      id={businessInfo.Oper.KeyNo}
                      name={businessInfo.Oper.Name}
                      hasimage={businessInfo?.Oper.HasImage ? 1 : 0}
                      size="40px"
                      class="shrink-0"
                    ></CompanyLogo>
                    <div class="ml-2.5 flex flex-col">
                      <QEntityLink
                        coyObj={{
                          KeyNo: businessInfo.Oper?.KeyNo,
                          Name: businessInfo.Oper?.Name,
                          Org: businessInfo.Oper?.Org,
                        }}
                        ellipsis={false}
                      />
                      {businessInfo?.Oper?.CompanyCount ? (
                        <a
                          class="text-#FF722D mt-1 cursor-pointer"
                          target="_blank"
                          href={`/embed/beneficaryDetail?personId=${businessInfo.Oper.KeyNo}&title=${encodeURIComponent(businessInfo.Oper.Name)}`}
                        >
                          关联{businessInfo.Oper.CompanyCount}家企业<QIcon type="icon-wenzilianjiantou"></QIcon>
                        </a>
                      ) : (
                        ''
                      )}
                    </div>
                  </div>
                ) : (
                  <div>-</div>
                )}
              </td>
              <td class="tb">律师人数</td>
              <td>{toDash(businessInfo?.LawyerCount)}</td>
              <td class="tb">所属地区</td>
              <td>{toDash(resetArea(businessInfo?.Area))}</td>
            </tr>
            <tr>
              <td class="tb">电话</td>
              <td colspan={5}>{toDash(businessInfo?.ContactInfo?.PhoneNumber)}</td>
            </tr>
            <tr>
              <td class="tb">机构简介</td>
              <td colspan={5}>
                <div domPropsInnerHTML={businessInfo?.Intro || '-'}></div>
              </td>
            </tr>
          </tbody>
        </QPlainTable>
      </div>
    );
  },
});
