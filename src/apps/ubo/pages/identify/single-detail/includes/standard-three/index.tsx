import { defineComponent, reactive } from 'vue';
import StandardListBase from '../standard-list-base';
import { IQRichTableColumn, IQRichTablePagination } from '@/components/global/q-rich-table';

interface IRecord {
  uboDate: string;
  keyNo: string;
  name: string;
  nameRemark?: string;
  position?: string;
}

export default defineComponent({
  name: 'StandardListThree',
  props: {
    list: {
      type: Array as () => IRecord[],
      default: () => [],
    },
  },
  setup(props) {
    // 模拟数据
    const tableData = reactive({
      list: props.list,
    });
    console.log('tableData', tableData);
    // 列定义
    const columns: IQRichTableColumn[] = [
      {
        title: '受益所有人',
        dataIndex: 'name',
        width: 272,
        customRender: (text, row: IRecord) => {
          const remark = row.nameRemark ? <span class="text-#BBB ml-1">({row.nameRemark})</span> : '';
          return (
            <div>
              <q-entity-link coyObj={{ KeyNo: row.keyNo, Name: row.name }}></q-entity-link>
              {remark}
            </div>
          );
        },
      },
      {
        title: '任职类型',
        dataIndex: 'position',
        width: 540,
      },
      {
        title: '受益所有权形成日期',
        dataIndex: 'uboDate',
        width: 180,
        // 处理空值显示
        customRender: (text) => text || '-',
      },
    ];

    return {
      tableData,
      columns,
    };
  },
  render() {
    return <StandardListBase title="标准三：控制/影响自然人" columns={this.columns} list={this.tableData.list} isNet={false} />;
  },
});
