.headerContainer {
  background: #fff;
  height: 50px;
  border-radius: 4px 4px 0 0;
  position: relative;

  .header {
    width: 400px;
    height: 50px;
    border-radius: 4px 4px 0 0;

    &.green {
      background: linear-gradient(
        90deg,
        rgba(0, 173, 101, 0.15) 0%,
        rgba(18, 139, 237, 0) 100%,
        rgba(255, 255, 255, 0) 100%
      );
    }

    &.blue {
      background: linear-gradient(
        90deg,
        rgba(18, 139, 237, 0.15) 0%,
        rgba(18, 139, 237, 0) 100%,
        rgba(255, 255, 255, 0) 100%
      );
    }

    &.red {
      background: linear-gradient(
        90deg,
        rgba(255, 0, 0, 0.1) 0%,
        rgba(255, 5, 5, 0) 100%,
        rgba(255, 255, 255, 0) 100%
      );
    }
  }

  .headerPosition {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    padding: 0 15px;
    font-size: 16px;
    font-weight: bold;
    line-height: 24px;
    letter-spacing: 0;

    /* 0中性/中性1-#333333 */
    color: #333;
    display: flex;
    align-items: center;

    .tip {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: normal;
      letter-spacing: 0;

      /* 0中性/中性2-#666666 */
      color: #666;

      .line {
        display: inline-block;
        margin: 0 10px;
        width: 1px;
        height: 12px;
        background: #EEE;
        box-shadow: inset 1px 0 0 0 #999;
      }

      .iconfont {
        color: #128bed;
      }
    }

    &.green {
      .overview {
        color: #00ad65;
      }
    }

    &.blue {
      .overview {
        color: #128bed;
      }
    }

    &.red {
      .overview {
        color: #f04040;
      }
    }
  }

  .function {
    position: absolute;
    right: 15px;
    top: 10px;
  }
}