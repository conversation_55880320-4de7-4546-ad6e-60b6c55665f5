import { defineComponent, computed, PropType } from 'vue';
import { Tooltip } from 'ant-design-vue';
import styles from './result-overview.module.less';
import { StatusList, StatusItem } from '../../config';
import QIcon from '@/components/global/q-icon';

export const ResultOverview = defineComponent({
  name: 'ResultOverview',
  props: {
    item: {
      type: Object as PropType<{ beneType?: string }>,
      default: () => ({}),
    },
    tip: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const currentStatus = computed<StatusItem | any>(() => {
      return StatusList[props.item?.beneType || ''] || {};
    });

    return {
      currentStatus,
    };
  },
  render() {
    return (
      <div class={styles.headerContainer}>
        <div class={[styles.header, styles[this.currentStatus?.status]]}></div>
        <div class={[styles.headerPosition, styles[this.currentStatus?.status]]}>
          <span>受益所有人：</span>
          <span class={styles.overview}>{this.currentStatus?.text}</span>
          {this.tip && (
            <span class={styles.tip}>
              <span class={styles.line}></span>
              <span>
                <Tooltip title="判定理由" placement="bottom">
                  <QIcon type="icon-pandingliyou" class={[styles.overview, 'text-16px mr-1']}></QIcon>
                </Tooltip>
                <span class={styles.tipText} domPropsInnerHTML={this.tip}></span>
              </span>
            </span>
          )}
        </div>
      </div>
    );
  },
});
