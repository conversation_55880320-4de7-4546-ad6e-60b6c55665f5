import { defineComponent } from 'vue';
import { ResultOverview } from './index';

export default defineComponent({
  name: 'ResultOverviewDemo',
  setup() {
    // 示例数据
    const normalItem = { beneType: 'N' };
    const simpleItem = { beneType: 'S' };
    const exemptItem = { beneType: 'E' };
    const emptyItem = { beneType: 'NE' };

    const tipExample = '根据《法人和非法人组织统一社会信用代码编码规则》判定';

    return {
      normalItem,
      simpleItem,
      exemptItem,
      emptyItem,
      tipExample,
    };
  },
  render() {
    return (
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div>
          <h3>正常识别示例</h3>
          <ResultOverview item={this.normalItem} tip={this.tipExample} />
          <div style="height: 100px; background: #f5f5f5; padding: 20px;">内容区域</div>
        </div>

        <div>
          <h3>简易识别示例</h3>
          <ResultOverview item={this.simpleItem} tip={this.tipExample} />
          <div style="height: 100px; background: #f5f5f5; padding: 20px;">内容区域</div>
        </div>

        <div>
          <h3>豁免识别示例</h3>
          <ResultOverview item={this.exemptItem} tip={this.tipExample} />
          <div style="height: 100px; background: #f5f5f5; padding: 20px;">内容区域</div>
        </div>

        <div>
          <h3>识别结果为空示例</h3>
          <ResultOverview item={this.emptyItem} tip={this.tipExample} />
          <div style="height: 100px; background: #f5f5f5; padding: 20px;">内容区域</div>
        </div>
      </div>
    );
  },
});
