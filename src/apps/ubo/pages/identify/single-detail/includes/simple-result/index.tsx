import { defineComponent, reactive } from 'vue';
import StandardListBase from '../standard-list-base';
import { IQRichTableColumn, IQRichTablePagination } from '@/components/global/q-rich-table';

interface IRecord {
  id: string;
  appointDate: string;
  keyNo: string;
  name: string;
  mergeInd: number;
}

export default defineComponent({
  name: 'SimpleResult',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    // 模拟数据
    const tableData = reactive({
      list: props.list,
    });
    const paginationConfig = reactive<IQRichTablePagination>({
      total: 0,
      current: 1,
      pageSize: 0,
      onChange: () => {},
      onShowSizeChange: () => {},
    });
    // 列定义
    const columns: IQRichTableColumn[] = [
      {
        title: '受益所有人',
        dataIndex: 'name',
        width: 220,
        customRender: (text, row) => {
          return <q-entity-link coyObj={{ KeyNo: row.operKeyNo, Name: row.operName }}></q-entity-link>;
        },
      },
      {
        title: '受益所有人类型',
        dataIndex: 'resultType',
        width: 220,
      },
      {
        title: '任职类型',
        dataIndex: 'position',
        width: 200,
      },
      {
        title: '受益所有权形成日期',
        dataIndex: 'uboDate',
        width: 180,
        customRender: (text) => text || '-',
      },
    ];

    return {
      tableData,
      columns,
    };
  },
  render() {
    return <StandardListBase columns={this.columns} list={this.tableData.list} isNet={false} />;
  },
});
