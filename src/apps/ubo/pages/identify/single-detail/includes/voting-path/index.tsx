import QEntityLink from '@/components/global/q-entity-link';
import QGlossaryInfo from '@/components/global/q-glossary-info';
import QModal from '@/components/global/q-modal';
import QPlainTable from '@/components/global/q-plain-table';
import { createPromiseDialog } from '@/components/promise-dialogs';
import { company } from '@/shared/services';
import { computed, defineComponent, onMounted, ref } from 'vue';
import { handleAcualControll4Data, handleDetailWithListed } from './utils';
import { cloneDeep } from 'lodash';
import AppPath from './widgets/app-tdpath/index.vue';
import KzrY from '@/shared/charts/kzr-y';

const VotingPath = defineComponent({
  name: 'VotingPath',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const name = ref('');
    const keyNo = ref('');
    const viewData = ref<Record<string, any>>({});
    const nameLabel = ref('实际控制人');
    const totalPercentLabel = ref('表决权比例');
    const pathLabel = ref('控制链');
    const isPathChainCustom = ref(false);
    const stockName = ref('');
    const pathTsId = ref('');
    const type = computed(() => props.params.type || 'control');
    const isControl = ref(false);
    const isInvest = ref(true);
    const reverse = ref(false);

    const getParams = (item) => {
      const {
        mutiActual,
        Name,
        realNames,
        ControlPercent,
        RealPaths,
        KeyNo,
        isShowMoreInfo,
        IsShowCollapsedInfo,
        ImageUrl,
        pathList,
        showControllChart,
        source,
        chartData,
      } = item;
      if (isShowMoreInfo) {
        pathTsId.value = '779';
      } else if (IsShowCollapsedInfo) {
        pathTsId.value = '214';
        // 返回一个对象，包含mutiActual、realNames、pathList、pathTsId、ControlPercent、RealPaths
      }
      viewData.value = {
        source,
        chartData,
        Name: mutiActual ? realNames : Name,
        PercentTotal: ControlPercent,
        Paths: RealPaths,
        KeyNo: mutiActual ? '' : KeyNo,
        ImageUrl: mutiActual ? '' : ImageUrl,
        nameLabel: nameLabel,
        totalPercentLabel: totalPercentLabel,
        pathLabel: '控制链',
        pathTsId,
        mutiActual: !!mutiActual,
        pathList,
        showControllChart,
      };
      if (type.value === 'control') {
        isControl.value = true;
        reverse.value = true;
        isInvest.value = false;
      } else if (type.value === 'shareholding') {
        isControl.value = false;
        reverse.value = true;
        isInvest.value = false;
      }
    };

    const handleData = (res) => {
      const resCopy = { Result: res.result };
      const dataX = res.actual || res.yisiActual || [];
      const data = cloneDeep(dataX);
      const data0 = data[0];
      if (data0) {
        if (!data0.realNames) {
          data0.ControlPercent = data0.Names?.ControlPercent;
          data0.realNames = data0?.Names?.PersonList;
          data0.mutiActual = data0.Names.PersonList.length > 1;
        } else {
          if (data0.Names && data0.Names.PersonList && data0.Names.PersonList.length > 0) {
            data0.ControlPercent = data0.Names.ControlPercent;
            if (!data0.realNames) {
              data0.realNames = data0.Names.PersonList;
              data0.mutiActual = data0.Names.PersonList.length > 1;
            }
          }
        }
        if (data0.realNames && data0.realNames.length > 0) {
          if (!data0.Name) {
            data0.Name = data0.realNames[0].Name;
          }
          if (!data0.KeyNo) {
            data0.KeyNo = data0.realNames[0].KeyNo;
          }
        }
        data0.showControllChart = true;
        data0.source = 'ubo_id';
        data0.chartData = handleDetailWithListed(resCopy, props.params.keyNo, props.params.name);
        console.log('🚀 ~ handleData ~ data0.chartData:', data0.chartData);
        getParams(data0);
      }
    };

    const fetchData = async () => {
      if (!props.params.keyNo) return;
      const res = await company.getVotingPath(props.params.keyNo, {
        companyId: props.params.keyNo,
        isCollapsed: true,
        isOnlyMainPath: false,
      });
      const detail = handleAcualControll4Data(res.Result);
      handleData(detail);
      return res;
    };

    const handleCancel = () => {
      emit('resolve', null);
    };

    onMounted(async () => {
      await fetchData();
      // setTimeout(() => {
      //   if (actualcontroller.value) {
      //     actualcontroller.value.onRefreshNew(showViewData.chartData, showViewData.source);
      //   }
      // }, 100);
    });

    return {
      keyNo,
      name,
      nameLabel,
      type,
      viewData,
      handleCancel,
      totalPercentLabel,
      isPathChainCustom,
      pathLabel,
      pathTsId,
      isControl,
      reverse,
      isInvest,
    };
  },
  render() {
    return (
      <QModal visible={true} size="x-large" title="控制链" footer={false} onCancel={this.handleCancel}>
        <QPlainTable>
          {/* {this.stockName && (
            <tr>
              <td class="tb" width="20%">
                股东名称
              </td>
              <td colspan="5">
                <QEntityLink coyObj={{ KeyNo: this.keyNo, Name: this.name }} />
              </td>
            </tr>
          )} */}
          <tr>
            <td class="tb" width="20%">
              {this.nameLabel}
            </td>
            <td colspan="5">
              {this.type === 'control' && this.viewData.mutiActual ? (
                <QEntityLink coyArr={this.viewData.Name} />
              ) : (
                <QEntityLink coyObj={{ KeyNo: this.viewData.KeyNo, Name: this.viewData.Name }} />
              )}
            </td>
          </tr>
          {this.type !== 'control' && (
            <tr>
              <td class="tb" width="20%">
                {this.viewData.PercentTotalLabel || '总持股比例'}
              </td>
              <td width="20%">{this.viewData.PercentTotal || '-'}</td>
              <td class="tb" width="15%">
                {this.viewData.StockPercentLabel || '直接持股比例'}
              </td>
              <td width="15%">{this.viewData.StockPercent || '-'}</td>
              <td class="tb" width="15%">
                {this.viewData.IndirectStockPercentLabel || '间接持股比例'}
              </td>
              <td width="15%">{this.viewData.IndirectStockPercent || '-'}</td>
            </tr>
          )}
          {this.type === 'control' && this.viewData.PercentTotal && (
            <tr>
              <td class="tb" width="20%">
                {this.totalPercentLabel}
              </td>
              <td colspan="3">{this.viewData.PercentTotal || '-'}</td>
            </tr>
          )}
          {this.isPathChainCustom && (
            <tr>
              <td class="tb" width="20%">
                直接持股
              </td>
              <td colspan="5">{this.viewData.DirectPercent || '-'}</td>
            </tr>
          )}
          {this.viewData.showControllChart && (
            <tr>
              <td class="tb" width="20%">
                控制图谱
              </td>
              <td colspan="5" style={{ padding: '0px' }}>
                <div style={{ height: '450px' }}>
                  <KzrY
                    typeMini
                    iframe
                    paths={this.viewData.chartData}
                    companyInfo={{
                      name: this.name,
                      keyNo: this.keyNo,
                    }}
                  />
                </div>
              </td>
            </tr>
          )}
          <tr>
            <td class="tb" width="20%">
              {this.pathLabel}
              {this.pathTsId && <QGlossaryInfo info-id={this.pathTsId} />}
            </td>
            <td colspan="5">
              <AppPath
                name={this.name}
                key-no={this.keyNo}
                is-control={this.isControl}
                reverse={this.reverse}
                is-invest={this.isInvest}
                paths={this.viewData.Paths}
                list={this.viewData.pathList}
              />
            </td>
          </tr>
        </QPlainTable>
      </QModal>
    );
  },
});

export default VotingPath;

export const openVotingPath = createPromiseDialog(VotingPath);
