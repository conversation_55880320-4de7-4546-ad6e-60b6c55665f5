import _ from 'lodash';
import { handleLinkForKzr } from './helper';

const handleRealPaths = (list) => {
  const actual = _.cloneDeep(list);
  _.forEach(actual, (item) => {
    // let controlPercent = ''
    // let controlPercentDesc = ''
    let realNames: any = null;
    if (item.Names) {
      // controlPercent = item.Names.ControlPercent
      // controlPercentDesc = Number(item.Names.ControlPercent) > 0 ? (item.Names.ControlPercent + '%') : ''
      realNames = [];
      if (item?.Names?.PersonList?.length > 0) {
        _.forEach(item.Names.PersonList, (item) => {
          realNames.push({
            Name: item.Name,
            KeyNo: item.KeyNo,
            Org: item.Org,
          });
        });
      }
    }

    const CollapsedPaths = _.cloneDeep(item.CollapsedPaths);
    const RealPaths: any = [];
    _.forEach(CollapsedPaths, (path) => {
      const { Paths, Children } = path;
      const arr = [Paths, ...Children];
      _.forEach(arr, (subarr) => {
        _.forEach(subarr, (sub, index: number) => {
          // console.log(JSON.stringify(sub, null, 4))
          // handleLinkForKzr()
          const { lineType, lineTextPrefix, lineTextSuffix, percent, tips } = handleLinkForKzr(sub) || {};
          sub.lineType = lineType;
          sub.lineTextPrefix = lineTextPrefix;
          sub.lineTextSuffix = lineTextSuffix;
          sub.percent = percent;
          sub.tips = tips;
          if (index === subarr.length - 1) {
            if (realNames && realNames.length && !sub.Name) {
              sub.mutiActual = true;
              sub.Name = realNames;
              // sub.ControlPercent = controlPercent
              // sub.ControlPercentDesc = controlPercentDesc
              // sub.percent = controlPercent
            }

            if (index >= 1 && subarr?.[index - 1].IsPublic) {
              sub.lineType = 'dashed';
            }
          }
        });
      });
      RealPaths.push(arr);
    });
    item.RealPaths = RealPaths;
  });
  return actual;
};

const resetActualList = (PersonList, ControlPercent) => {
  const actual: any = [];
  if (PersonList?.length > 0) {
    const ControlPercentDesc = Number(ControlPercent) > 0 ? ControlPercent + '%' : '';
    const realName: any = [];
    _.forEach(PersonList, (item) => {
      realName.push({
        Name: item.Name,
        KeyNo: item.KeyNo,
        Org: item.Org,
      });
    });
    _.forEach(PersonList, (item, index: number) => {
      if (index === 0) {
        item.mutiActual = true;
        item.realNames = realName;
        item.ControlPercent = ControlPercentDesc;
        const obj = {
          mutiActual: true,
          Name: realName,
          lineType: 'dashed',
          lineTextSuffix: '(表决权)',
          percent: item.ControlPercent,
          ControlPercentDesc,
        };
        item.RealPaths = [[[obj]]];
      }
      actual.push({
        ...item,
        Names: {
          ControlPercent: ControlPercentDesc,
          PersonList: [item],
        },
      });
    });
  }
  return actual;
};

export const handlePath = (data) => {
  let actual: any = [];
  let yisiActual: any = [];
  const { ActualControl, Names, FinalActualControl, MergedActualControl, NameCount } = data || {};
  // console.log('FinalActualControl', ActualControl, Names, FinalActualControl)
  if (ActualControl || FinalActualControl) {
    let PersonList;
    let ControlPercent;
    if (FinalActualControl) {
      PersonList = FinalActualControl.PersonList;
      ControlPercent = FinalActualControl.ControlPercent;
    } else if (ActualControl) {
      PersonList = ActualControl.PersonList;
      ControlPercent = ActualControl.ControlPercent;
    }

    actual = resetActualList(PersonList, ControlPercent);
  } else if (Names?.length) {
    _.forEach(Names, (item) => {
      if (Number(item.IsActual)) {
        actual.push(item);
      } else {
        yisiActual.push(item);
      }
    });
    actual = handleRealPaths(actual);
    // 如果NameCount大于1，就处理数据
    if (NameCount > 1 && yisiActual.length <= 0) {
      const pathList: any = [];
      _.forEach(actual, (item) => {
        const obj = {
          ...item,
          ...item?.Names?.PersonList?.[0],
          ControlPercent: item?.Names?.ControlPercent,
        };

        const RealPaths: any = [];
        _.forEach(item.Paths, (path) => {
          _.forEach(path, (sub, sindex: number) => {
            const { lineType, lineTextPrefix, lineTextSuffix, percent } = handleLinkForKzr(sub) || {};
            sub.lineType = lineType;
            sub.lineTextPrefix = lineTextPrefix;
            sub.lineTextSuffix = lineTextSuffix;
            sub.percent = percent;
            if (sindex === path.length - 1) {
              if (sindex >= 1 && path?.[sindex - 1].IsPublic) {
                sub.lineType = 'dashed';
              }
              // 处理国籍标签
              if (sub.KeyNo === obj.KeyNo) {
                sub.Tags = item.Names.PersonList[0].Tags || [];
              }
            }
          });

          RealPaths.push([path]);
        });
        obj.Paths = RealPaths;
        pathList.push(obj);
      });
      actual = [
        {
          Names: MergedActualControl,
          isShowMoreInfo: true,
          pathList,
        },
      ];
    }

    yisiActual = handleRealPaths(yisiActual);

    // 疑似实际控制人数据处理
    if (yisiActual?.length) {
      yisiActual = _.map(yisiActual, (item) => {
        return {
          ControlPercent: item.Names.ControlPercent,
          ...item,
          ...item.Names?.PersonList?.[0],
        };
      });
    }
  }

  return {
    actual,
    yisiActual,
  };
};
