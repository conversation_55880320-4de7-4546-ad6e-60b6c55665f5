<template>
  <div :class="{ 'dashed-line': lineType === 'dashed', 'app-tdpath-line-left': direction === 'left','app-tdpath-line':true}">
    <span v-if="$slots.underStr" class="percent-value"><slot name="underStr" /></span>
    <span v-else class="percent-value">{{ underStr }}</span>
    <div class="allow" />
    <span v-if="$slots.lineTextPrefix" class="data-type"><slot name="lineTextPrefix" /></span>
    <span v-else class="data-type">{{ lineTextPrefix }}</span>
  </div>
</template>

<script src="./component.js"></script>

<style lang="less" src="./style.less"></style>
