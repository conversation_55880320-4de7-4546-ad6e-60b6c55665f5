.app-tdpath-line {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  min-width: 47px;
  height: 37px;
  vertical-align: middle;
  padding: 0 12px 0 10px;
  margin: 0 5px;

  &.app-tdpath-line-left {
    padding: 0 10px 0 12px;

    &::after {
      left: 5px;
    }

    .allow {
      border-left: 0;
      border-right: 14px solid #999999;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      left: -8px;
    }
  }
  .percent-value, .data-type {
    line-height: 18px;
    white-space: nowrap;
    font-size: 12px;
    color: #128BED;
    text-align: center;
  }
  .percent-value {
    margin-bottom: 1px;
  }
  .allow {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%) scale(0.5);
    transform-origin: right;
    width: 0;
    height: 0;
    border-left: 14px solid #999999;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    z-index: 1;
  }
  &.dashed-line::after {
    border-top-style: dashed;
  }
  &::after {
    position: absolute;
    content: '';
    width: calc(100% - 6px);
    height: 0;
    border-top: 1px solid #999999;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}