<template>
  <div v-if="pathList?.length" class="td-path">
    <template v-if="isInvest">
      <div v-for="(path, pindex) in pathList" :key="`path-line_${pindex}`" class="path-line">
        <div class="name">{{ path.name }}</div>
        <div class="path-item-wrapper">
          <q-entity-link v-if="name" :coy-obj="{ Name: name, KeyNo: keyNo }" :ellipsis="false"></q-entity-link>
          <span class="path-item" v-for="(item, index) in path.arr" :key="`path-item_${index}`">
            <span class="percent" :class="{ t5: item.DataType === 5 }">{{ item.underStr }}</span>
            <q-entity-link :coy-obj="item" :ellipsis="false"></q-entity-link>
          </span>
        </div>
      </div>
    </template>
    <template v-else-if="isControl">
      <div v-for="(fpath, findex) in pathList" :key="`path-line_${findex}`" class="control-path">
        <div class="title">{{ fpath.name }}</div>
        <div class="control-content">
          <div v-for="(spath, sindex) in fpath.arr" :key="sindex" class="line-bottom">
            <div v-if="sindex === 0 || (sindex > 0 && !fpath.showRoute)" class="line-content">
              <span class="path-item" v-for="(item, index) in spath" :key="`path-item_${index}`">
                <q-entity-link v-if="item.mutiActual" :coy-arr="item.Name" :ellipsis="false"></q-entity-link>
                <q-entity-link
                  v-else
                  :coy-obj="{
                    Name: item.Name,
                    KeyNo: [-1, -2, -3].includes(item.Org) ? '' : item.KeyNo,
                    Org: item.Org,
                  }"
                  :ellipsis="false"
                ></q-entity-link>
                <div class="path-item-info" :class="{ 'dashed-line': item.lineType === 'dashed' }">
                  <span v-if="item.underStr" class="percent">
                    {{ item.underStr }}
                    <q-glossary-info
                      v-if="item.tips"
                      :text="item.tips"
                      autoWidth
                      :maxWidth="400"
                      placement="bottom-start"
                    ></q-glossary-info>
                  </span>
                  <div class="allow"></div>
                  <span v-if="item.lineTextPrefix" class="data-type">
                    {{ item.lineTextPrefix }}
                  </span>
                </div>
              </span>
              <q-entity-link :coy-obj="{ Name: name, KeyNo: keyNo }" :ellipsis="false"></q-entity-link>
            </div>
            <div v-if="sindex === 0 && fpath.arr.length > 1" class="toggle-route" @click="toggleLine(fpath, findex)">
              <span class="line"></span>
              <span class="name">
                {{ fpath.showRoute ? '展开路径' : '收起路径' }}
                <span class="iconfont icon-wenzilianjiantou" :class="{ 'hide-route': !fpath.showRoute }"></span>
              </span>
              <span class="line"></span>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div v-for="(path, pindex) in pathList" :key="`path-line_${pindex}`" class="path-line">
        <div class="name">{{ path.name }}</div>
        <div class="path-item-wrapper">
          <span class="path-item" v-for="(item, index) in path.arr" :key="`path-item_${index}`">
            <q-entity-link :coy-obj="item" :ellipsis="false"></q-entity-link>
            <span class="percent">{{ item.underStr || '&#12288;' }}</span>
          </span>
          <q-entity-link v-if="name" :coy-obj="{ Name: name, KeyNo: keyNo }" :ellipsis="false"></q-entity-link>
        </div>

        <div v-if="path.name === '决定路径'" class="tips">注：未识别出股比>25%的自然人，因此决定以最终法定代表人作为受益所有人结果</div>
      </div>
    </template>
    <div v-if="pathList.length >= 10 && !isControl" class="text-gray">*仅显示TOP10路径</div>
  </div>
  <div v-else-if="pathSet && pathSet.length" class="td-path td-path-list">
    <template v-if="isControl">
      <div v-for="(path, pindex) in pathSet" :key="pindex + 'path'" class="td-path-item">
        <div class="title">{{ path.name }}</div>
        <div v-for="(fpath, findex) in path.pathList" :key="`path-line_${pindex}_${findex}`" class="control-path">
          <div v-if="findex <= 2 || (findex > 2 && !path.showRoute)" class="control-content">
            <div v-for="(spath, sindex) in fpath.arr" :key="sindex" class="line-bottom">
              <div v-if="sindex === 0 || (sindex > 0 && !fpath.showRoute)" class="line-content">
                <span v-for="(item, index) in spath" :key="`path-item_${index}`" class="path-item">
                  <template v-if="item.mutiActual">
                    <template v-for="(vo, i) in item.Name">
                      <q-entity-link :key="'company-name' + i" class="company-name" :coy-arr="[vo]" :ellipsis="false" />
                      <span
                        v-if="vo.Tags && handleDomicileTag(vo.Tags) && showTag"
                        :key="'company-name-tags' + i"
                        class="domicile-name g-ml4"
                      >
                        {{ handleDomicileTag(vo.Tags) }}
                      </span>
                      <span v-if="i !== item.Name.length - 1" :key="'company-name-split' + i"> 、 </span>
                    </template>
                  </template>
                  <q-entity-link
                    v-else
                    class="company-name"
                    :coy-obj="{
                      Name: item.Name,
                      KeyNo: [-1, -2, -3].includes(item.Org) ? '' : item.KeyNo,
                      Org: item.Org,
                    }"
                    :ellipsis="false"
                  />
                  <template v-if="showTag">
                    <span v-if="item.Tags && handleDomicileTag(item.Tags)" class="domicile-name g-ml4">
                      {{ handleDomicileTag(item.Tags) }}
                    </span>
                    <span v-else-if="item.domicileTagName" class="domicile-name g-ml4">
                      {{ item.domicileTagName }}
                    </span>
                  </template>
                  <app-tdpath-line :line-type="item.lineType" :under-str="item.underStr" :line-text-prefix="item.lineTextPrefix" />
                </span>
                <q-entity-link class="company-name" :coy-obj="{ Name: name, KeyNo: keyNo }" :ellipsis="false" />
                <span v-if="domicileTagName && showTag" class="domicile-name">{{ domicileTagName }}</span>
              </div>
              <app-tdpath-route
                v-if="sindex === 0 && fpath.arr.length > 1"
                :show-route="fpath.showRoute"
                @toggleLine="() => toggleLineList(findex, path, pindex)"
              />
            </div>
          </div>
          <app-tdpath-route
            v-if="findex === 2 && path.pathList.length > 3"
            :show-route="path.showRoute"
            @toggleLine="() => toggleLineMore(path, pindex)"
          />
        </div>
      </div>
    </template>
  </div>
  <!--  <span v-else>-</span>-->
  <Empty v-else></Empty>
</template>
<script src="./component.js"></script>
<style lang="less" scoped>
.td-path {
  ::v-deep {
    a {
      color: #333333;
    }

    a:hover {
      color: #128bed;
    }
  }

  .control-path {
    .title {
      line-height: 20px;
      font-weight: bold;
    }

    .control-content {
      margin-top: 5px;
      padding: 0 10px 10px;
      border-radius: 2px;
      background: #fafafa;

      a {
        color: #333;

        &:hover {
          color: #128bed;
        }
      }

      .line-content {
        line-height: 37px;
        padding: 10px 0;

        .path-item {
          .path-item-info {
            position: relative;
            display: inline-flex;
            flex-direction: column;
            min-width: 79px;
            height: 37px;
            vertical-align: middle;
            padding: 0 15px 0 10px;
            margin: 0 5px;

            .percent,
            .data-type {
              line-height: 18px;
              white-space: nowrap;
              font-size: 12px;
              color: #128bed;
              text-align: center;
            }

            .percent {
              margin-top: -2px;

              ::v-deep {
                .iconfont {
                  color: #ff722d;
                }
              }
            }

            .allow {
              position: absolute;
              top: 46%;
              right: 0;
              transform: translateY(-50%);
              width: 0;
              height: 0;
              border-left: 10px solid #bbbbbb;
              border-top: 3.5px solid transparent;
              border-bottom: 3.5px solid transparent;
            }

            &.dashed-line::after {
              border-top-style: dashed;
            }

            &::after {
              position: absolute;
              content: '';
              width: calc(100% - 10px);
              height: 0;
              border-top: 1px solid #999999;
              left: 0;
              top: 46%;
              transform: translateY(-50%);
            }
          }
        }
      }

      .toggle-route {
        display: flex;
        align-items: center;
        font-size: 12px;
        line-height: 18px;
        color: #999999;
        cursor: pointer;

        .name {
          flex: 0 0 82px;
          text-align: center;

          &:hover {
            color: #128bed;

            .icon-wenzilianjiantou {
              color: #128bed;
            }
          }

          .icon-wenzilianjiantou {
            display: inline-block;
            transform: rotate(90deg);
            font-size: 10px;
            margin-left: 4px;
            color: #d8d8d8;

            &.hide-route {
              transform: rotate(-90deg);
            }
          }
        }

        .line {
          flex: 1;
          height: 0;
          margin-top: 1px;
          border-top: 1px dashed #d8d8d8;
        }
      }
    }

    .line-bottom:not(:first-child):not(:last-child) .line-content {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 1px;
        background-color: #eeeeee;
      }
    }

    .line-bottom:last-child .line-content {
      padding-bottom: 0;
    }

    &:not(:last-child) {
      margin-bottom: 15px;
    }
  }

  .path-item-wrapper {
    padding: 10px;
    background: #fafafa;
    border-radius: 2px;
  }

  .company-name {
    line-height: 24px;
    vertical-align: middle;
  }

  .domicile-name {
    display: inline-block;
    line-height: 16px;
    border: 1px solid #d8d8d8;
    border-radius: 2px;
    padding: 0px 2px;
    color: #666666;
    font-size: 11px;
    white-space: nowrap;
  }

  .g-ml4 {
    margin-left: 4px;
  }

  &.td-path-list {
    .td-path-item {
      &:not(:last-child) {
        margin-bottom: 15px;
      }

      .title {
        line-height: 20px;
        font-weight: bold;
      }

      .control-path:not(:last-child) {
        margin-bottom: 5px;
      }
    }
  }
}

.td-path {
  .path-line {
    line-height: 28px;

    &:not(:last-child) {
      margin-bottom: 6px;
    }

    .name {
      color: #222;
      font-weight: bold;
      line-height: 28px;
      padding-bottom: 5px;
    }

    a {
      color: #333;

      &:hover {
        color: #128bed;
      }
    }

    .path-item {
      .percent {
        display: inline-block;
        width: 84px;
        height: 26px;
        background: url('../../assets/images/<EMAIL>') no-repeat right bottom;
        background-size: 75px 8px;
        padding-bottom: 10px;
        font-size: 12px;
        color: #128bed;
        text-align: center;
        position: relative;
        top: -8px;
        margin-right: 6px;
      }

      .percent.t5 {
        width: 105px;
        background: url('../../assets/images/line_arrowt5.png') no-repeat right bottom;
        background-size: 100px 34px;
        font-size: 12px;
        top: -9px;
        margin-right: 6px;
        padding-bottom: 40px;
      }
    }
  }
}
</style>
