import _ from 'lodash';
import appTdpathLine from './components/app-tdpath-line';
import appTdpathRoute from './components/app-tdpath-route';
import Empty from '@/shared/components/empty';

export default {
  name: 'app-tdpath',
  components: {
    [appTdpathLine.name]: appTdpathLine,
    [appTdpathRoute.name]: appTdpathRoute,
    Empty: Empty,
  },
  props: {
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    dataType: {
      type: Number,
      default: 0,
    },
    paths: {
      type: Array,
      default: null,
    },
    isInvest: {
      type: Boolean,
      default: false,
    },
    reverse: {
      type: Boolean,
      default: true,
    },
    isControl: {
      type: Boolean,
      default: false,
    },
    domicileTags: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 多实控人展示
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      showTag: false,
      pathList: [],
      pathSet: [],
    };
  },
  computed: {
    domicileTagName() {
      return this.handleDomicileTag(this.domicileTags);
    },
  },
  watch: {
    // paths() {
    //   this.setPath()
    // },
    paths(val) {
      this.pathList = this.setPath(val);
    },
    list() {
      this.setPathList();
    },
  },
  created() {
    if (this.list?.length) {
      this.setPathList();
    } else {
      this.pathList = this.setPath(this.paths);
    }
  },
  mounted() {},
  methods: {
    setPathList() {
      if (this.list?.length) {
        const pathSet = _.map(this.list, (item) => {
          const pathList = this.setPath(item.Paths || item.paths);
          // pathList = [...pathList, ...pathList, ...pathList, ...pathList, ...pathList]
          return {
            pathList,
            showRoute: pathList.length > 3,
            name: item.Name || item.name,
            keyNo: item.KeyNo || item.keyNo,
          };
        });
        this.pathSet = pathSet;
      }
    },
    setPath(paths) {
      if (paths?.length) {
        if (this.isControl) {
          return this.handleControlPath(paths);
        } else {
          return this.handleOtherPath(paths);
        }
      }
    },
    handleControlPath(data) {
      const pathList = [];
      const paths = _.cloneDeep(data);
      _.forEach(paths, (fPath, findex) => {
        let percent = '';
        _.forEach(fPath, (sPath, sindex) => {
          _.forEach(sPath, (tPath, index) => {
            if (sindex === 0 && index === 0) {
              percent = tPath.percent || tPath.Percent;
            }
            const underStr =
              (tPath.percent || tPath.Percent ? tPath.percent || tPath.Percent : '') + (tPath.lineTextSuffix ? tPath.lineTextSuffix : '');
            tPath.underStr = percent ? underStr : '';
            tPath.domicileTagName = this.handleDomicileTag(tPath.Tags || tPath.tags);
          });
          if (this.reverse) {
            sPath.reverse();
          }
        });
        const proportion = percent ? `（占比 ${percent}）` : '';
        const name = `控制路径 ${findex + 1}${proportion}`;
        pathList.push({
          name,
          showRoute: fPath?.length > 1,
          arr: fPath,
        });
      });
      return pathList;
    },
    handleOtherPath(data) {
      return data.map((pathArr, pindex) => {
        const arr = pathArr.map((item) => {
          if (this.dataType === 5 || (this.dataType === 1 && pindex === 0)) {
            item.underStr = '法定代表人';
          } else if (item.Percent ?? item.percent) {
            item.underStr = item.Percent ?? item.percent;
          } else if (this.dataType === 2) {
            item.underStr = '总公司';
          }
          item.domicileTagName = this.handleDomicileTag(item.Tags || item.tags);
          return item;
        });
        if (this.reverse) {
          arr.reverse();
        }
        const percentTotal = arr?.[0]?.PercentTotal ?? arr?.[0]?.percentTotal;
        let proportion = percentTotal ? `（占比 ${percentTotal}）` : '';
        let name = `股权路径 ${pindex + 1}${proportion}`;
        if (this.isInvest) {
          const lastPercentTotal = arr?.[arr?.length - 1]?.PercentTotal ?? arr?.[arr?.length - 1]?.percentTotal;
          proportion = lastPercentTotal ? `（占比 ${lastPercentTotal}）` : '';
          name = `路径 ${pindex + 1}${proportion}`;
        }
        if (this.dataType === 5 || (this.dataType === 1 && pindex === 0)) {
          name = '决定路径';
        }
        return {
          name,
          arr,
        };
      });
    },
    handleDomicileTag(tags = []) {
      let tagName = '';
      if (tags?.length) {
        for (const item of tags) {
          // 112 香港  115 台湾 402 海外注册地
          if (_.includes([402, 112, 115], item.Type) && item.Name) {
            tagName = item.Name;
            break;
          }
        }
      }

      return tagName;
    },
    toggleLine(item, index) {
      item.showRoute = !item.showRoute;
      this.$set(this.pathList, index, item);
    },
    toggleLineMore(item, index) {
      item.showRoute = !item.showRoute;
      this.$set(this.pathSet, index, item);
    },
  },
};
