import _ from 'lodash';
// let IKzrDataType = {
//   股东: 0,
//   法人股权路径: 1,
//   分公司: 2,
//   公司有股东无股比: 3,
//   公司无股东: 4,
//   GP路径: 5
// }

// let IKzrItemData = {
//   DataType: null, // 节点类型
//   ControllerType: null, // 控制类型
//   Percent: null, // 路径比例
//   ShareHoldType: null, // 股东类型
//   ControlPercent: null, // 数据接口处理的比例 优先取值
//   ControlRatio: null, // 控制比例
//   OriginKeyNo: null, // 披露企业keyno
//   OriginName: null // 披露企业名称
// }

// let IKzrControlType = {
//   type: null, // 类型
//   typeDesc: null, // 类型描述
//   lineType: null, // 路径虚实线
//   tips: null, // 注释文案
//   lineTextPrefix: null, // 路径文案前缀
//   lineTextSuffix: null, // 路径文案后缀
//   percent: null, // 比例
//   isSpecial: null, // 是否是特殊路径 shareHoldType：1234 controllerType: 23457为特殊路径
//   lineText: null // 拼接文案 lineTextlineTextPrefix + percent + '%'
// }

const shareHoldTypeMap = [
  {
    type: 1,
    typeDesc: '参控股-直接持股',
    lineType: 'solid',
    tips: '数据源于xxx的公告披露',
    lineTextPrefix: '参控股',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 2,
    typeDesc: '参控股-间接持股',
    lineType: 'dashed',
    tips: '数据源于xxx的公告披露',
    lineTextPrefix: '参控股',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 3,
    typeDesc: '参控股-未持股',
    lineType: 'solid',
    isSpecial: false,
  },
  {
    type: 0,
    typeDesc: '参控股-未披露',
    lineType: 'solid',
    isSpecial: false,
  },
];

const controlTypeMap = [
  {
    // 无controlpercent情况
    type: 1,
    typeDesc: '普通持股',
    lineType: 'solid',
    isSpecial: false,
  },
  {
    type: 2,
    typeDesc: '协议控制',
    lineType: 'dashed',
    tips: '数据源于xxx的公告披露，中间可能会存在收起路径。',
    lineTextPrefix: '协议控制',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 3,
    typeDesc: '信托控制',
    lineType: 'dashed',
    // tips: '委托人委托xxx代持公司股份',
    tips: '数据源于xxx的公告披露',
    lineTextPrefix: '信托控制',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 4,
    typeDesc: '普通合伙人',
    lineType: 'solid',
    lineTextPrefix: '普通合伙人',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 5,
    typeDesc: '疑似实际控制人',
    lineType: 'dashed',
    tips: '',
    isSpecial: true,
  },
  {
    type: 6,
    typeDesc: '执行事务合伙人',
    lineType: 'solid',
    lineTextPrefix: '执行事务合伙人',
    isSpecial: false,
  },
  {
    type: 7,
    typeDesc: '同股不同权',
    lineType: 'dashed',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 0,
    typeDesc: '其他',
    lineType: 'solid',
    isSpecial: true,
  },
];

export const handleLinkForKzr = (itemData) => {
  const getPercent = ({ itemData, isSpecial }) => {
    if (!isSpecial) {
      return +(itemData.Percent || '').split('%')[0] ? itemData.Percent : '';
    }
    if (itemData.ControlPercent) {
      return itemData.ControlPercent + '%';
    }
    if (itemData.ControlRatio) {
      return itemData.ControlRatio + '%';
    }
    if (+itemData.DataType === 5) {
      return '100%';
    }
    return '';
  };

  if (+itemData.DataType === 5) {
    itemData.ControllerType = 6;
  }
  let returnData = {
    type: -1,
    typeDesc: '默认',
    lineType: 'solid',
    lineText: itemData.Percent,
    percent: itemData.Percent,
  };
  let target = null;
  // 1为股权控制 根据ShareHoldType区分控股类型
  const cloneShareHoldTypeMap = _.cloneDeep(shareHoldTypeMap);
  const cloneControlTypeMap = _.cloneDeep(controlTypeMap);
  if ([1, 8].indexOf(itemData.ControllerType) > -1 && itemData.ShareHoldType >= 0) {
    target = _.find(cloneShareHoldTypeMap, (stype) => stype.type === itemData.ShareHoldType);
  } else if (itemData.ControllerType >= 0) {
    // 根据controller判断
    target = _.find(cloneControlTypeMap, (ctype) => ctype.type === itemData.ControllerType);
  }
  if (target) {
    // 特殊路径使用控制权比例 其他使用股权比例Percent
    target.percent = getPercent({ itemData, isSpecial: target.isSpecial });
    // 疑似实际控制人不限时比例
    if (itemData.ControllerType === 5) {
      target.lineText = '';
      target.percent = '';
    } else {
      target.lineText = (target.lineTextPrefix || '') + (target.percent || '');
      if (target.lineText.indexOf('%') < 0 && target.percent) {
        target.lineText += '%';
      }
      target.lineText += target.lineTextSuffix || '';
      if (target.tips && target.tips.indexOf('xxx') > -1) {
        target.tips = itemData.OriginKeyNo ? _.replace(target.tips, 'xxx', itemData.OriginName) : '';
      } else if (!target.tips) {
        target.tips = '';
      }
    }
    returnData = target;
  }

  return returnData;
};

