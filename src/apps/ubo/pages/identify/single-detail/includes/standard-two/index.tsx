import { defineComponent, reactive, getCurrentInstance } from 'vue';
import StandardListBase from '../standard-list-base';
import { IQRichTableColumn } from '@/components/global/q-rich-table';
import QIcon from '@/components/global/q-icon';
import { openVotingPath } from '../voting-path';
import { company as companyService } from '@/shared/services';
interface IRecord {
  votingPercent: string;
  uboDate: string;
  keyNo: string;
  name: string;
  totalStockPercent: string;
  mergeInd: number;
}

export default defineComponent({
  name: 'StandardListTwo',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    keyNo: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    parentKeyNo: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const vm = getCurrentInstance()?.proxy;
    const hasShare = props.list.find((e: any) => e.mergeInd === 1);
    // 模拟数据
    const tableData = reactive({
      list: props.list,
    });
    const getCellRowSpan = (record: IRecord, rowIndex: number, dataIndex: string) => {
      if (record.mergeInd === 1) {
        if (rowIndex === 0) {
          const mergedCount = tableData.list.length; // 合并的行数
          return {
            rowSpan: mergedCount,
            colSpan: 1,
          };
        } else {
          return {
            rowSpan: 0,
            colSpan: 1,
          };
        }
      }
    };
    const showDetailDialog = async (record: IRecord) => {
      console.log(record);
      const res = await companyService.getGroupInvPath(props.keyNo, {
        name: record.name,
        toKeyNo: props.keyNo,
      });

      vm?.$modal.showDimension('benefitDetail', {});
    };
    // 列定义
    const columns: IQRichTableColumn[] = [
      {
        title: '受益所有人',
        dataIndex: 'name',
        width: 272,
        customRender: (text, row) => {
          const remark = row.nameRemark ? <span class="text-#BBB ml-1">({row.nameRemark})</span> : '';
          return (
            <div>
              <q-entity-link coyObj={{ KeyNo: row.keyNo, Name: row.name }}></q-entity-link>
              {remark}
            </div>
          );
        },
      },
      {
        title: '直接持股比例',
        dataIndex: 'percent',
        width: 180,
        // 处理空值显示
        customRender: (text) => text || '-',
      },
      {
        title: '总持股比例',
        dataIndex: 'totalStockPercent',
        width: 180,
        customRender: (text, record) => {
          return (
            <div>
              <span style={{ cursor: 'pointer' }}>{text || '-'}</span>
              {text ? (
                <span class="text-#FF722D ml-1">
                  <QIcon type="icon-guquanlian" onClick={() => showDetailDialog(record)} />
                </span>
              ) : (
                ''
              )}
            </div>
          );
        },
      },
      {
        title: `表决权比例${hasShare ? '(共同持有)' : ''}`,
        dataIndex: 'votingPercent',
        width: 180,
        // 使用自定义渲染函数处理点击事件和复杂逻辑
        customRender: (text, record, index) => {
          const spanOptions = getCellRowSpan(record, index, 'votingPercent');

          return {
            attrs: spanOptions,
            children: (
              <span>
                {text || '-'}
                <span class="text-#FF722D ml-1">
                  <QIcon
                    type="icon-guquanlian"
                    onClick={() => {
                      openVotingPath({
                        keyNo: props.keyNo,
                      });
                    }}
                  />
                </span>
              </span>
            ),
          };
        },
      },
      {
        title: '受益所有权形成日期',
        dataIndex: 'uboDate',
        width: 180,
        // 处理空值显示
        customRender: (text) => text || '-',
      },
    ];

    return {
      tableData,
      columns,
    };
  },
  render() {
    return <StandardListBase title="标准二：25%以上表决权、收益权" columns={this.columns} list={this.tableData.list} isNet={false} />;
  },
});
