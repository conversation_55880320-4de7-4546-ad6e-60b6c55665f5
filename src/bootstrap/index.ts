import moment from 'moment';
import VueI18n from 'vue-i18n';
import 'moment/locale/zh-cn';

import env from '@/shared/config/env';
import Root from '@/shared/root';
import { messages } from '@/shared/locales';
import { getCurrentLocale } from '@/utils/locale';
import UserAbility from '@/libs/plugins/user-ability';
import UserTracking from '@/libs/plugins/user-tracking/vue';

import * as Inject from './inject';
import * as Antd from './antd';
import * as GlobalComponents from '../components/global';
import * as Sentry from './sentry';
import store from '../store';
import customDirectives from '../directives';
import customFilters from '../filters';
import { ABILITY_RULES } from './ability';

export const bootstrap = (Vue, router) => {
  moment.locale('zh-CN');
  Vue.use(VueI18n);
  const i18n = new VueI18n({
    locale: getCurrentLocale(),
    fallbackLocale: 'zh-CN',
    messages,
  });
  customDirectives(Vue);
  customFilters(Vue);
  Vue.use(Inject, { store, router });
  Vue.use(Sentry, { router });
  Vue.use(Antd);

  // 权限插件
  Vue.use(UserAbility, {
    user: {
      get permissions() {
        return store.getters['user/permissions'];
      },
      get usage() {
        return store.getters['user/usage'];
      },
    },
    rules: ABILITY_RULES,
  });

  // 用户追踪
  Vue.use(UserTracking, {
    appName: 'qcc-benefishield-web',
    applicationName: '受益所有人',
    sdk: env.TRACK_PLATFORM,
    // beforeSend() {
    //   const uid = (store.getters['user/profile'] || {}).guid;
    //   if (uid) {
    //     this.setUserId(uid);
    //   }
    // },
  });

  Vue.use(GlobalComponents);
  const app = new Vue({
    router,
    store,
    i18n,
    render: (h) => h(Root, { ref: 'entry' }),
  }).$mount('#app');

  window.__VUE_ROOT__ = app;
};
