import { PaginationResponse } from '@/core/entities';
import { HttpClient } from '@/utils/http-client';

const REPORT_HISTORY = `/boi/report/history`;
const CREATE_REPORT = `/boi/report/create`;

export interface SearchReportParam {
  pageSize: number;
  pageIndex: number;
  companyId: string;
  companyName: string;
  operatorId: string;
  reportType: string;
  status: number;
  createDate: CreateDate[];
  sortField: string;
  isSortAsc: boolean;
}

interface CreateDate {
  number: number;
  unit: string;
  currently: boolean;
  flag: number;
  min: unknown;
  max: unknown;
  year: number;
  city: string;
}

export const createService = (httpClient: HttpClient) => ({
  getPersonId(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/person/get-person-id', params);
  },
  getRelatCompany(params): Promise<Readonly<any>> {
    return httpClient.get('/v1/api/people/getRelatCompany', {
      params,
    });
  },
  batchSearch(data): Promise<any> {
    return httpClient.post(`/batch/boi/search`, data);
  },
  batchDetail(params): Promise<any> {
    return httpClient.get(`/batch/boi/detail`, { params });
  },
  batchHistory(data): Promise<any> {
    return httpClient.post(`/batch/boi/history`, data);
  },
  historyStatistics(data): Promise<any> {
    return httpClient.post('/batch/boi/statistics', data);
  },
  dataBatch(data): Promise<any> {
    return httpClient.post(`/batch/boi/import/data`, data);
  },
  getSingleDetail(params: { companyKey: string; companyId?: string }): Promise<any> {
    return httpClient.post(`/boi`, params);
  },
  getVerificationRecord(params): Promise<Readonly<any>> {
    return httpClient.post('/boi/history', params);
  },

  /**
   * 报告历史记录
   */
  searchReportHistory(data: Partial<SearchReportParam>): Promise<Readonly<PaginationResponse<any>>> {
    return httpClient.post(REPORT_HISTORY, data);
  },
  /**
   * 生成pdf
   */
  createPdf(params): Promise<Readonly<any>> {
    return httpClient.post(CREATE_REPORT, params);
  },
  // 获取股权架构图
  getShareholdingChart(data) {
    return httpClient.post('/boi/force/chart', data);
  },
});
