import QPlainTable from '@/components/global/q-plain-table';
import { computed, defineComponent } from 'vue';
import styles from './change-table.module.less';
import { dateFormat } from '@/utils/format';
import { convertToRelationalPathData } from '@/apps/ubo/pages/identify/single-detail/includes/standard-one/util';
import RelationalPath from '@/components/relational-path';

const extraColumns = [
  {
    title: '受益权形成日期',
    customRender: (record) => {
      return dateFormat(record.uboDate);
    },
  },
];

const standard3 = [
  {
    title: '任职类型',
    customRender: (record) => {
      return record.position || '-';
    },
  },
];

const standard2 = [
  {
    title: '表决权比例',
    customRender: (record) => {
      return record.votingPercent || '-';
    },
  },
  {
    title: '表决权路径',
    customRender: (record, changed = false) => {
      // return record.votingPathList?.map((item) => {
      //   return <div class={{ [styles.path]: true, [styles.changed]: changed }}>{item.path}</div>;
      // });
      return '-';
    },
  },
];

const standard1 = [
  {
    title: '持股比例',
    customRender: (record) => {
      return record.calculation || '-';
    },
  },
  {
    title: '股权穿透路径',
    customRender: (record, changed = false) => {
      return record.detailInfoList?.map((item) => {
        return (
          <div class={{ [styles.path]: true, [styles.changed]: changed }}>
            <RelationalPath type="investment" elements={convertToRelationalPathData(item)} />
          </div>
        );
      });
    },
  },
];

const ChangeTable = defineComponent({
  name: 'ChangeTable',
  props: {
    changeType: {
      type: Number,
      default: 1,
    },
    standard: {
      type: Number,
      default: 1,
    },
    before: {
      type: Object,
      default: () => ({}),
    },
    after: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const columns = computed(() => {
      const standardMap = {
        1: standard1,
        2: standard2,
        3: standard3,
      };
      const columns = standardMap[props.standard];
      return props.changeType === 1 ? [...columns, ...extraColumns] : columns;
    });

    return {
      columns,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <QPlainTable>
          <thead>
            <tr>
              <th></th>
              <th>变更前</th>
              <th>变更后</th>
            </tr>
          </thead>
          <tbody>
            {this.columns.map((col) => {
              return (
                <tr>
                  <td>{col.title}</td>
                  <td>{col.customRender(this.before)}</td>
                  <td>{col.customRender(this.after, true)}</td>
                </tr>
              );
            })}
          </tbody>
        </QPlainTable>
      </div>
    );
  },
});

export default ChangeTable;
