@gutter: 15px;

.company-container {
  font-family: "Microsoft YaHei", Arial, sans-serif;
  width: 340px;
  // padding: @gutter;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 4px 0;
  background-color: #fff;
  border-radius: 6px;
  // border: 1px solid #128BED;

  .company-container-header {
    padding: 15px;
    padding-bottom: 0;
  }

  :global {
    .e_path {
      .e_path_title {
        padding: 0 15px;
      }

      .e_path-Mx {
        padding: 15px;
        padding-top: 0;
      }
    }
  }

  .loading {
    width: 340px;
    height: 120px;
    background: url("./images/loading.png") center center;
    background-size: cover;
    margin: 0 0 0 -@gutter;
  }

  .logo {
    width: 50px;
    height: 50px;
    float: left;
    box-sizing: border-box;
  }

  .gap {
    margin: @gutter / 2 + 4 -@gutter @gutter / 2 -@gutter;
    height: 5px;
    background: rgba(246, 246, 246, 1);
  }

  .content-container {
    float: left;
    // min-height: 100px;
    padding-left: @gutter;
    width: 260px;
    box-sizing: border-box;

    &__header {
      line-height: 24px;
      word-wrap: break-word;
      word-break: break-word;

      .ppl {
        word-wrap: break-word;
        word-break: break-word;

        :global {
          a {
            word-wrap: break-word;
            word-break: break-word;
          }
        }
      }
    }

    a {
      color: #128bed;
      text-decoration: none;

      &:hover {
        color: #3071a9;
      }
    }

    .company-link {
      font-size: 16px;
      word-break: break-all;
      margin-right: 5px;
      vertical-align: middle;
    }

    .row-item {
      margin-top: 5px;

      &.margin-top-3 {
        margin-top: 3px;
      }

      &.margin-top-10 {
        margin-top: 10px;
      }
    }

    .label-text {
      color: #999;
      font-size: 14px;
      font-weight: unset;
    }

    .content {
      color: #333;
      font-size: 14px;
    }
  }

  .extra-link {
    margin-top: 5px;

    a {
      font-size: 14px;
      color: #ff722d;
      line-height: 22px;
      display: inline-block;

      .icon-wenzilianjiantou {
        color: #ff722d;
        font-size: 14px;
        margin-left: 2px;
      }
    }

    a:hover {
      color: #ff722d !important;
    }
  }
}

.company-status-tag {
  line-height: 18px;
  font-size: 12px;
  height: 20px;
  position: relative;
  top: -1px;
  cursor: auto;
  margin-right: 0;
}
