import { dateFormat } from '@/utils/format';
import { rmRMB2 } from '@/utils/number-formatter';
import { forEach } from 'lodash';
import moment from 'moment';
import { handleCompanyInfoTags4New } from '@/utils/handle-tag';

const checkDelist = (tag) => {
  let res = false;
  // 退市、上市不成功等情况，超过2年，隐藏标签
  if (tag?.dataExtend2 && typeof tag?.dataExtend2 === 'string') {
    const extraData = JSON.parse(tag?.dataExtend2 || '{}');
    const SUD = extraData?.SUD; // 上次更新时间
    res = (SUD && moment().diff(moment(SUD), 'years') >= 2) || false;
  }
  return res;
};

/**
 * 获取Safari 版本号
 * @returns {string|string}
 */
const getSafariVersion = () => {
  return navigator.userAgent.match(/Version\/([\d.]+)/)?.[1] || '';
};

const isGovernment = (keyNo) => {
  return keyNo && keyNo.indexOf('g') === 0;
};

const countryTagsSubText =
  '计算结果主要来源于企业信用信息网公示系统的股权信息（由企业自主披露并由各地工商部门形式审查），不代表企查查任何明示或是暗示的承诺及保证，也不得直接作为任何活动的依据，因参考、使用引起的争议、纠纷等由使用方自行承担。';

const getOwnedCompanyHoverText = (tag) => {
  const name = tag.Name || tag.name;
  const dataExtend = JSON.parse(tag.DataExtend || tag.dataExtend || '{}');
  let hoverObj: any = null;
  if (name === '央企') {
    hoverObj = {
      texts: [
        { key: '定义', value: '指国务院授权国有资产监督管理委员会履行出资人职责的企业。' },
        { key: '来源', value: '依据国务院、中央及地方国资委等相关官方公开披露信息认定央企类型。' },
      ],
    };
  } else if (name === '央企子公司') {
    if (String(dataExtend.CompType) === '4') {
      hoverObj = {
        texts: [
          { key: '定义', value: '指国有资产监督管理委员会披露的央企旗下的子公司或成员企业。' },
          {
            key: '来源',
            value: '依据国务院、中央及地方国资委等相关官方公开披露信息认定央企子公司类型。',
          },
        ],
      };
    } else {
      hoverObj = {
        texts: [
          { key: '定义', value: '指国有资产监督管理委员会披露的央企直接出资控股的企业。' },
          {
            key: '来源',
            value: '依据《企业国有资产交易监督管理办法》规定的股权计算逻辑，结合官方公开信息披露综合判定。',
          },
        ],
        subText: countryTagsSubText,
      };
    }
  } else if (name === '省属国企') {
    if (String(dataExtend.CompType) === '6') {
      hoverObj = {
        texts: [
          { key: '定义', value: '指各省和直辖市国有资产监督管理委员会披露的省属监管企业。' },
          {
            key: '来源',
            value: '依据国务院、中央及地方国资委等相关官方公开披露信息认定省属国企类型。',
          },
        ],
      };
    } else {
      hoverObj = {
        texts: [
          { key: '定义', value: '指各省和直辖市国有资产监督管理委员会直接出资控股的企业。' },
          {
            key: '来源',
            value: '依据《企业国有资产交易监督管理办法》规定的股权计算逻辑，结合官方公开信息披露综合判定。',
          },
        ],
        subText: countryTagsSubText,
      };
    }
  } else if (name === '市属国企') {
    if (String(dataExtend.CompType) === '7') {
      hoverObj = {
        texts: [
          { key: '定义', value: '指各地级市国有资产监督管理委员会所披露的市属监管企业。' },
          {
            key: '来源',
            value: '依据国务院、中央及地方国资委等相关官方公开披露信息认定市属国企类型。',
          },
        ],
      };
    } else {
      hoverObj = {
        texts: [
          { key: '定义', value: '指各地级市国有资产监督管理委员会直接出资控股的企业。' },
          {
            key: '来源',
            value: '依据《企业国有资产交易监督管理办法》规定的股权计算逻辑，结合官方公开信息披露综合判定。',
          },
        ],
        subText: countryTagsSubText,
      };
    }
  } else if (name === '县(区)属国企') {
    if (String(dataExtend.CompType) === '10') {
      hoverObj = {
        texts: [
          { key: '定义', value: '指各县(区)国有资产监督管理委员会所披露的区/县属监管企业。' },
          {
            key: '来源',
            value: '依据国务院、中央及地方国资委等相关官方公开披露信息认定县(区)属国企类型。',
          },
        ],
      };
    } else {
      hoverObj = {
        texts: [
          { key: '定义', value: '指各县(区)国有资产监督管理委员会直接出资控股的企业。' },
          {
            key: '来源',
            value: '依据《企业国有资产交易监督管理办法》规定的股权计算逻辑，结合官方公开信息披露综合判定。',
          },
        ],
        subText: countryTagsSubText,
      };
    }
  } else if (name === '国有企业') {
    let definition = '';
    // 1 国有独资
    // 2 国有全资
    // 3 国有控股
    // 4 国有实际控制企业
    // 0 其他
    switch (String(dataExtend.controlType)) {
      case '1':
        definition =
          '国有企业（国有独资企业）指国家单独出资、由国务院或者地方人民政府授权本级人民政府国有资产监督管理机构履行出资人职责的企业。';
        break;
      case '2':
        definition = '国有企业（国有全资企业）指政府部门、机构、事业单位、国有独资企业单独或共同出资，直接或间接合计持股为100%的企业。';
        break;
      case '3':
        definition = '国有企业（国有控股企业）是指在企业的全部资本中，国家资本（股本）所占比例大于50%的企业。';
        break;
      case '4':
        definition =
          '国有企业（国有实际控制企业）是指在企业的全部资本中，国家资本（股本）所占比例未超过50%，但为第一大股东，并且通过股东协议、公司章程、董事会决议或者其他协议安排能够对其实际支配的企业。';
        break;
      default:
        definition = '国有企业（国有控股企业）是指在企业的全部资本中，国家资本（股本）所占比例大于50%的企业。';
        break;
    }
    if (definition) {
      hoverObj = {
        texts: [
          { key: '定义', value: definition },
          {
            key: '来源',
            value: '依据《企业国有资产交易监督管理办法》规定的股权计算逻辑，结合官方公开信息披露综合判定。',
          },
        ],
        subText: countryTagsSubText,
      };
    }
  }
  const safariVersion = getSafariVersion();
  const isSafari156 = (safariVersion || '').includes('15.6');
  let hoverText: any = '';
  if (hoverObj) {
    hoverText += `<div style="${isSafari156 ? 'letter-spacing: 0.5px;' : ''}">`;
    hoverObj.texts &&
      hoverObj.texts.forEach((txt) => {
        hoverText += `<span style="font-weight: bold;">${txt.key}：</span>${txt.value}<br>`;
      });
    if (hoverObj.subText) {
      hoverText += `<span style="font-weight: bold;">特别提示：</span>${hoverObj.subText}<br>`;
    }
    hoverText += '</div>';
  }
  return hoverText;
};

const formatFinancialHoverText = (code) => {
  if (code === '0') return '';
  switch (code) {
    case '1':
      return '注册制，拟于沪主板上市';
    case '2':
      return '注册制，拟于深主板上市';
    case '3':
      return '注册制，拟于创业板上市';
    case '4':
      return '注册制，拟于科创板上市';
    case '107':
      return '注册制，拟于京主板上市';
    case '101':
      return '核准制，拟于沪主板上市';
    case '102':
      return '核准制，拟于深主板上市';
    case '31':
      return '当前为基础层，基础层包含的个股都是准入标准最低的个股，这里的公司风险是最大的，一方面它都是创新层和精选层中淘汰下来的个股，另一方面它是一个极其庞大的股票群体，近乎代表整个新三板市场';
    case '32':
      return '当前为创新层，创新层企业资质、财务状况较好，会被要求公司信息披露更加公开公正，支持连续竞价的交易方式使交易更灵活';
    case '33':
      return '当前为精选层，精选层是从创新层中挑出的优质企业，它的挑选标准是从利润指标、收入增长指标、市值指标三者中选择，满足其一即可入选';
    case '34':
      return '两网代表的是staq、net两个系统，退市是指股票退出交易市场。股票退市后会进入该市场交易，投资者可以在证券公司进行确权，并开通三板交易权限就能在三板市场挂牌转让';
  }
};

const handleManufacturingTag = (info) => {
  switch (info.type) {
    case '制造业单项冠军企业':
    case '国家级冠军企业':
    case '省级冠军企业':
    case '冠军企业':
      info.tips = '指长期专注于制造业某些特定细分产品市场,生产技术或工艺国际领先,单项产品市场占有率位居全球前列的企业。';
      break;
    case '制造业单项冠军产品企业':
      info.tips = '指拥有生产技术、工艺国内领先，产品质量精良，相关关键性能指标处于国内同类产品的领先水平产品的企业。';
      break;
    case '制造业头雁企业':
      info.tips = '指一批创新水平高、质量效益优、成长性较好、带动力强的头雁企业。';
      break;
    case '制造业大优强企业':
      info.tips = '指一批处于产业链关键环节、规模大、实力强、带动性大的大企业（大集团）。';
      break;
    case '高端装备制造业骨干企业':
      info.tips = '指创新能力强、研发水平高、示范引领作用强、市场占有率高的优势企业。';
      break;
    case '产融合作制造业重点企业':
      info.tips = '指纳入产融合作制造业“白名单”。';
      break;
    case '环保装备制造业规范条件企业':
      info.tips = '指大气治理、污水治理、环境监测仪器、固废处理等环保装备制造行业规范条件的企业。';
      break;
    case '制造业云上企业':
      info.tips =
        '指以数字基础设施云化部署为前提，全面云化构建核心业务系统，以数据资源为关键要素，打造企业内外部、产业链上下游的云上数字化协同生态，不断提高资源要素配置效率和生产管理智能化水平，从而实现竞争力、发展质量和效益显著提升的制造业企业。';
      break;
    case '基于互联网的制造业双创平台':
      info.tips = '指具有“双创”功能的大型制造企业及为制造业提供“双创”服务的信息技术企业、互联网企业、电信运营企业、高等院校和科研院所等。';
      break;
    case '先进制造业和现代服务业融合发展企业':
      info.tips = '指推动现代服务业同先进制造业深度融合，加快推进服务业数字化的企业。';
      break;
    case '智能制造标杆企业':
      info.tips =
        '指在设计、生产、物流、销售、服务等业务环节所达到的智能制造能力水平，重点关注数据驱动业务优化在企业发展过程发挥作用的企业。';
      break;
    case '服务型制造示范企业':
      info.tips =
        '服务型制造示范企业主要面向定制化服务、供应链管理、检验检测认证服务、全生命周期管理、总集成总承包、节能环保服务、生产性金融服务及其他服务型制造创新模式开展遴选。在本行业或相关领域内，其生产技术与工艺、服务能力与水平具有一定优势，服务收入占企业营业收入比重达规定占比水平线以上。';
      break;
    case '重点实验室':
      info.tips =
        '是指国家科技创新体系中重要的实验室，是国家组织高水平基础研究和应用基础研究、聚集和培养优秀科技人才、开展高水平学术交流、科研装备先进的重要基地。';
      break;
    case '创新中心':
      info.tips =
        '是指主要开展关键技术研究、工程化研发、科技成果转移转化及应用示范，提高关键核心技术创新能力，加快科研成果向现实生产力转化的单位。';
      break;
    case '绿色制造':
      info.tips =
        '绿色制造是一个产品的全生命周期概念，综合考虑资源效率和环境影响相协调的现代化制造模式。按照《工业和信息化部办公厅关于开展绿色制造体系建设的通知》，绿色制造名单包含绿色工厂、绿色设计产品、绿色工业园区、绿色供应链管理企业名单。';
      break;
    default:
      break;
  }
};

const schoolLevelMap = {
  985: '985工程是指中国共产党和中华人民共和国国务院在世纪之交为建设具有世界先进水平的一流大学而做出的重大决策。',
  211: '211工程是指面向21世纪，重点建设100所左右的高等学校和一批重点学科的建设工程。是新中国成立以来由国家立项在高等教育领域进行的规模最大、层次最高的重点建设工作',
  双一流:
    '“双一流”是指世界一流大学和一流学科。建设世界一流大学和一流学科，是中共中央、国务院作出的重大战略决策，也是继“211工程”，“985工程”之后的又一国家战略。',
};

const hospitalLevelMap = {
  三级特等:
    '简称“三特医院”，是依照中国现行《医院分级管理办法》等的规定而划分的医院等级之一。医院建设成绩显著，科室设置、人员配备、管理水平、技术水平、工作质量和技术设施等，按分等标准综合考核检查达900分及以上。三级特等医院从级别上是我国医院的最高级别。',
  三级甲等:
    '三级甲等医院是向所在地区以及周边辐射区域提供高水平医疗卫生服务和执行高等教育、科研任务的区域性以上医院。医院建设升级显著，科室设置、人员配备、管理水平、技术水平、工作质量和技术设施等，按分等标准综合考核检查达900分及以上。',
  三级乙等: '三级乙等医院是指建设成绩尚好，科室设置、人员配备、技术水平、工作质量、技术设施等，按分等标准综合考核检查达750-899分。',
  三级丙等:
    '三级丙等医院是依照中国现行《医院分级管理办法》等的规定而划分的医院等级之一。医院建设有一定成绩，基本标准考核合格，但与本标准要求尚有较大差距。按分等标准综合考核检查在749分及以下。三级丙等医院应有切实可行的改进措施。',
  二级甲等:
    '二级甲等医院属于二级医院的一种，在二级医院中等级最高，二级甲等是按照医院的功能、任务、设施条件、技术建设、医疗服务质量和科学管理的综合水平进行等级评定而确定的医院等级。',
  二级乙等:
    '二级乙等医院属于二级医院的一种，在二级医院中等级中等，主要指一般市、县医院及省辖市的区级医院，以及相当规模的工矿、企事业单位的职工医院。',
  二级丙等:
    '二级丙等医院主要指一般市、县医院及省辖市的区级医院，以及相当规模的工矿企事业单位的职工医院。二级医院是向多个社区提供综合医疗卫生服务和承担一定教学、科研任务的地区性医院。',
  一级甲等:
    '一级甲等医院属于一级医院的一种，在一级医院中等级最高，它是指直接向具有⼀定⼈⼝的社区提供医疗、预防、保健和康复服务的基层医疗卫⽣机构。',
  一级乙等:
    '一级乙等医院属于一级医院的一种，在一级医院中等级中等，它是指直接为一定人口的社区提供预防、治疗、保健、康复服务的基层医院、卫生院。',
  一级丙等: '一级丙等医院属于一级医院的等级之一，一级医院是直接为一定人口的社区提供预防、治疗、保健、康复服务的基层医院、卫生院。',
  一级医院:
    '是直接为社区提供医疗、预防、康复、保健综合服务的基层医院，是初级卫生保健机构。其主要功能是直接对人群提供一级预防，在社区管理多发病常见病现症病人并对疑难重症做好正确转诊，协助高层次医院搞好中间或院后服务，合理分流病人。',
  二级医院:
    '是跨几个社区提供医疗卫生服务的地区性医院，是地区性医疗预防的技术中心。其主要功能是参与指导对高危人群的监测，接受一级转诊，对一级医院进行业务技术指导，并能进行一定程度的教学和科研。',
  三级医院:
    '是跨地区、省、市以及向全国范围提供医疗卫生服务的医院，是具有全面医疗、教学、科研能力的医疗预防技术中心。主要提供专科（包括特殊专科）的医疗服务，解决危重疑难病症，接受二级转诊；完成培养各种高级医疗专业人才的教学和承担省以上科研项目的任务。',
  三级: '是跨地区、省、市以及向全国范围提供医疗卫生服务的医院，是具有全面医疗、教学、科研能力的医疗预防技术中心。主要提供专科（包括特殊专科）的医疗服务，解决危重疑难病症，接受二级转诊；完成培养各种高级医疗专业人才的教学和承担省以上科研项目的任务。',
};

export const handleTagCards = (tag, tips) => {
  const levelMap = {
    0: '无法判断',
    1: '国家级',
    2: '省级',
    3: '市级',
    4: '区/县级',
    5: '园区级',
    9: '其他',
  };
  const extendObj = JSON.parse(tag.dataExtend || '{}') || {};
  let releaseDate = extendObj.pd || '';
  let endDate = extendObj.ed || '';
  const source = extendObj.s || '';
  let hoverText = '';
  if (releaseDate) {
    releaseDate = moment(releaseDate * 1000).format('YYYY-MM-DD');
    hoverText += `<span style="font-weight: bold">发布日期</span>：${releaseDate}<br />`;
  }
  if (endDate) {
    endDate = moment(endDate * 1000).format('YYYY-MM-DD');
    if (moment(endDate).diff(moment().format('YYYY-MM-DD'), 'days') < 0) {
      tag.isExpired = true;
    }
    if (moment(endDate).diff(moment().format('YYYY-MM-DD'), 'months') <= -6) {
      tag.isExpiredSixMonth = true;
    }

    let valueTag = '';
    if (tag.isExpired && tag.type === 91 && Number(extendObj.l) === 1) {
      valueTag = '已到期，展示至本年末';
    } else if (tag.isExpired) {
      valueTag = '已到期，延续展示半年';
    }
    hoverText += `<span style="font-weight: bold">到期日期</span>：${endDate}<br />`;
    if (valueTag) {
      hoverText += `${valueTag}<br />`;
    }
  }
  hoverText += levelMap[extendObj.l] ? `<span style="font-weight: bold">级别</span>：${levelMap[extendObj.l]}<br />` : '';
  hoverText += source ? `<span style="font-weight: bold">来源</span>：${source}<br />` : '';
  hoverText += `<span style="font-weight: bold">定义</span>：${tips || '-'}`;
  // hoverText += `<div class='check-more'><a href=${link}>查看更多 <i class="iconfont icon-icon_jiantou8"></i></a></div>`
  return '<span style="font-size: 14px;line-height: 22px">' + hoverText + '</span>';
};

// 企业主页科技型企业的定义
export const tecTipMap = {
  88: '是指企业根据市场竞争需要设立的技术研发与创新机构，负责制定企业技术创新规划、开展产业技术研发、创造运用知识产权、建立技术标准体系、凝聚培养创新人才、构建协同创新网络、推进技术创新全过程实施。',
  89: '是以促进科技成果转化，培育科技企业和企业家精神为宗旨，提供物理空间、共享设施和专业化服务的科技创业服务机构，是国家创新体系的重要组成部分、创新创业人才的培养基地、大众创新创业的支撑平台。',
  90: '是指在工业主要产业中技术创新能力较强、创新业绩显著、具有重要示范和导向作用的企业。',
  91: '是指依托一定数量的科技人员从事科学技术研究开发活动，取得自主知识产权并将其转化为高新技术产品或服务，从而实现可持续发展的中小企业。',
  92: '是指为满足大众创新创业需求，提供工作空间、网络空间、社交空间和资源共享空间，积极利用众筹、众扶、众包等新手段，以社会化、专业化、市场化、网络化为服务特色，实现低成本、便利化、全要素、开放式运营的创新创业平台。',
  93: '指那些不为公众所熟知，却在某个细分行业或市场占据领先地位，拥有核心竞争力和明确战略，其产品、服务难以被超越和模仿的中小型企业。',
  94: '国家为了扶持高端技术性服务业的发展，对从事技术外包、业务外包和知识外包服务的企业进行税收等多项政策支持的企业类型。',
  95: '是指以科技人员为主体，以技术密集型产品研制、生产、销售以及技术开发、技术转让、技术咨询和技术服务为主要业务，按照自筹资金、自愿组合、自主经营、自负盈亏原则依法创办和经营的经济实体。',
  97: '是专精特新企业中的佼佼者，是专注于细分市场、创新能力强、市场占有率高、掌握关键核心技术、质量效益优的排头兵企业。',
  102: '一般指10亿美元以上估值，并且创办时间相对较短（一般为十年内）还未上市的企业。',
  103: '一般指创业后跨过死亡谷以科技创新或商业模式创新为支撑进入高成长期的中小企业。',
  105: '一般是指企业在研究、开发、生产、销售和管理过程中，通过技术创新、管理创新、服务创新或模式创新取得核心竞争力，提供高新技术产品或服务，具有较高成长性或发展潜力巨大的科技创新中小企业。',
  106: '一般指具有“专业化、精细化、特色化、新颖化”特征的工业中小企业。',
  107: '一般指技术水平领先、竞争能力强、成长性好的科技型初创企业。',
  108: '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础开展经营活动，在中国境内（不包括港、澳、台地区）注册的居民企业。',
  211: '由工业和信息化部、财政部从已认定的专精特新“小巨人”企业中择优选定并给予重点支持的企业。',
  911: '是指综合实力和创新能力较强的企业、高校或科研院所，具有较完备的工程技术综合配套试验条件，有一支高素质的研究开发、工程设计和试验的专业科技队伍，有稳定的经费来源，并能提供多种综合性技术服务的工程技术研究开发机构。',
  916: '指具有较高专业化水平、较强创新能力和发展潜力的企业，是优质中小企业的基础力量。',
};

const hoverMap = [
  {
    id: 622,
    text: '是指国务院和地方人民政府分别代表国家履行出资人职责的国有独资企业、国有独资公司以及国有资本控股公司，包括中央和地方国有资产监督管理机构和其他部门所监管的企业本级及其逐级投资形成的企业。',
  },
  { id: 109, text: '教育者有计划、有组织地对受教育者进行系统的教育活动的组织机构。' },
  {
    id: 110,
    text: '按照法律法规和行业规范，为病员开展必要的医学检查、治疗措施、护理技术、接诊服务、康复设备、救治运输等服务，以救死扶伤为主要目的医疗机构。',
  },
  { id: 111, text: '由政府利用国有资产设立的，从事教育、科技、文化、卫生等活动的社会服务组织。' },
  { id: 114, text: '中华人民共和国律师执行职务进行业务活动的工作机构。' },
  { id: 203, text: '利用自然人、法人或者其他组织捐赠的财产，以从事公益事业为目的，按照本条例的规定成立的非营利性法人。' },
  { id: 404, text: '建筑企业是指依法自主经营、自负盈亏、独立核算，从事建筑商品生产和经营，具有法人资格的经济实体。' },
  { id: 501, text: '独立于现有主板市场的新设板块，并在该板块内进行注册制试点。' },
  { id: 502, text: '独立于现有主板市场的新设板块，并在该板块内进行注册制试点。' },
  { id: 503, text: '独立于现有主板市场的新设板块，并在该板块内进行注册制试点。' },
  { id: 510, text: '药品临床试验机构是经国家食品药品监督管理局资格认定的承担药物临床试验的医疗机构。' },

  {
    id: 88,
    text: '根据创新驱动发展要求和经济结构调整需要，对创新能力强、创新机制好、引领示范作用大、符合条件的企业技术中心予以认定，并给予政策支持，鼓励引导行业骨干企业带动产业技术进步和创新能力提高。',
  },
  {
    id: 89,
    text: '培育和扶植高新技术中小企业的服务机构',
  },
  {
    id: 90,
    text: '工业主要产业中技术创新能力较强、创新业绩显著、具有重要示范和导向作用的企业。',
  },
  {
    id: 91,
    text: '以科技人员为主体，由科技人员领办和创办，主要从事高新技术产品的科学研究、研制、生产、销售，以科技成果商品化以及技术开发、技术服务、技术咨询和高新产品为主要内容，以市场为导向，实行“自筹资金、自愿组合、自主经营、自负盈亏、自我发展、自我约束”的知识密集型经济实体。',
  },
  {
    id: 92,
    text: '为满足大众创新创业需求，提供工作空间、网络空间、社交空间和资源共享空间，积极利用众筹、众扶、众包等新手段，以社会化、专业化、市场化、网络化为服务特色，实现低成本、便利化、全要素、开放式运营的创新创业平台。',
  },
  {
    id: 93,
    text: '不为公众所熟知，却在某个细分行业或市场占据领先地位，拥有核心竞争力和明确战略，其产品、服务难以被超越和模仿的中小型企业。',
  },
  {
    id: 94,
    text: '国家为了扶持高端技术性服务业的发展，对从事技术外包、业务外包和知识外包服务的企业进行税收等多项政策支持的企业类型。',
  },
  {
    id: 95,
    text: '以科技人员为主体创办的，实行自筹资金、自愿组合、自主经营、自负盈亏、自我约束、自我发展的经营机制，主要从事科技成果转化及技术开发、技术转让、技术咨询、技术服务或实行高新技术及其产品的研究、开发、生产、销售的智力、技术密集型的经济实体。',
  },
  {
    id: 96,
    text: '具有自主知识产权，连续两年销售收入年均增长30%以上，且最近一个会计年度达500万元人民币以上的企业。',
  },
  {
    id: 97,
    text: '培育一批主营业务突出、竞争力强、成长性好的专精特新‘小巨人’企业。',
  },
  {
    id: 102,
    text: '一般指10亿美元以上估值，并且创办时间相对较短（一般为十年内）还未上市的企业。',
  },
  {
    id: 103,
    text: '高成长中小企业，一般指创业后跨过死亡谷以科技创新或商业模式创新为支撑进入高成长期的中小企业。',
  },
  {
    id: 104,
    text: '主要是指拥有自主知识产权和知名品牌，具有较强国际竞争力，依靠技术创新获取市场竞争优势和持续发展的企业。',
  },
  {
    id: 105,
    text: '一般指在研究、开发、生产、销售和管理过程中，通过技术创新、管理创新、服务创新或模式创新取得核心竞争力，提供高新技术产品或服务，具有较高成长性或发展潜力巨大的科技创新中小企业。',
  },
  {
    id: 106,
    text: '一般指具有“专业化、精细化、特色化、新颖化”特征的工业中小企业。',
  },
  {
    id: 107,
    text: '一般指技术水平领先、竞争能力强、成长性好的科技型初创企业。',
  },
  {
    id: 108,
    text: '在《国家重点支持的高新技术领域》内，持续进行研究开发与技术成果转化，形成企业核心自主知识产权，并以此为基础开展经营活动，在中国境内（不包括港、澳、台地区）注册一年以上的居民企业。',
  },
  {
    id: 112,
    text: '国籍（或地区）/注册地：香港',
  },
  {
    id: 115,
    text: '国籍（或地区）/注册地：台湾',
  },
  {
    id: 200,
    text: '人们为了有效地达到特定目标按照一定的宗旨、制度、系统建立起来的共同活动集体。',
  },
  {
    id: 505,
    text: '小微企业基于国家市场监督管理部门公布的小微企业库，依据企业营业收入，资产总额，从业人员数量等标准进行划分。企业划分由政府综合统计部门根据统计年报每年确定一次。',
  },
  {
    id: 508,
    text: 'GSP是《药品经营质量管理规范》的英文缩写，是药品经营企业统一的质量管理准则。药品经营企业应在药品监督管理部门规定的时间内达到GSP要求，并通过认证取得认证证书。',
  },
  {
    id: 507,
    text: 'GMP是一套指导食物、药品、医疗产品生产和质量管理的标准，药品GMP认证是国家依法对药品生产企业（车间）和药品品种实施GMP监督检查并取得认可的一种制度。',
  },
  {
    id: 510,
    text: '药品临床试验机构是经国家食品药品监督管理局资格认定的承担药物临床试验的医疗机构。',
  },
  {
    id: 908,
    text: '机关单位指从事国家管理和行使国家权力的机关。',
  },
];

export const hoverHandle = (type) => {
  const find = hoverMap.find((e) => e.id === type);
  if (find) {
    return find.text;
  } else {
    return '';
  }
};

const getStockName = (tag, stockStatus) => {
  let name = tag.name;
  if (tag.shortName) {
    name += ` | ${tag.shortName}`;
  }
  if (tag.dataExtend) {
    name += ` ${tag.dataExtend} `;
  }
  if (tag.name === '新三板' && stockStatus === '已退市') {
    stockStatus = '已摘牌';
  }
  if (stockStatus) {
    name += ` ${stockStatus}`;
  }
  return name;
};

const stockNameMap = [
  { id: 1, stockStatus: '' },
  { id: 2, stockStatus: '' },
  { id: 401, stockStatus: '' },
  { id: 301, stockStatus: '' },
  { id: 7, stockStatus: '' },
  { id: 17, stockStatus: '' },
  { id: 11, stockStatus: '' },
  { id: 8, stockStatus: '已退市' },
  { id: 9, stockStatus: '已退市' },
  { id: 12, stockStatus: '待上市' },
  { id: 13, stockStatus: '暂停上市' },
];

const getStockName4Type = (tag) => {
  const find = stockNameMap.find((e) => e.id === tag.type);
  if (find) {
    return getStockName(tag, find.stockStatus);
  } else {
    return '';
  }
};

const needShowTagID = [
  1, 2, 3, 6, 401, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 21, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 121, 622, 79, 88, 89, 90, 91,
  92, 93, 94, 95, 96, 97, 102, 103, 104, 105, 106, 107, 108, 112, 115, 200, 211, 911, 912, 99, 109, 110, 111, 114, 119, 203, 207, 208, 209,
  301, 302, 402, 501, 502, 503, 506, 601, 602, 603, 623, 901, 605, 902, 606, 904, 604, 608, 907, 17, 171, 510, 620, 625, 700, 701, 702, 704,
  705, 706, 707, 708, 908, 910, 913, 916, 90, 91, 92, 93, 94, 95, 505, 507, 508, 9990, 9991, 9992, 1000001, 1000002, 1000003, 1000004,
  1000005, 1000006, 1000007, 1000008, 1000009, 1000010, 1000011,
];

export const getTags = (
  tagsInfo,
  {
    keyNo,
    originalName,
    isQCCPRO_G,
    navPos,
    navPosJump,
    companyName,
  }: { keyNo; originalName; isQCCPRO_G?; navPos; navPosJump?; companyName? }
) => {
  const tags: any = [];
  tagsInfo.forEach((tag) => {
    const find = needShowTagID.find((e) => e === tag.type);
    if (!find) {
      return;
    }
    const extraData = JSON.parse(tag.dataExtend2 || '{}');
    let extendObj;

    let name = getStockName4Type(tag) || tag.name;
    let className: string = '';
    let colorClass = '';
    let color = '';
    let bgColor = '';
    let hoverText: any = hoverHandle(tag.type);
    let hoverEvent: any = null;
    let link = '';
    const clink = ''; // 不新开页面
    let click;
    let trackName = '';
    const suffixIcon = '';
    let autoWidth = false;
    let toolTipMaxWidth;
    const tips = tecTipMap[tag.type] || '';
    let key;
    const caseObj: Record<string, any> = {};
    switch (tag.type) {
      case 708:
        hoverText = '商事登记机关对符合一定条件的失联企业或被吊销营业执照的“僵尸企业”，在履行公告程序后，将其予以除名。';
        break;
      case 707:
        if (navPosJump) {
          click = () => navPosJump('经营风险', 'mktScpecOverdueCountAreaId');
        }

        break;
      case 705:
        if (navPosJump) {
          click = () => navPosJump('经营风险', 'bankRuptcyListAreaId');
        }
        break;
      // 科技型企业
      case 88:
      case 89:
      case 90:
      case 91:
      case 92:
      case 93:
      case 94:
      case 95:
      case 97:
      case 102:
      case 103:
      case 105:
      case 106:
      case 107:
      case 108:
      case 211:
      case 916:
        hoverText = handleTagCards(tag, tips);
        if (navPosJump) {
          click = () =>
            navPosJump(
              '企业发展',
              'tecCountAreaId',
              '',
              '',
              tag.isExpired
                ? {
                    tab: '历史信息',
                    id: 'tecCountAreaId',
                  }
                : null
            );
        }
        break;
      case 3: // 产品信息
        link = `/product-info?id=${tag.dataExtend}`;
        hoverText = `
                <span style="font-size: 14px;line-height: 22px">
                  <span style="font-weight: bold;">当前轮次：</span>${tag.name}
                  <br />
                  <span style="font-weight: bold;">日期：</span>${extraData.D || '-'}
                  <br />
                  <span style="font-weight: bold;">融资金额：</span>${rmRMB2(extraData.A) || '-'}
                  <br />
                  <span style="font-weight: bold;">投资方：</span>${extraData.I || '-'}
                </span>
            `;
        break;
      case 110: // 医院
        name = tag.name;
        hoverText =
          '按照法律法规和行业规范，为病员开展必要的医学检查、治疗措施、护理技术、接诊服务、康复设备、救治运输等服务，以救死扶伤为主要目的医疗机构。';
        break;
      case 15: // 医院标签附加
        name = tag.dataExtend;
        hoverText = hospitalLevelMap[tag.dataExtend];
        break;
      case 16: // 学校标签附加
        hoverText = schoolLevelMap[tag.name];
        break;
      case 21: // 学校办学性质
        name = tag.dataExtend || tag.name;
        break;
      case 99: // 曾用名
        name = `${tag.name}`;
        if (originalName && originalName.length) {
          hoverText = originalName
            .map((vo) => {
              let text = vo.name;
              if (vo.startDate || vo.changeDate) {
                text += `（${dateFormat(vo.startDate, {
                  pattern: 'YYYY-MM-DD',
                  defaultVal: '~',
                  x1000: false,
                })} 至 ${dateFormat(vo.changeDate, {
                  pattern: 'YYYY-MM-DD',
                  defaultVal: '~',
                  x1000: false,
                })}）`;
              }
              return text + '<br>';
            })
            .join('');
        }
        // suffixIcon = 'fa el-icon-caret-bottom'
        autoWidth = true;
        toolTipMaxWidth = 400;

        break;
      case 207: // 私募基金管理人
        break;
      case 700:
        name = tag.name;
        if (extraData?.date && extraData?.type) {
          hoverText += `
                <span style="font-size: 14px;line-height: 22px">
                <span style="font-weight: bold;">注销时间：</span>${extraData?.Date || '-'}
                <br />
                <span style="font-weight: bold;">注销类型：</span>${extraData?.Type || '-'}
                <br />
                <span style="font-weight: bold;">来源：</span>中国证券投资基金业协会披露
                </span>
              `;
        }
        break;
      case 208: // 投资机构
        if (isQCCPRO_G) {
          name = '投资机构';
          link = `/investAgency?investId=${tag.dataExtend}&searchKey=${tag.name}`;
        }
        break;
      case 209: // 私募基金
        break;
      case 301: // 新四板
        trackName = '新四板';
        break;
      case 302: // 非正常户
        name = tag.name;
        extendObj = JSON.parse(tag.dataExtend || '{}');

        if (extendObj.JoinTime) {
          hoverText = `列入日期：${moment(extendObj.JoinTime * 1000).format('YYYY-MM-DD')} <br />`;
        }
        if (extendObj.JoinAuthority) {
          hoverText += `列入机关：${extendObj.JoinAuthority} <br />`;
        }
        hoverText +=
          '说明：纳税人负有纳税申报义务，但连续三个月所有税种均未进行纳税申报的，税收征管系统自动将其认定为非正常户，并停止其发票领用簿和发票的使用。';
        break;
      case 506: // 银保监会
        break;
      case 623: // 假冒国企
        name = tag.Name || tag.name;
        hoverText =
          '来源于中央企业公告的假冒国企名单，明确有关公司及其下设各级子公司均为假冒国企，与中央企业无任何隶属或股权关系，也不存在任何投资、合作、业务等关系，其一切行为均与中央企业无关。';
        break;
      case 901: // 经营异常
      case 605:
        if (navPos) {
          click = () => navPos('fengxian', 'yichang');
        }
        break;
      case 902: // 严重违法
      case 606:
        if (navPos) {
          click = () => navPos('fengxian', 'svlist');
        }
        break;
      case 904: // 失信
      case 604:
        if (navPos) {
          click = () => navPos('susong', 'shixinlist');
        }
        break;
      case 704:
        if (navPosJump) {
          click = () => navPosJump('法律诉讼', 'sumptuaryListAreaId');
        }
        break;
      case 608: // 被执行人
        if (navPos) {
          click = () => navPos('susong', 'zhixinglist');
        }
        break;
      case 907: // 债券违约
        if (name === '债券违约') {
          hoverText =
            '发行人未能按约定足额偿付债务融资工具本金或利息，以及因破产等法定或约定原因，导致债务融资工具提前到期且发行人未能按期足额偿付本息的情形。';
        } else if (name === '债券展期') {
          hoverText =
            '债券违约后，发行人可以与违约债务融资工具持有人协商调整当期债务融资工具的基本偿付条款并变更登记要素，若没有相关法律、法规或发行文件约定的，应经全体持有人表决同意，签订变更协议。';
        }
        if (navPosJump) {
          click = () => navPosJump('经营状况', 'operCreditorRightsAreaId', 'switchBondDefaultTab', 1);
        }
        break;
      case 17: // 债券
        name = getStockName4Type({ ...tag, dataExtend: extendObj.symbol });
        trackName = '债券';
        break;
      case 171: // 债券
        trackName = '债券';
        if (navPosJump) {
          click = () => navPosJump('经营状况', 'operCreditorRightsAreaId');
        }
        break;
      case 510: // 药品临床试验机构
        if (navPos) {
          click = () => navPos('assets', 'zhengshulist');
        }
        break;
      case 620: // 历史迁出
        name = tag.name;
        try {
          key = JSON.parse(tag.dataExtend)
            .map((item) => {
              return item.k;
            })
            .join(',');
        } catch (error) {}
        if (key) {
          link = '历史迁出';
          click = () => {
            // requestService
            //   .getHistoryMoveOutList({
            //     id: key,
            //   })
            //   .then((res) => {
            //     if (res.status !== '200') {
            //       return;
            //     }
            //     window.AppRuntimeContext.instance.$uiService.showDialog(historyMoveOut, {
            //       list: res.resultList,
            //     });
            //   });
          };
        }
        break;
      case 625: // 历史迁出--新
        name = tag.name;
        hoverEvent = (tag) => {
          // requestPenetrationService
          //   .post('/ECILocal/GetDetailsAllInOne', {
          //     keyNos: [keyNo],
          //     selection: ['ListOfMoveOutHistory'],
          //   })
          //   .then((res) => {
          //     if (res && res.Result) {
          //       const list = res.Result?.[0].MoveOutHistory;
          //       tag.hoverText = historyOut625(list, companyName);
          //       // this.$set(tag, 'hoverText', 'ddddddddddddddddddddd')
          //     }
          //   });
        };
        break;
      case 905: // 股东质押
        if (navPosJump) {
          click = () => navPosJump('股东信息', 'shareholderListAreaId');
        }
        break;
      case 908: // 机关单位
        name = tag.Name || tag.name;
        hoverText = '机关单位指从事国家管理和行使国家权力的机关。';
        break;
      case 910: // 媒体
        name = tag.Name || tag.name;
        break;
      // 制造业企业
      case 913:
        name = tag.Name || tag.name;
        if (['国家级冠军企业', '省级冠军企业', '冠军企业'].includes(name)) {
          name = '制造业单项冠军企业';
        }
        caseObj.tagObj = { type: name };
        handleManufacturingTag(caseObj.tagObj);
        hoverText = caseObj.tagObj.tips;
        hoverText = handleTagCards(tag, hoverText);
        if (navPosJump) {
          click = () => navPosJump('企业发展', 'tecCountAreaId');
        }
        break;
      case 706:
        extendObj = JSON.parse(tag.dataExtend || '{}');
        name = tag.name;
        hoverText = '';
        if (+extendObj?.CX) {
          hoverText += `<span style="font-weight: bold;">诚信信息：</span>${extendObj?.CX || 0}条<br />`;
        }
        if (+extendObj?.TS) {
          hoverText += `<span style="font-weight: bold;">提示信息：</span>${extendObj?.TS || 0}条<br />`;
        }
        hoverText += '<span style="font-weight: bold;">来源：</span><span>中国证券投资基金业协会披露</span>';
        break;
      case 622: // 国有企业
        name = tag.Name || tag.name;
        hoverText = getOwnedCompanyHoverText(tag);
        break;
      case 9990:
        hoverText = `国籍（或地区）/注册地：${tag.name}`;
        break;
      case 1:
      case 8: // 新三板 两网及退市  8 退市
      case 33: // 新三板 待挂牌
        name = extraData.WL || tag.dataExtend || tag.Name || tag.name;
        if (navPos) {
          click = () => navPos('sanban', 'sanbanrealtime');
        }
        trackName = '新三板';
        hoverText = tag.type === 1 ? formatFinancialHoverText(extraData.WH) : ''; // 退市无hover
        break;
      case 2:
      case 12: // 已申报有代码
      case 9: // 沪深A股已上市  // A股 退市
        name = extraData.WL || tag.dataExtend || tag.Name || tag.name;
        if (navPos) {
          click = () => navPos('ipo', 'iporealtime');
        }
        trackName = 'A股';
        break;
      case 7: // 中概股 上市
      case 10: // 港股 退市 不跳转详情
      case 11: // 美股 上市
      case 25: // 美股退市
      case 13: // 暂停上市
      case 18: // 北交所
      case 26: // A股 辅导期
      case 28: // 终止发行
      case 29: // 港股待上市
      case 30: // 美股 VIE
      case 31: // 港股 VIE
      case 32: // 新三板 挂牌申报
      case 34: // 美股 待上市
      case 121: // 赴港上市IPO
        name = extraData.WL || tag.Name || tag.name;
        hoverText = formatFinancialHoverText(extraData.WH);
        break;
      case 6: // 大陆公司港股
      case 401: // 香港公司港股
        name = extraData.WL || tag.Name || tag.name;
        if (navPos) {
          click = () => navPos('hkstock', 'hkstockrealtime');
        }
        trackName = '港股';
        break;
      case 27: // 北交所 已申报无代码
      case 14: // A股 主板申报 未上会
      case 35: // A股 主板 注册制
      case 502: // 科创板
      case 503: // 科创板
      case 602: // 创业板
      case 603: // 创业板
        name = extraData.WL || tag.dataExtend || tag.Name || tag.name;
        if (navPos) {
          click = () => navPos('ipo', 'iporealtime');
        }
        hoverText = formatFinancialHoverText(extraData.WH);
        break;

      default:
        break;
    }
    const formatStyle = handleCompanyInfoTags4New(tag);
    colorClass = formatStyle.colorClassName;
    bgColor = formatStyle.bgColor;
    color = formatStyle.color;
    className = formatStyle.className;
    if (name && !([901, 902, 904, 907].includes(tag.type) && isGovernment(keyNo)) && !checkDelist(tag) && !tag.isExpiredSixMonth) {
      // 政府隐藏标签
      tags.push({
        name,
        trackName,
        colorClass: tag.isExpired ? 'grey' : colorClass,
        className: tag.isExpired ? 'grey' : className,
        color,
        bgColor,
        hoverText,
        hoverEvent,
        link,
        clink,
        click,
        suffixIcon,
        type: tag.type,
        autoWidth,
        toolTipMaxWidth,
      });
    }
  });

  return tags;
};

const getOperTypeName = (operType) => {
  operType = +operType;
  let name = '';
  switch (operType) {
    case 1:
      name = '法定代表人';
      break;
    case 2:
      name = '执行事务合伙人';
      break;
    case 3:
      name = '负责人';
      break;
    case 4:
      name = '经营者';
      break;
    case 5:
      name = '投资人';
      break;
    case 6:
      name = '董事长';
      break;
    case 7:
      name = '理事长';
      break;
    case 8:
      name = '代表人';
      break;
    default:
      name = '法定代表人';
  }
  return name;
};

const navPosT = (tab, pos, keyNo, route, sub?) => {
  if (route?.name === 'company-detail') {
    $(document).trigger('onNav', { tab, pos, sub });
  }
};

const commonHandle = (tagsArr, companyInfo?, keyNo?, route?, isNew?) => {
  const originalName = companyInfo?.originalName;
  const navPos = (tab, pos) => {
    navPosT(tab, pos, keyNo, route);
  };
  const tagList = getTags(tagsArr, { keyNo, originalName, navPos });
  return tagList;
};

export const formatEnterpriseOverview = (data) => {
  if (!data) {
    return null;
  }
  let startDate = '';
  if (data.startDate && data.startDate !== '0' && data.startDate !== 0) {
    startDate = moment(+data.startDate * 1000).format('YYYY-MM-DD');
  }
  const nodeData: Record<string, any> = {
    id: data.id,
    name: data.name,
    image: data.image,
    registCapi: data.registCapi,
    startDate: startDate,
    region: data.region,
    mainName: data.mainName,
    mainNo: data.mainNo,
    memberCount: data.memberCount,

    multipleOper: data.multipleOper,
    enName: data.enName,
    isHK: data.id.indexOf('h') === 0,
    isTW: data.id.indexOf('t') === 0,
    kind: data.kind,
    isStock: false,
  };

  if (data.oper) {
    nodeData.operType = data.oper.operType;
    nodeData.operTypeName = getOperTypeName(data.oper.operType);
    nodeData.operName = data.oper.name;
    nodeData.operId = data.oper.keyNo;
  }

  nodeData.tags = [];

  // if (data.tags) {
  //   nodeData.tags = _.map(data.tags, tag => {
  //     return {
  //       type: +tag.type,
  //       name: tag.name
  //     }
  //   })
  // }
  // if (data.tags) {
  //   nodeData.tags = _.map(data.tags, tag => {
  //     let tagName = tag.name
  //     if (tag.type === 1 || tag.type === 2 || tag.type === 7 || tag.type === 301 || tag.type === 401) {
  //       tagName = tag.name + ' | ' + tag.shortName + ' ' + tag.dataExtend
  //       nodeData.isStock = true
  //     } else if (tag.type === 502) {
  //       tagName = tag.dataExtend
  //     } else if (tag.type === 8 || tag.type === 9) {
  //       tagName = tag.name + ' | ' + tag.shortName + ' ' + tag.dataExtend + ' 已退市'
  //     }
  //     if (tag.dataExtend2) {
  //       const tagData = JSON.parse(tag.dataExtend2)
  //       if (tagData?.WL) {
  //         tagName = tagData.WL
  //       }
  //     }
  //     return {
  //       type: +tag.type,
  //       name: tagName
  //     }
  //   })
  // }
  if (data.tags) {
    forEach(data.tags, (tag) => {
      if (+tag.type === 903) {
        nodeData.shortStatus = tag.name;
      }
    });
    nodeData.tags = commonHandle(data.tags);
  }

  return nodeData;
};
