import _ from 'lodash';

const __PLUGIN__ = true;
const __QCCPRO_G__ = true;

const toDash = (str) => {
  if (str) {
    return str;
  }
  if (str === '' || str === null) {
    return '-';
  }
  if (str === 0) {
    return str;
  }
  return '-';
};
const isBlank = (obj) => {
  if (!obj || obj === '' || $.trim(obj) === '') {
    return true;
  }
  return false;
};

/**
 * 尝试转换成JSON对象
 * @param str
 * @param defaultValue
 * @returns {any|null}
 */
const tryParseJSON = (str, defaultValue = null) => {
  if (_.isEmpty(str)) {
    return defaultValue;
  }
  try {
    const obj = JSON.parse(str);
    if (_.isObject(obj)) {
      return obj;
    }
  } catch (e) {}
  return defaultValue;
};

const getForbidOurterLinkerFlag = () => {
  // if (__QCCPRO_G__) {
  //   // if (keyNo) {
  //   //   return !`${keyNo}`.startsWith('p', 0)
  //   // }
  //   return true
  // }
  // if (!__PLUGIN__ && !store.state.appState.currentIsAMLAccount) {
  //   return false
  // }
  // return (
  //   store.state.appState.currentLoginUserForbidOuterLinkerFlag ||
  //   store.state.appState.currentIsAMLAccount
  // )
  return true;
};

export const generateAssignorList = (assignorList, cb) => {
  const obj: string[] = [];
  const result: string[] = [];
  assignorList?.forEach((item) => {
    obj.push(cb(item.name || item.Name, item.keyNo || item.KeyNo));
  });
  if (obj.length > 0) {
    result.push(obj.join('、'));
    result.unshift('（委派代表：');
    result.push('）');
  }
  return result.join('');
};

export const getCompanyOrPersonLinkerByOrg = (name, keyNo, org?, options?): string => {
  // org -1 无法判断, -2:没有Id的人名,0:公司,1:社会组织,2:主要人员,3:香港公司,4:政府机构和学校,5:台湾公司, 100: 基金
  options = options || {};
  name = options.name || name || '-';
  let otherUrlParams: string | string[] = [];
  if (options.redirect) {
    otherUrlParams.push(`redirect=${options.redirect}`);
  }
  if (options.subRediect) {
    otherUrlParams.push(`subRediect=${options.subRediect}`);
  }
  if (options.rootKeyNo) {
    otherUrlParams.push(`rootKeyNo=${options.rootKeyNo}`);
  }
  if (otherUrlParams.length) {
    otherUrlParams = '&' + otherUrlParams.join('&');
  }
  const isForbidOurterFlag = !options.mustJumpLink && getForbidOurterLinkerFlag();
  let resultLabel = !keyNo || isForbidOurterFlag || (isForbidOurterFlag && keyNo.indexOf('p') === 0) ? 'span' : 'a';
  let onclickStr = `${options.stop ? 'onclick="event.stopPropagation();"' : ''}`;

  let assignorInfo = '';
  if (options.assignorList) {
    assignorInfo = generateAssignorList(options.assignorList, getCompanyOrPersonLinkerByOrg);
  }

  if (__PLUGIN__ && isForbidOurterFlag && keyNo && keyNo.indexOf('p') !== 0) {
    onclickStr = ` onclick="onCompanyTextClick('${name}','${keyNo}')${options.stop ? ';event.stopPropagation();' : ''}"`;
    options.className = (options.className || '') + ' _clickContent';
  }

  let xname = options && options.title ? options.title || '' : name || '';
  if (options.noTitle) {
    xname = '';
  }
  let shortName = name;
  if (options.length) {
    shortName = shortName.length > options.length ? `${shortName.substr(0, options.length)}...` : shortName;
  }
  if (options.specialShortName) {
    shortName = options.specialShortName;
  }

  if (!keyNo || keyNo.length < 32) {
    if (options.companyId && options.personName) {
      return `<${resultLabel} target="_blank" style="${
        options.style || ''
      }" class="${options.className || ''}" title="${xname}" ${onclickStr}
              href="/embed/beneficaryDetail?companyId=${options.companyId}&personName=${encodeURIComponent(options.personName)}">
              ${toDash(shortName)}
              </${resultLabel}>`;
    }
    return `<span title="${xname}" style="${options.style || ''}" class="${options.className || ''}">${shortName}</span>${assignorInfo}`;
  }
  if (!org) {
    if (isBlank(keyNo) || keyNo.startsWith('h-')) {
      return `<span title="${xname}" style="${options.style || ''}" class="${
        options.className || ''
      }">${toDash(shortName)}</span>${assignorInfo}`;
    }

    if (keyNo.indexOf('p') === 0) {
      let pName = shortName;
      let nameMark = '(工商公示)';
      if (shortName.indexOf('(基金业协会备案)') > -1 || name.indexOf('(工商公示)') > -1) {
        if (shortName.indexOf('(基金业协会备案)') > -1) {
          nameMark = '(基金业协会备案)';
        }
        pName = pName.replace(nameMark, '');
        return (
          '<span>' +
          `<${resultLabel} target="_blank" class="${options.className || ''}" ${onclickStr} title="${xname}" style="${options.style || ''}"
                  href="/embed/beneficaryDetail?personId=${keyNo}">${pName}</${resultLabel}>` +
          nameMark +
          '</span>'
        );
      }
      return `<${resultLabel} target="_blank" class="${
        options.className || ''
      }" ${onclickStr} title="${xname}" style="${options.style || ''}"
          href="/embed/beneficaryDetail?personId=${keyNo}">${shortName}</${resultLabel}>`;
    }
    return `<${resultLabel} target="_blank" class="${options.className || ''}" ${onclickStr} title="${xname}" style="${options.style || ''}"
          href="/embed/companyDetail?keyNo=${keyNo}${otherUrlParams}">${shortName}</${resultLabel}>${assignorInfo}`;
  }
  switch (`${org}`) {
    case '-1':
      return '';
    case '-2':
      return shortName + `${assignorInfo}`;
    case '16': // 公募基金
      return `<${resultLabel} target="_blank" class="${
        options.className || ''
      }" ${onclickStr} title="${xname}"  href="/embed/webfund/${keyNo}">${shortName}</${resultLabel}>`;
    case '6': // 私募基金
    case '19': // 私募基金管理
      return shortName;
    case '2':
      return `<${resultLabel} target="_blank" class="${options.className || ''}" ${onclickStr} title="${xname}"
              href="/embed/beneficaryDetail?personId=${keyNo}">${shortName}</${resultLabel}>`;
    case '3':
    case '4':
    case '5':
    case '7':
      return `<${resultLabel} target="_blank" class="${options.className || ''}" ${onclickStr} title="${xname}"
              href="/embed/companyDetail?keyNo=${keyNo}${otherUrlParams}">${shortName}</${resultLabel}>${assignorInfo}`;
    case '13':
    case '-3':
      if (isForbidOurterFlag) {
        return `<span title="${options && options.title ? options.title : name}" class="${options.className || ''}">
              ${options && options.name ? options.name : name}</span>`;
      }
      return `<${resultLabel} title="${options && options.title ? options.title : name}" class="${options.className || ''}"
              href="/embed/investAgency?investId=${keyNo}&searchKey=${encodeURIComponent(name)}">${
                options && options.name ? options.name : name
              }</${resultLabel}>`;
    case '20':
    case '21':
      return `<${resultLabel} target="_blank" class="${options.className || ''}"  ${onclickStr} title="${xname}"
              href="/embed/groupdetail?keyNo=${keyNo}">${shortName}</${resultLabel}>`;
    case '99':
    case '18':
      if (__QCCPRO_G__) {
        resultLabel = 'a';
      }
      return `<${resultLabel} target="_blank" class="${options.className || ''}"  ${onclickStr} title="${xname}"
              href="/embed/product-info?id=${keyNo}">${shortName}</${resultLabel}>`;
    case '100':
      return `<${resultLabel} target="_blank" class="${options.className || ''}"  ${onclickStr} title="${xname}"
              href="/embed/webfund/${keyNo}">${shortName}</${resultLabel}>`;
    default:
      if (keyNo) {
        if (keyNo.indexOf('p') === 0) {
          let pName = shortName;
          let nameMark = '(工商公示)';
          if (shortName.indexOf('(基金业协会备案)') > -1 || name.indexOf('(工商公示)') > -1) {
            if (shortName.indexOf('(基金业协会备案)') > -1) {
              nameMark = '(基金业协会备案)';
            }
            pName = pName.replace(nameMark, '');
            return (
              '<span>' +
              `<${resultLabel} target="_blank" class="${options.className || ''}" ${onclickStr} title="${xname}"
                      href="/embed/beneficaryDetail?personId=${keyNo}">${pName}</${resultLabel}>` +
              nameMark +
              '</span>'
            );
          }
          return `<${resultLabel} target="_blank" class="${options.className || ''}" ${onclickStr} title="${xname}"
                  href="/embed/beneficaryDetail?personId=${keyNo}">${shortName}</${resultLabel}>`;
        }
        return `<${resultLabel} target="_blank" class="${options.className || ''}" ${onclickStr} title="${xname}"
                  href="/embed/companyDetail?keyNo=${keyNo}${otherUrlParams}">${shortName}</${resultLabel}>${assignorInfo}`;
      }
      return `${shortName || '-'}${assignorInfo}`;
  }
};

export const generateCompanyDirectors = (row, options: Record<string, any> = {}) => {
  const { operList: directorsText, opername: directorName, operkeyno: directorKeyNo } = row;
  const { type = 'page', resultType = 'string', ignoreSingleDirector = false, ignoreEmptyText = false } = options;
  const directors = tryParseJSON(directorsText, directorsText);

  // 最后的replace原因未知，从其他地方复制的
  const getDirectorText = (name, keyNo) =>
    type === 'page' ? getCompanyOrPersonLinkerByOrg(name, keyNo).replace('title=', 'stitle=') : name;

  if (_.isArray(directors) && !_.isEmpty(directors)) {
    const fullDirectorTexts = directors.map((director) => {
      const name = director.Name || director.name;
      const keyNo = director.KeyNo || director.keyNo;
      const assignorList = director.AssignorList || director.assignorList;
      const assignorSource = director.AssignorSource || director.assignorSource;
      const directorText = getDirectorText(name, keyNo);
      let delegateText = '';
      if (!_.isEmpty(assignorList)) {
        const assignorSourceText =
          assignorSource === 1 ? (type === 'page' ? ' <span class="text-gray font-12">来源于中基协</span>' : ' 来源于中基协') : '';
        delegateText = `（委派代表：${assignorList.map((item) => getDirectorText(item.Name || item.name, item.KeyNo || item.keyNo)).join('、')}${assignorSourceText}）`;
      }
      return directorText + delegateText;
    });
    return resultType === 'string' ? fullDirectorTexts.join('、') : fullDirectorTexts;
  }
  if (directorName && !ignoreSingleDirector) {
    return getDirectorText(directorName, directorKeyNo);
  }
  if (!ignoreEmptyText) {
    return '-';
  }
};
