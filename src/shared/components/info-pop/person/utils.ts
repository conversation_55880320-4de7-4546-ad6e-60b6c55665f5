import _ from 'lodash';

export const formatPersonOverview = (data) => {
  const nodeData: Record<string, any> = {
    id: data.id,
    name: data.name,
    image: data.image,
    ename: data.enterpriseName,
    eid: data.enterpriseKeyNo,
    jobTitle: data.jobTitle,
    tags: [],
    summary: [],
  };

  if (data.tags) {
    nodeData.tags = _.map(data.tags, (tag) => {
      return {
        type: 0,
        name: tag,
      };
    });
  }

  if (data.count) {
    nodeData.summary.push({
      label: '担任法人',
      count: data.count.operCount || 0,
    });

    nodeData.summary.push({
      label: '对外投资',
      count: data.count.partnerCount || 0,
    });

    nodeData.summary.push({
      label: '在外任职',
      count: data.count.employeeCount || 0,
    });

    nodeData.summary.push({
      label: '控股企业',
      count: data.count.nameCount || 0,
    });
  }

  return nodeData;
};
