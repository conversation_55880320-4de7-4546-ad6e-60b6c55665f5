.ntag.text-primary {
  color: #128bed;
  background: #e7f4ff;
}

.ntag.text-success {
  color: #094;
  background: #e4fff0;
}

.ntag.text-warning {
  color: #ff8900;
  background: #fff2de;
}

.ntag.text-danger {
  color: #f04040;
  background: #ffecec;
}

.ntag.text-list {
  color: #ec9662;
  background: #fff4ed;
}

.ntag.text-pl {
  color: #6f77d1;
  background: #edeef9;
}

.ntag.text-so {
  color: #3ca2c4;
  background: #e6f9ff;
}

.ntag.text-round {
  color: #e59e74;
  background: #fff3ee;
}

.ntag.text-gray {
  background: #f6f6f6;
}

.ntag:not(:last-child) {
  margin-right: 6px;
}

.ntag.text-brand-blue {
  color: #128bed;
  background: #e2f1fc;
}

.ntag.text-red {
  color: #f04040;
  background: #ffecec;
}

.ntag.text-green {
  color: #00ad65;
  background: #e3f6ee;
}

.ntag.text-orange {
  color: #ff8900;
  background: #ffeedb;
}

.ntag.text-blue-violet {
  color: #845fff;
  background: #f0ebff;
}

.ntag.text-cobalt-blue {
  color: #6171ff;
  background: #edeeff;
}

.ntag.text-tech-blue {
  color: #367dff;
  background: #e6efff;
}

.ntag.text-ming-qing {
  color: #00a3cc;
  background: #dff3f8;
}

.ntag.text-orange-red {
  color: #ff722d;
  background: #ffeee5;
}

.ntag.text-vip-gold {
  color: #bb833d;
  background: #f5ede3;
}

.ntag[data-toggle] {
  cursor: pointer;
}
