import { defineComponent, ref, computed, onMounted, PropType } from 'vue';
import { handleCompanyStatusTag } from '@/utils/handle-tag';
import styles from './company-tag.module.less';
import './nstatus.less';
import './ntag.less';

const CompanyTag = defineComponent({
  name: 'company-tag',
  props: {
    type: {
      type: Number,
      default: undefined,
    },
    text: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    className: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const style = ref('');
    const tooltip = ref('');

    const currentText = computed(() => {
      if (props.type === 622 && ['国有全资', '国有独资', '国有控股', '国有实际控制'].includes(props.text)) {
        return '国有企业';
      }
      return props.text;
    });

    const currentStyle = computed(() => {
      if (props.type === 903) {
        const { color } = handleCompanyStatusTag(props.text);
        return color;
      } else {
        return props.className;
      }
    });

    onMounted(() => {
      // 组件挂载后的逻辑
    });

    return {
      style,
      tooltip,
      currentText,
      currentStyle,
    };
  },
  render() {
    return (
      <span class={[styles.appTag, this.type === 903 ? 'nstatus' : 'ntag', this.currentStyle]} title={this.tooltip}>
        {this.currentText}
      </span>
    );
  },
});

export default CompanyTag;
