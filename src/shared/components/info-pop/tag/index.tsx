import { defineComponent, ref, onMounted } from 'vue';
import styles from './tag.module.less';
import { _settings } from './tag.config';

const AppTag = defineComponent({
  name: 'app-tag',
  props: {
    type: {
      type: [Number, String],
      default: undefined,
    },
    text: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const style = ref('');
    const tooltip = ref('');

    onMounted(() => {
      let tag1 = _settings[props.type];
      if (!tag1) {
        tag1 = _settings.default;
      }

      let tag2 = tag1.types[props.text];
      if (!tag2) {
        tag2 = tag1.types.default;
      }

      if (tag2) {
        style.value = tag2.style;
        tooltip.value = tag2.title || props.title;
      }
    });

    return {
      style,
      tooltip,
    };
  },
  render() {
    return (
      <span class={[styles.appTag, styles[this.style]]} title={this.tooltip}>
        {this.text}
      </span>
    );
  },
});

export default AppTag;
