import { defineComponent, getCurrentInstance, PropType } from 'vue';
import styles from './path.module.less';

const openTargetPersonForPK = (keyNo, name?, parentKeyNo?) => {
  if (keyNo && keyNo !== 'null' && keyNo !== 'undefined') {
    window.open(`/beneficaryDetail?personId=${keyNo}`);
  } else {
    window.open(`/beneficaryDetail?personName=${name}&companyId=${parentKeyNo}`);
  }
};

const openTargetCompany = (corpKeyNo, rootKeyNo?) => {
  window.open(`/companyDetail?keyNo=${corpKeyNo}${rootKeyNo ? '&rootKeyNo=' + rootKeyNo : ''}`);
};

const AppPath = defineComponent({
  name: 'app-path',
  props: {
    paths: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    category: {
      type: String,
      default: '',
    },
    keyNo: {
      type: String,
      default: '',
    },
    glf: {
      type: Boolean,
      default: false,
    },
    glf2: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const instance = getCurrentInstance();

    const isForbid = (path: any) => {
      return false;
      // return (window as any).__PLUGIN__ && (instance?.proxy as any)?.isForbidOurterLinkerFlag && path.Id && path.Id[0] !== 'p';
    };

    const eleTag = (path: any) => {
      return isForbid(path) ? 'span' : 'a';
    };

    const isZero = (reason: string) => {
      const rea = reason.replace('%', '');
      return +rea === 0;
    };

    const getCategoryName = () => {
      if (props.category === 'sh') {
        return `<span class="${styles.titleCategory} ${styles.tagBlue}">上交所</span>`;
      }
      if (props.category === 'sz') {
        return `<span class="${styles.titleCategory} ${styles.tagBlue}">深交所</span>`;
      }
      if (props.category === 'kj') {
        return `<span class="${styles.titleCategory} ${styles.tagBlue}">会计准则</span>`;
      }
      if (props.category === 'jr') {
        return `<span class="${styles.titleCategory} ${styles.tagBlue}">金融机构准则</span>`;
      }
      return '';
    };

    const showDetail = (path: any) => {
      if (isForbid(path)) {
        (window as any).onCompanyTextClick && (window as any).onCompanyTextClick(path.Name, path.Id);
        return;
      }

      let type = 1;
      if (path.Id && path.Id[0] === 'p') {
        type = 0;
      }

      if (type === 0) {
        openTargetPersonForPK(path.Id);
      } else {
        openTargetCompany(path.Id);
      }
    };

    return {
      eleTag,
      isZero,
      isForbid,
      getCategoryName,
      showDetail,
    };
  },
  render() {
    return (
      <div class={styles.ePath}>
        <div class={styles.ePathTitle}>
          <span class={styles.title}>关联方认定详情</span>
          <span class={styles.titleTag} domPropsInnerHTML={this.getCategoryName()} />
        </div>
        <div class={styles.ePathMx}>
          {this.paths.map((onePath, index) => (
            <div key={index} class={styles.ePathC}>
              {onePath.map((path: any, pathIndex: number) => (
                <span key={pathIndex}>
                  {path.Id ? (
                    <component is={this.eleTag(path)} onClick={() => this.showDetail(path)}>
                      {path.Name}
                    </component>
                  ) : (
                    <span>
                      <span class={styles.eLangArrow}>
                        {path.Operation && (
                          <span class={styles.eaText} style={this.glf || this.glf2 ? { color: '#128bed' } : {}}>
                            {path.Operation}
                          </span>
                        )}
                        {!this.isZero(path.Reason) && (
                          <span class={styles.eaText} style={this.glf || this.glf2 ? { color: '#128bed' } : {}}>
                            {path.Reason}
                          </span>
                        )}
                        <span class={styles.eaLine} />
                        {this.glf ? (
                          (this.paths[0].Id == this.keyNo && path.Direction == 'IN') ||
                          (this.paths[0].Id != this.keyNo && path.Direction == 'OUT') ? (
                            <i class={[styles.eaArrowWrap, styles.eaRight, styles.triangleRight]} />
                          ) : (
                            <i class={[styles.eaArrowWrap, styles.eaLeft, styles.triangleLeft]} />
                          )
                        ) : (this.paths[0].Id == this.keyNo && path.Direction == 'IN') ||
                          (this.paths[0].Id != this.keyNo && path.Direction == 'OUT') ? (
                          <i class={[styles.eaArrowWrap, styles.eaLeft, styles.triangleLeft]} />
                        ) : (
                          <i class={[styles.eaArrowWrap, styles.eaRight, styles.triangleRight]} />
                        )}
                      </span>
                    </span>
                  )}
                </span>
              ))}
            </div>
          ))}
        </div>
      </div>
    );
  },
});

export default AppPath;
