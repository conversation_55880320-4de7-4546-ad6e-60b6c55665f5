.e_path {
  display: inline-block;
  line-height: 28px;
  margin-bottom: 6px;
  width: 100%;

  .e_path-Mx {
    max-height: 140px;
    overflow-y: auto;
  }

  .e_path_title {
    display: flex;
    align-items: center;
    line-height: 1.2;

    .title {
      font-weight: bold;
    }

    .title-tag {
      margin-left: 4px;
    }
  }
}

.e_path .e_path-C {
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.e_path .e_path-C:last-child {
  border-bottom-color: transparent;
  padding-bottom: 0;
}

.e_path div.ea_path-title {
  color: #333;
  font-weight: 600;
  font-size: 12px;
}

.e_path a {
  word-break: break-word;
  color: #333;
  font-size: 12px;
}

.e_path a:hover {
  color: #128bed;
}

.e_path span.ea_arrow {
  display: inline-block;
  min-width: 84px;
  height: 26px;
  background-size: 75px 8px;
  padding-bottom: 10px;
  font-size: 12px;
  color: #999;
  text-align: center;
  position: relative;
  top: -7px;
  margin-right: 6px;
  margin-left: 6px;
}

.e_path span.ea_arrow.ea_left {
  background-size: 75px 8px;
}

.e_path span.ea_t5 {
  display: inline-block;
  width: 105px;
  height: 26px;
  background-size: 100px 34px;
  font-size: 12px;
  color: #128bed;
  text-align: center;
  position: relative;
  top: -9px;
  margin-right: 6px;
  padding-bottom: 40px;
}

.e_path .ea_path-wrap {
  max-height: 200px;
  overflow-y: auto;
  float: left;
}

.e_path .ea_path-wrap > div {
  float: left;
  overflow: hidden;
}

/* 路径长箭头 */
.e_lang-arrow {
  position: relative;
  top: 10px;
  border: red 0 solid;
  display: inline-block;
  height: 32px;
  padding: 0 5px;
  margin-left: 10px;
  margin-right: 10px;
  max-width: 160px;
}

.ea_text {
  position: relative;
  display: inline-block;
  height: 12px;
  line-height: 12px;
  color: #999;
  text-align: center;
  font-size: 12px;
  width: 100%;
  margin-bottom: 7px;
  float: left;
  max-width: 150px;
  word-break: keep-all;
  text-overflow: ellipsis;
  overflow: hidden;
}

.ea_line {
  position: absolute;
  width: 100%;
  height: 1px;
  background-color: #ddd;
  top: 15px;
  left: 0;
}

.ea_arrow-wrap {
  position: absolute;
  top: 8px;
  font-size: 15px;
  color: #999;
  width: 15px;
  height: 100%;
  z-index: 99;
}

.ea_arrow-wrap.ea_left {
  left: -5px;
  top: 10px;
}

.ea_arrow-wrap.ea_right {
  right: -6px;
  top: 11px;
}

.triangle-left {
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-right: 11px solid #d6d6d6;
  border-bottom: 5px solid transparent;
}

.triangle-right {
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-left: 11px solid #d6d6d6;
  border-bottom: 5px solid transparent;
}
