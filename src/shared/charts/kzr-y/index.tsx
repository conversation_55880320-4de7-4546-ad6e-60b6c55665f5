import { defineComponent, nextTick, onMounted, PropType, reactive, ref } from 'vue';
import _, { isEmpty } from 'lodash';
import { NodeType } from '@/utils/yfiles/lib/node-type';
import { clearHighlights, handleHoverClass } from '@/utils/yfiles/lib/NodeHighlightManager';
import { getNodeSize } from '@/utils/yfiles/templates/helper';
import { buildGraph, createLayoutConfig, createLayoutData } from './graph';
import '@/utils/yfiles/license-loader/license-loader';
import { createCompanyCombinedTemplate } from '@/utils/yfiles/templates/node-combined-style-template';
import { GraphItemTypes, Insets, ScrollBarVisibility } from 'yfiles';
import styles from './kzr-y.module.less';
import { Slider } from 'ant-design-vue';
import QIcon from '@/components/global/q-icon';
import { useFullscreen } from '@vueuse/core';
import { AppCompany, AppPerson } from '@/shared/components/info-pop';
import AppKzrSubtext from './widgets/app-kzr-subtext';
import { isFakeKeyNo } from '@/utils';
import { usePopover } from '../equity-chain/utils/popover';
import { useSaveImg } from '../equity-chain/utils/save-img';
import moment from 'moment';

const chartData: { nodes: any[]; edges: any[] } = {
  nodes: [],
  edges: [],
};

export interface KzrYProps {
  containerId?: string;
  iframe?: boolean;
  typeMini?: boolean;
  finishedDelayTime?: number;
  aKeyNo?: string;
  aName?: string;
  hideOperationIcons?: boolean;
  lazyInit?: boolean;
}

const KzrY = defineComponent({
  name: 'KzrY',
  props: {
    containerId: {
      type: String,
      default: 'beneficiaryGraph',
    },
    iframe: {
      type: Boolean,
      default: false,
    },
    typeMini: {
      type: Boolean,
      default: false,
    },
    finishedDelayTime: {
      type: Number,
      default: 0,
    },
    companyInfo: {
      type: Object as PropType<{ name: string; keyNo: string }>,
      default: () => ({}),
    },
    hideOperationIcons: {
      type: Boolean,
      default: false,
    },
    lazyInit: {
      type: Boolean,
      default: false,
    },
    paths: {
      type: Object as PropType<{ nodes: any; links: any }>,
      required: false,
    },
  },
  setup(props, { emit }) {
    const DEFAULT_SCALE = 100;
    const containerRef = ref<HTMLElement | null>(null);

    const graphComponent = ref<any>(null);
    const dataSource = ref<any>(null);
    const isInit = ref(false);
    const noData = ref(false);
    const scale = ref<number>(DEFAULT_SCALE);
    const showKzrExtra = ref(false);
    const isListed = ref(false);
    const controllerData = ref<any>(null);
    const actualControl = ref<any>(null);
    const hasFinalAc = ref(false);
    const acListForTips = ref<any[]>([]);
    const mergedActualControl = ref<any>(null);
    const hoverData = ref<any>(null);

    /**
     * 全屏显示控制
     */
    const { isFullscreen, toggle } = useFullscreen(containerRef);

    const { exportImage, saveImgElementAsPng } = useSaveImg(graphComponent, props.companyInfo.name);

    const { load: showPopover, move: movePopover, unload: hidePopover } = usePopover(containerRef);

    const clearHover = () => {
      hidePopover();
    };

    const loadData = (data?: any): Promise<any> => {
      if (data) {
        return Promise.resolve(data);
      }
      return Promise.resolve(null);
      // return loadActualControllerData(props.companyInfo.keyNo, props.companyInfo.name, '', true);
    };

    const initData = (data?: any) => {
      // return new Promise((resolve, reject) => {
      //   isInit.value = false;
      //   noData.value = false;
      //   loadData(d)
      //     .then((data) => {
            isListed.value = data.isListed || false;
            controllerData.value = data.controllerData;
            actualControl.value = data.actualControl;
            acListForTips.value = data.acListForTips;
            hasFinalAc.value = data.hasFinalAc;
            dataSource.value = data;
            mergedActualControl.value = data.mergedActualControl;
            showKzrExtra.value = true;
            // emit('loadDataSuccess', {
            //   companyName: companyName.value,
            //   companyKeyNo: keyNo.value,
            // });
          //   resolve();
          // })
          // .catch((err) => {
          //   isInit.value = true;
          //   noData.value = true;
          //   reject(err);
          // });
      // });
    };

    const doInitData = (data?: any) => {
      initData(data);
      initializeGraphComponent();
    };

    const cleanup = () => {
      isInit.value = false;
      reset();
      hidePopover();
    };

    const reset = () => {
      if (graphComponent.value) {
        graphComponent.value.morphLayout({
          targetBoundsInsets: Insets.from([0, 0, 0, 0]),
          layout: createLayoutConfig(),
          layoutData: createLayoutData(graphComponent.value),
        });
        clearHighlights(graphComponent.value);
      }
    };

    const zoomIn = () => {
      if (graphComponent.value) {
        const newZoom = graphComponent.value.zoom + 0.2;
        graphComponent.value.zoom = Math.max(newZoom, 0.2);
      }
    };

    const zoomOut = () => {
      if (graphComponent.value) {
        graphComponent.value.zoom -= 0.2;
      }
    };

    const zoomScale = ({ scale: newScale }: { scale: number }) => {
      if (newScale && graphComponent.value?.zoom) {
        graphComponent.value.zoom = newScale / 100;
      }
    };

    const saveImage = async () => {
      console.log('🚀 ~ saveImage ~ graphComponent.value:', graphComponent.value);
      const dataPng = await exportImage(graphComponent.value, 2);
      saveImgElementAsPng(dataPng, `${props.companyInfo.name}-实际控制人图谱-${moment().format('YYYY-MM-DD')}.png`, { scale: 2 });
    };

    const handleNodeHover = ({ event }: { event: any }) => {
      const hoverItem = event.item;

      if (!hoverItem) {
        clearHover();
        return;
      }

      /** 置灰的节点不展示popover */
      if (hoverItem?.style?.cssClass === 'gray20') {
        return;
      }

      const data = dataSource.value?.nodes.find((item) => item.keyNo === hoverItem?.tag?.unique);

      if (!data) {
        clearHover();
        return;
      }

      hoverData.value = data;

      const size = null;
      if (!data?.keyNo || data.org < 0) {
        // -1 无效企业 -2无效的人
        /** combined类型，全部存在keyNo，不展示复制 */
        let combinedAllNoKey = false;
        if (hoverItem?.tag?.combinedNameList?.length) {
          const noKeyNo = hoverItem?.tag?.combinedNameList?.find((i: any) => !i.keyNo);
          combinedAllNoKey = !noKeyNo;
        }
        if (combinedAllNoKey) {
          clearHover();
          return;
        }

        // popoverHelper.showDetailCard({
        //   component: AppCopy,
        //   data: {
        //     text: data.name,
        //     isVertical: false,
        //   },
        //   direction: 'bottom',
        //   directionX: 'left',
        //   size,
        //   container: $(`#${props.containerId}`),
        //   identity: `detail-popover-${data.nodeId}`,
        //   callback: moveCardPosition(event.item, 'copy'),
        //   isYFile: true,
        // });

        return;
      }

      if (isFakeKeyNo(data.keyNo)) {
        return;
      }

      const popoverComp = data.keyNo && data.keyNo[0] === 'p' ? AppPerson : AppCompany;

      showPopover(popoverComp, {
        data: {
          hasKeyNo: true,
          id: data.keyNo,
          name: data.name,
          keyNo: data.keyNo,
          rsTags: data.rsTags,
          eid: props.companyInfo.keyNo,
          ename: props.companyInfo.name,
          org: data.org,
          hideEmptyDataInPath: true,
        },
        // container: $(`#${props.containerId}`).parent(),
        // size,
        // identity: `detail-${data.nodeId}`,
        // isYFile: true,
      });
      movePopover({
        nodeItem: hoverItem,
        graphComponent: graphComponent.value,
      });
    };

    const initializeGraphComponent = async () => {
      const { graphComponent: gc, graphBuilder, nodeSource, edgeSource, graphInputMode } = buildGraph(`${props.containerId}`);

      gc.maximumZoom = 2.5;
      gc.minimumZoom = 0.4;

      gc.focusIndicatorManager.enabled = false;
      gc.selectionIndicatorManager.enabled = false;

      if (isEmpty(dataSource.value)) return;

      chartData.nodes = [];
      chartData.edges = [];

      // 数据处理
      dataSource.value.nodes.forEach((node) => {
        let combinedHeight = 0;

        const personList = node.listedAc?.map((x: any) => ({ name: x.Name, keyNo: x.KeyNo })) || [];
        if (personList.length) {
          const template = createCompanyCombinedTemplate(personList);
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = template;
          tempDiv.classList.add('temp-measure');
          document.body.appendChild(tempDiv);
          combinedHeight = document.querySelector('.temp-measure')?.clientHeight || 0;
          document.body.removeChild(tempDiv);
        }

        const areaTag = node.tags?.length
          ? {
              name: node.tags[0].name,
            }
          : { name: '' };
        const nodeType = node.listedAc?.length > 1 ? NodeType.combined : _.startsWith(node.keyNo, 'p') ? NodeType.person : NodeType.company;
        const { isOversize, layoutSize, nodeSize, labelSize, tagSize } = getNodeSize({
          nodeType,
          text: node.name,
          labels: node.rsTags,
          tag: areaTag.name,
          fixedHeight: combinedHeight,
          isBold: node.isRoot,
        });

        const preparedNode = {
          id: node.nodeId,
          name: node.name,
          unique: node.keyNo,
          isKzr: node.kzr,
          isSelf: node.isRoot,
          type: nodeType,
          areaTag,
          tipLabels: node?.rsTags,
          imageUrl: node.image ? node.image : '',
          isOversize,
          combinedNameList: personList,
          combinedHeight,
          layoutSize,
          nodeSize,
          labelSize,
          tagSize,
        };

        chartData.nodes.push(preparedNode);
      });

      dataSource.value.links.forEach((link) => {
        chartData.edges.push({
          id: link.linkId,
          sourceId: link.sourceNode?.nodeId,
          targetId: link.targetNode?.nodeId,
          lineType: link.lineType,
          lineText: link.lineText,
          isMain: link.isRed,
          tips: link.tips,
        });
      });

      graphBuilder.setData(nodeSource as any, chartData.nodes);
      graphBuilder.setData(edgeSource as any, chartData.edges);

      gc.fitGraphBounds();

      gc.addZoomChangedListener(() => {
        const value = Number(gc.zoom?.toFixed(2));
        scale.value = Number((value * 100).toFixed(0));
      });

      gc.verticalScrollBarPolicy = ScrollBarVisibility.NEVER;
      gc.horizontalScrollBarPolicy = ScrollBarVisibility.NEVER;
      gc.addMouseDragListener((_sender: any, evt: any) => {
        clearHover();
      });

      graphBuilder.updateGraph();

      gc.fitGraphBounds();
      // 更新布局
      await gc.morphLayout({
        targetBoundsInsets: Insets.from([0, 0, 0, 0]),
        layout: createLayoutConfig(),
        layoutData: createLayoutData(gc),
        morphDuration: '0.3s',
      });

      graphInputMode.addItemClickedListener((_sender: any, evt: any) => {
        const tag = evt.item.tag;
        if (tag?.unique) {
          const entity = {
            id: tag.unique,
            eid: tag.unique,
            ename: tag.name,
            org: '',
            pathData: null,
          };
          emit('showDetail', { data: entity });
        }
      });

      graphInputMode.addCanvasClickedListener((_sender: any, evt: any) => {
        emit('clickEmpty');
      });

      graphInputMode.itemHoverInputMode.hoverItems = GraphItemTypes.NODE;
      // 定义一个变量来保存定时器的ID
      let timerId: number | null = null;
      !props.typeMini &&
        graphInputMode.itemHoverInputMode.addHoveredItemChangedListener((_sender: any, evt: any) => {
          /** 处理hover效果 */
          handleHoverClass(evt, graphComponent.value);

          if (evt.item) {
            // 如果鼠标悬停在节点上，则设置一个定时器，在200毫秒后触发
            timerId = setTimeout(() => {
              handleNodeHover({ event: evt });
              if (timerId) {
                clearTimeout(timerId);
                timerId = null;
              }
            }, 200);
          } else {
            // 如果鼠标离开节点，则清除定时器
            if (timerId) {
              clearTimeout(timerId);
              timerId = null;
            }
            clearHover();
          }
        });

      console.log('🚀 ~ initializeGraphComponent ~ gc:', gc);
      graphComponent.value = gc;
    };

    onMounted(() => {
      doInitData(props.paths);
    });

    return {
      containerRef,
      isInit,
      noData,
      scale,
      showKzrExtra,
      isListed,
      controllerData,
      actualControl,
      hasFinalAc,
      acListForTips,
      mergedActualControl,
      isFullscreen,
      toggle,
      doInitData,
      cleanup,
      reset,
      zoomIn,
      zoomOut,
      zoomScale,
      saveImage,
      clearHover,
      handleNodeHover,
      initializeGraphComponent,
    };
  },
  render() {
    return (
      <div class={styles.container} ref="containerRef">
        <div class={styles.content}>
          {/* 图谱 */}
          <div id={this.containerId} class={styles.chart}></div>
          {/* 工具栏 */}
          <div class={styles.toolbox}>
            {/* 缩放 */}
            <Slider class={styles.slider} vertical v-model={this.scale} max={250} min={40} onAfterChange={this.zoomScale} />
            <ul class={styles.btnGroup}>
              {/* 刷新 */}
              <li class={styles.btnItem} onClick={this.reset}>
                <i class={styles.icon}>
                  <QIcon type="icon-icon_sqq" />
                </i>
                <span class={styles.label}>刷新</span>
              </li>
              {/* 全屏 */}
              <li class={styles.btnItem} onClick={this.toggle}>
                <i class={styles.icon}>
                  <QIcon type={this.isFullscreen ? 'icon-tuichu' : 'icon-quanping1'} />
                </i>
                <span class={styles.label}>{this.isFullscreen ? '退出' : '全屏'}</span>
              </li>
              {/* 保存 */}
              <li class={styles.btnItem} onClick={this.saveImage}>
                <i class={styles.icon}>
                  <QIcon type="icon-xiazai" />
                </i>
                <span class={styles.label}>保存</span>
              </li>
            </ul>
          </div>
        </div>
        {/* 顶部信息 */}
        {!this.typeMini && this.showKzrExtra && (
          <AppKzrSubtext
            isListed={this.isListed}
            iframe={this.iframe}
            hasFinalAc={this.hasFinalAc}
            acListForTips={this.acListForTips}
            controllerData={this.controllerData}
            actualControl={this.actualControl}
            mergedActualControl={this.mergedActualControl}
            isFullScreen={this.isFullscreen}
          />
        )}
      </div>
    );
  },
});

export default KzrY;
