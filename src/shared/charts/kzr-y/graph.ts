/* eslint-disable no-bitwise */

import { createEdgeLabelStyle, createEdgeStyle } from '@/utils/yfiles/lib/edge-style';
import { nodeCompanyLayout, nodePersonLayout, nodePersonOverSizeLayout } from '@/utils/yfiles/lib/node-layout';
import { personNodeSize } from '@/utils/yfiles/lib/node-size';
import { createNodeCompanyLabel, createNodeCompanyStyle } from '@/utils/yfiles/lib/node-style';
import { LineType, NodeType } from '@/utils/yfiles/lib/node-type';
import { createTooltipContent } from '@/utils/yfiles/templates/helper';
import { NodeCombinedStyleTemplate } from '@/utils/yfiles/templates/node-combined-style-template';
import { EdgeLabelStyle } from '@/utils/yfiles/templates/edge-label-style';
import { PersonNodeOversize } from '@/utils/yfiles/templates/person-node-oversize';
import { PersonNodeTopTip } from '@/utils/yfiles/templates/person-node-top-tip';
import { ZOrderSupport } from '@/utils/yfiles/lib/ZOrderSupport';
import { PointsForCircle } from '@/utils/yfiles/lib/PortCalculator';
import { NodeRightTopTagStyle } from '@/utils/yfiles/templates/node-right-top-tag-style-template';
import {
  clearHighlights,
  disHighlightEdge,
  disHighlightNode,
  getHighlightEdges,
  highlightEdges,
} from '@/utils/yfiles/lib/NodeHighlightManager';

import {
  BridgeCrossingPolicy,
  BridgeManager,
  GraphBuilder,
  GraphComponent,
  GraphEditorInputMode,
  GraphItemTypes,
  GraphObstacleProvider,
  HierarchicLayout,
  HierarchicLayoutData,
  HierarchicLayoutEdgeLayoutDescriptor,
  HierarchicLayoutEdgeRoutingStyle,
  ICollection,
  INode,
  LabelAngleReferences,
  LayoutOrientation,
  List,
  MoveInputMode,
  MoveViewportInputMode,
  Point,
  PortCandidate,
  PortDirections,
  PortCandidateSet,
  PreferredPlacementDescriptor,
  Font,
} from 'yfiles';

const CONTAINER_SELECTOR = '#beneficiaryGraph';

export const buildGraph = (selector) => {
  const graphComponent = new GraphComponent(selector);
  const graph = graphComponent.graph;
  const graphBuilder = new GraphBuilder(graph);

  const nodeSource = graphBuilder.createNodesSource({
    data: [],
    id: (d: any) => d.id,
  });

  nodeSource.nodeCreator.layoutProvider = (d: any) => {
    if (d.type === NodeType.combined) {
      return nodeCompanyLayout({
        graphComponent,
        layoutSize: d.layoutSize,
      });
    }
    if (d.type === NodeType.person) {
      if (d.isOversize) {
        return nodePersonOverSizeLayout({ layoutSize: d.layoutSize, graphComponent });
      }
      return nodePersonLayout({ graphComponent, layoutSize: d.layoutSize });
    }

    return nodeCompanyLayout({ graphComponent, layoutSize: d.layoutSize });
  };

  nodeSource.nodeCreator.styleProvider = (d) => {
    if (d.type === NodeType.combined) {
      return new NodeCombinedStyleTemplate();
    }
    if (d.isSelf) {
      return createNodeCompanyStyle({ fill: '#128BED' });
    }
    if (d.type === NodeType.company) {
      if (d.isKzr || d.areaTag.name) {
        return new NodeRightTopTagStyle();
      }
      return createNodeCompanyStyle();
    }

    if (d.isOversize) {
      return new PersonNodeOversize();
    }

    return new PersonNodeTopTip();
  };

  const nodeLabelBinding = nodeSource.nodeCreator.createLabelBinding((d) => {
    if (d.type === NodeType.person) {
      return null;
    }
    if (d.type === NodeType.combined) {
      return null;
    }
    if (d.type === NodeType.company && d.areaTag?.name) {
      return null;
    }
    if (d.isKzr) {
      return null;
    }

    return d.name;
  });

  nodeLabelBinding.styleProvider = (d) => {
    if (d.type === NodeType.combined) {
      return null;
    }

    if (d.isSelf) {
      return createNodeCompanyLabel({
        textFill: '#FFFFFF',
        ownerFill: '#128BED',
        font: new Font({ fontWeight: 'bold', fontSize: 12 }),
      });
    }
    return createNodeCompanyLabel();
  };

  const edgeSource = graphBuilder.createEdgesSource({
    data: [],
    id: (d: any) => d.id,
    sourceId: 'sourceId',
    targetId: 'targetId',
  });

  edgeSource.edgeCreator.styleProvider = (d) => {
    if (d.isMain) {
      return createEdgeStyle({
        fillColor: '#FF6060',
        arrowColor: '#FF6060',
        isDashed: d.lineType === LineType.dashed,
      });
    }
    return createEdgeStyle({ isDashed: d.lineType === LineType.dashed });
  };

  const edgeLabelBinding = edgeSource.edgeCreator.createLabelBinding((d) => d.lineText);
  edgeLabelBinding.styleProvider = (d) => {
    if (d.tips) {
      return new EdgeLabelStyle({ tips: d.tips });
    }
    if (d.isMain) {
      return createEdgeLabelStyle({ fillColor: '#FF6060' });
    }
    return createEdgeLabelStyle();
  };

  // 障碍物
  createBridgeManager(graphComponent);

  // 交互模式
  const graphInputMode = createGraphInputMode(graphComponent);
  graphComponent.inputMode = graphInputMode;

  const zOrderSupport = new ZOrderSupport(graphComponent);
  const onDragStartedEventHandler = (sender, evt) => {
    if (sender instanceof MoveInputMode) {
      const node = sender.affectedItems.filter((o) => o instanceof INode)?.at(0);
      if (node) {
        zOrderSupport.toFront(List.fromArray([node]));
      }
    }
  };
  graphInputMode.moveInputMode.addDragStartedListener(onDragStartedEventHandler);
  graphInputMode.moveUnselectedInputMode.addDragStartedListener(onDragStartedEventHandler);

  // 高亮
  createHighlightIndicatorManager(graphComponent, graphInputMode);

  graphBuilder.buildGraph();

  return { graphComponent, graphBuilder, nodeSource, edgeSource, graphInputMode };
};

/** 配置高亮 */
const createHighlightIndicatorManager = (graphComponent, graphInputMode) => {
  graphInputMode.addItemClickedListener((sender, evt) => {
    if (evt?.item?.owner?.tag?.tips) {
      const mouseHoverInputMode = graphInputMode.mouseHoverInputMode;
      mouseHoverInputMode.show(evt.location);
      return;
    }
    const item = evt.item;
    if (!item || !(item instanceof INode)) {
      return;
    }
    clearHighlights(graphComponent);

    const highlightedEdges = getHighlightEdges(item, graphComponent);
    // 高亮关联的线
    highlightEdges(highlightedEdges, graphComponent);

    let highlightNodes;

    if (item.tag.isSelf) {
      highlightNodes = new Set([item]);
    } else {
      highlightNodes = new Set([
        ...highlightedEdges.map((edge) => {
          return edge.sourceNode;
        }),
        ...highlightedEdges.map((edge) => {
          return edge.targetNode;
        }),
      ]);
    }

    const notHighlightedEdges = graphComponent.graph.edges.filter((edge) => !highlightedEdges.includes(edge));
    const notHighlightedNodes = graphComponent.graph.nodes.filter((node) => !highlightNodes.has(node));

    disHighlightNode(notHighlightedNodes, graphComponent);
    disHighlightEdge(notHighlightedEdges, graphComponent);

    graphComponent.graph.invalidateDisplays();
  });
};

/** 配置绕过障碍物 */
const createBridgeManager = (graphComponent) => {
  const bridgeManager = new BridgeManager({
    canvasComponent: graphComponent,
    considerCurves: true,
    bridgeCrossingPolicy: BridgeCrossingPolicy.HORIZONTAL_BRIDGES_VERTICAL,
  });
  bridgeManager.addObstacleProvider(new GraphObstacleProvider());
};

/** 配置图交互模式 */
export const createGraphInputMode = (graphComponent) => {
  const graphInputMode = new GraphEditorInputMode();
  // 允许节点拖拽
  graphInputMode.moveInputMode.enabled = true;

  /** 禁止在现有边上的交互式弯曲创建 */
  graphInputMode.createBendInputMode.enabled = false;

  graphInputMode.clickableItems = GraphItemTypes.NODE | GraphItemTypes.NODE_LABEL | GraphItemTypes.EDGE_LABEL;
  graphInputMode.selectableItems = GraphItemTypes.NODE;
  graphInputMode.movableItems = GraphItemTypes.NODE;
  // 禁止调整节点大小
  graphInputMode.handleInputMode.enabled = false;

  graphInputMode.showHandleItems = graphInputMode.showHandleItems & ~GraphItemTypes.NODE;

  graphInputMode.moveInputMode = new MoveInputMode({
    enabled: true,
    priority: 37,
  });

  graphInputMode.moveViewportInputMode = new MoveViewportInputMode({
    enabled: true,
    priority: 43,
  });

  graphComponent.inputMode?.selectionToFront();

  graphInputMode.toolTipItems = GraphItemTypes.EDGE_LABEL;

  const mouseHoverInputMode = graphInputMode.mouseHoverInputMode;
  mouseHoverInputMode.toolTipLocationOffset = new Point(15, -15);

  /** 因为全屏会把画布层级变最高导致tooltip全屏的时候看不见，所以tooltip需要放在画布里面，而不是默认的body */
  mouseHoverInputMode.toolTipParentElement = $(CONTAINER_SELECTOR)[0];

  graphInputMode.itemHoverInputMode.enabled = true;
  graphInputMode.itemHoverInputMode.hoverItems = GraphItemTypes.NODE | GraphItemTypes.NODE_LABEL;
  graphInputMode.itemHoverInputMode.discardInvalidItems = false;

  graphInputMode.moveUnselectedInputMode = new MoveInputMode({ enabled: true });
  // 允许图形拖动
  graphInputMode.moveViewportInputMode.enabled = true;
  graphInputMode.allowCreateNode = false;
  graphInputMode.allowCreateEdge = false;
  graphInputMode.allowEditLabel = false;
  graphInputMode.allowAddLabel = false;

  graphInputMode.addCanvasClickedListener((_, evt) => {
    clearHighlights(graphComponent);
  });

  graphInputMode.addQueryItemToolTipListener((src, eventArgs: any) => {
    if (eventArgs.handled) {
      return;
    }
    /** 移除所有tooltip */
    $(CONTAINER_SELECTOR).find('.qcc-graph-tooltip-container').remove();
    if (eventArgs?.item?.owner?.tag?.tips) {
      const toolTip = createTooltipContent(eventArgs.item);
      eventArgs.toolTip = toolTip;
      eventArgs.handled = true;
    }
  });

  return graphInputMode;
};

/** 配置布局 */
export const createLayoutConfig = () => {
  const hierarchicLayoutEdgeLayoutDescriptor = new HierarchicLayoutEdgeLayoutDescriptor();
  // 将边的风格改为方正的边
  hierarchicLayoutEdgeLayoutDescriptor.routingStyle.defaultEdgeRoutingStyle = HierarchicLayoutEdgeRoutingStyle.ORTHOGONAL;
  // 设置目标节点到边拐角的最小间距
  hierarchicLayoutEdgeLayoutDescriptor.minimumLastSegmentLength = 20;
  hierarchicLayoutEdgeLayoutDescriptor.sourcePortOptimization = true;
  return new HierarchicLayout({
    minimumLayerDistance: 50,
    edgeLayoutDescriptor: hierarchicLayoutEdgeLayoutDescriptor,
    integratedEdgeLabeling: true,
    orthogonalRouting: true,
    layoutOrientation: LayoutOrientation.TOP_TO_BOTTOM,
  });
};

/** 配置布局数据 */
export const createLayoutData = (graphComponent) => {
  const graph = graphComponent.graph;

  const layoutData = new HierarchicLayoutData();

  const northCandidate = PortCandidate.createCandidate(PortDirections.NORTH);
  // const southCandidate = PortCandidate.createCandidate(PortDirections.SOUTH, 1)
  layoutData.nodePortCandidateSets = (node) => {
    if (node.tag.type === NodeType.person) {
      const portCandidateSet = new PortCandidateSet();

      // bottom
      const southDegree = graph.outDegree(node);
      if (southDegree > 0) {
        // 获取 node 的宽和高
        const { height } = node.layout.toRect();
        const points = PointsForCircle.atBottom(southDegree, personNodeSize.height / 2, 20);

        for (const point of points) {
          portCandidateSet.add(
            PortCandidate.createCandidate(point.x, point.y + (height - personNodeSize.height) / 2, PortDirections.SOUTH, 0),
            1
          );
        }
      }
      return portCandidateSet;
    }
    return null;
  };

  // 将出去的边控制在一个点上
  // layoutData.sourceGroupIds = (edge) => `s-${edge.sourceNode?.tag.id}`
  // 将出去的边，控制在底部
  // layoutData.sourcePortCandidates = edge => ICollection.from([southCandidate])
  // 将进来的边，控制在顶部
  layoutData.targetPortCandidates = (edge) => ICollection.from([northCandidate]);
  // layoutData.targetGroupIds = (edge) => `t-${edge.targetNode?.tag.id}`
  layoutData.edgeLabelPreferredPlacement = new PreferredPlacementDescriptor({
    angle: Math.PI / 2,
    angleReference: LabelAngleReferences.RELATIVE_TO_EDGE_FLOW,
  });

  // 将实控人节点控制在顶层对齐
  for (const node of graph.nodes) {
    if (node.tag?.isKzr) {
      layoutData.layerConstraints.placeAtTop(node);
    }
  }

  return layoutData;
};
