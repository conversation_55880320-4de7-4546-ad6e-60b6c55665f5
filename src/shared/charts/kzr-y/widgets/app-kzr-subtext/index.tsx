import { defineComponent, computed, PropType } from 'vue';
import _ from 'lodash';
import styles from './app-kzr-subtext.module.less';
import QEntityLink from '@/components/global/q-entity-link';
import QGlossaryInfo from '@/components/global/q-glossary-info';
import QIcon from '@/components/global/q-icon';
import { Tooltip } from 'ant-design-vue';

export interface ControllerData {
  KeyNo?: string;
  Name?: string;
  ControlPercent?: string;
  Job?: string;
  showControlPercent?: boolean;
}

export interface ActualControl {
  PersonList?: Array<{
    KeyNo: string;
    Name: string;
  }>;
  ControlPercent?: string;
}

export interface AppKzrSubtextNProps {
  iframe?: boolean;
  controllerData?: ControllerData | ControllerData[];
  finalActualControl?: ActualControl | ActualControl[];
  actualControl?: ActualControl;
  isFullScreen?: boolean;
  isListed?: boolean;
  hasFinalAc?: boolean;
  acListForTips?: Array<{
    KeyNo: string;
    Name: string;
  }>;
  mergedActualControl?: ActualControl;
}

const AppKzrSubtextN = defineComponent({
  name: 'AppKzrSubtextN',
  props: {
    iframe: {
      type: Boolean,
      default: false,
    },
    controllerData: {
      type: [Object, Array] as PropType<ControllerData | ControllerData[]>,
      default: () => ({}),
    },
    finalActualControl: {
      type: [Object, Array] as PropType<ActualControl | ActualControl[]>,
      default: null,
    },
    actualControl: {
      type: Object as PropType<ActualControl>,
      default: null,
    },
    isFullScreen: {
      type: Boolean,
      default: false,
    },
    isListed: {
      type: Boolean,
      default: false,
    },
    hasFinalAc: {
      type: Boolean,
      default: false,
    },
    acListForTips: {
      type: Array as PropType<Array<{ KeyNo: string; Name: string }>>,
      default: () => [],
    },
    mergedActualControl: {
      type: Object as PropType<ActualControl>,
      default: () => ({}),
    },
  },
  setup(props) {
    /** 是否多实控人 顶点不合并 */
    const isMultiController = computed(() => {
      return _.isArray(props.controllerData) && (props.controllerData as ControllerData[])?.length;
    });

    const calcShowMore = ({ list }: { list: ControllerData[] }): boolean => {
      const nameList = _.map(list, (nList) => nList.Name);
      const nameStr = nameList.join('、');
      return nameStr.length > (list.length > 1 ? 33 : 35);
    };

    const getCompanyOrPersonList = (list: any[]) => {
      list &&
        list.forEach((l) => {
          if (l.KeyNo && l.KeyNo.indexOf('?') >= 0) {
            l.KeyNo = '';
          }
          l.Name = l.Name || '?';
        });
      return (window as any).$util.getCompanyOrPersonList(list, true, null, null, { noTitle: true });
    };

    return {
      isMultiController,
      calcShowMore,
      getCompanyOrPersonList,
    };
  },
  render() {
    const containerClass = [
      styles.container,
      {
        [styles.iframe]: this.iframe,
        [styles.isFull]: this.isFullScreen,
      },
    ];

    return (
      <div class={containerClass}>
        {/* 实际控制人（公示信息） */}
        {this.actualControl && Object.keys(this.actualControl).length ? (
          <div class={styles.kzr}>
            <span>
              实际控制人
              <QGlossaryInfo infoId="624" placement="bottom" maxWidth="400px" parentPopupContainer appendToBody={!this.isFullScreen} />
              {!this.hasFinalAc && '（公示信息）'}：
              <span v-if={this.actualControl.PersonList} class={styles.name}>
                {this.actualControl.PersonList?.map((v, index) => (
                  <span key={index}>
                    {index !== 0 && '、'}
                    <QEntityLink coyObj={{ KeyNo: v.KeyNo, Name: v.Name }} />
                  </span>
                ))}
              </span>
              {!this.actualControl.PersonList && <span>?</span>}
              {this.hasFinalAc && !this.isFullScreen ? (
                <Tooltip maxWidth={400}>
                  <div slot="title">
                    <span>企业最新公告公示的实际控制人为：</span>
                    <QEntityLink coyArr={this.acListForTips} />
                    <span>
                      ，而目前企查查平台所示"实际控制人"是基于中国证监会关于"实际控制人应当披露到自然人、国有资产管理机构、集体组织"等相关规定，结合公示实际控制人继续穿透形成的数据。
                    </span>
                  </div>
                  <span class="ntag text-primary click m-l-sm">
                    {/* TODO 补充icon */}
                    <QIcon type="icon-tuisong" class="text-primary m-r-space size12" />
                    公示信息
                  </span>
                </Tooltip>
              ) : null}
            </span>
            {this.actualControl.ControlPercent && (
              <span class={styles.mLLg}>
                表决权
                {this.actualControl.PersonList && this.actualControl.PersonList.length > 1 && '(共同持有)'}：
                <span class="text-danger-no-bg">{this.actualControl.ControlPercent + '%'}</span>
              </span>
            )}
          </div>
        ) : this.mergedActualControl && Object.keys(this.mergedActualControl).length ? (
          /* 合并的实际控制人（大数据分析） */
          <div class={styles.kzr}>
            <span>
              实际控制人
              <QGlossaryInfo
                infoId={this.isMultiController ? '778' : '624'}
                placement="bottom"
                maxWidth="400"
                appendToBody={!this.isFullScreen}
                parentPopupContainer
              />
              （大数据分析）：
              <span v-if={this.mergedActualControl.PersonList && this.mergedActualControl.PersonList.length} class={styles.name}>
                {this.mergedActualControl.PersonList?.map((v, index) => (
                  <span key={index}>
                    {index !== 0 && '、'}
                    <QEntityLink coyObj={{ KeyNo: v.KeyNo, Name: v.Name }} />
                  </span>
                ))}
              </span>
              {(!this.mergedActualControl.PersonList || !this.mergedActualControl.PersonList.length) && <span>?</span>}
            </span>
            {this.mergedActualControl.ControlPercent && (
              <span class={styles.mLLg}>
                表决权
                {this.mergedActualControl.PersonList && this.mergedActualControl.PersonList.length > 1 && '(共同持有)'}：
                <span class="text-danger-no-bg">{this.mergedActualControl.ControlPercent}</span>
              </span>
            )}
          </div>
        ) : this.controllerData && Object.keys(this.controllerData).length ? (
          /* 控制人数据（大数据分析） */
          <div class={styles.kzr}>
            <span>
              <span>
                实际控制人
                <QGlossaryInfo infoId="624" placement="bottom" maxWidth="400px" parentPopupContainer appendToBody={!this.isFullScreen} />
                （大数据分析）：
                {this.calcShowMore({ list: [this.controllerData as ControllerData] }) && !this.isFullScreen ? (
                  <Tooltip placement="bottomLeft" maxWidth={400} width={400}>
                    <div slot="title">
                      <QEntityLink
                        coyObj={{
                          KeyNo: (this.controllerData as ControllerData).KeyNo,
                          Name: (this.controllerData as ControllerData).Name,
                        }}
                      />
                    </div>
                    <QEntityLink
                      coyObj={{
                        KeyNo: (this.controllerData as ControllerData).KeyNo,
                        Name: (this.controllerData as ControllerData).Name,
                      }}
                    />
                  </Tooltip>
                ) : (
                  <QEntityLink
                    coyObj={{ KeyNo: (this.controllerData as ControllerData).KeyNo, Name: (this.controllerData as ControllerData).Name }}
                  />
                )}
              </span>
              {(this.controllerData as ControllerData).showControlPercent && (
                <span class={styles.mLLg}>
                  表决权：
                  <span class="text-danger-no-bg">{(this.controllerData as ControllerData).ControlPercent || '?'}</span>
                </span>
              )}
              {(this.controllerData as ControllerData).Job && (
                <span class={styles.mLLg}>
                  担任职务：<span class="text-danger-no-bg">{(this.controllerData as ControllerData).Job}</span>
                </span>
              )}
            </span>
            {!(this.controllerData as ControllerData).Name && (
              <span>
                实际控制人
                <QGlossaryInfo infoId="624" placement="bottom" />
                （大数据分析）：?
              </span>
            )}
          </div>
        ) : null}
        {this.$slots.extra}
      </div>
    );
  },
});

export default AppKzrSubtextN;
