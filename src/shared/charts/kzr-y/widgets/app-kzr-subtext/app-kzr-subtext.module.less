.container {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translate(-50%, 0);
  font-size: 14px;
  color: #999;
  background: #f5faff;
  border-radius: 2px;
  z-index: 2;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  height: 42px;
  align-items: center;

  &.isFull {
    top: 0;
  }

  &.iframe {
    top: 0;
    right: 0;
    font-size: 13px;
    width: 100%;

    .kzr {
      text-align: center;
    }
  }

  .kzr {
    text-align: center;
    line-height: 1.8;
    max-width: 1220px;
    display: flex;
    flex-wrap: nowrap;

    > span {
      display: flex;
      align-items: center;
    }

    :global(.app-glossary-info) {
      top: 0px;
    }

    :global(.icon-zhushi) {
      font-size: 16px;
      position: relative;
      top: 4px;
      margin: 0 5px;
    }
  }

  :global(.app-benefit-update) {
    margin-left: 30px;
  }
}

.nameMain {
  align-items: center;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.name {
  flex: 1;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #333;
}

.mLLg {
  margin-left: 30px;
  flex-shrink: 0;
}

.textDanger {
  color: #ff4040;
}

:global(a.text-primary) {
  color: #128bed;
  text-decoration: none;
}

.ntagClick {
  cursor: pointer;
}

.mRSpace {
  margin-right: 4px;
}

.ntagTextPrimaryClickHover {
  :global(.tag-icon) {
    background: #cae6fc;
  }
}
