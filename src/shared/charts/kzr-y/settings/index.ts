export interface NodeStyle {
  backgroundFit: string;
  lineHeight: number;
  overlayColor: string;
  textBgc: string;
}

export interface LabelSize {
  nodeLabel: number;
  extraLabel: number;
  linkLabel: number;
  tagLabel: number;
}

export interface LinkStyle {
  width: number;
  overlayColor: string;
  curveStyle: string;
  arrowShape: string;
  color: string;
  textColor: string;
}

export interface Settings {
  title: string;
  defaultScale: number;
  scaleRate: number;
  scaleRange: [number, number];
  duration: number;
  nameRule: [number, number];
  lineHeight: number;
  xGap: number;
  yGap: number;
  bottom: number;
  enodeWidth: number;
  enodeHeight: number;
  pnodeWidth: number;
  pnodeHeight: number;
  listTagHeight: number;
  tagWidth: number;
  rootnodeHeight: number;
  pathLimit: number;
  publicHeight: number;
  fontSize: number;
  nodeStyle: NodeStyle;
  labelSize: LabelSize;
  linkStyle: LinkStyle;
}

const settings: Settings = {
  title: '实际控制人',
  defaultScale: 1,
  scaleRate: 0.2,
  scaleRange: [0.2, 4],
  duration: 300,
  nameRule: [12, 24],
  lineHeight: 1.9,
  // 节点间距
  xGap: 100,
  yGap: 80,
  // 图谱理底部距离
  bottom: 150,
  // 企业节点宽度
  enodeWidth: 182,
  // 企业节点高度
  enodeHeight: 54,
  // 人物节点宽度
  pnodeWidth: 52,
  // 人物节点高度
  pnodeHeight: 52,
  // 标记高度
  listTagHeight: 15,
  // 标签宽度
  tagWidth: 124,
  // 根节点高度
  rootnodeHeight: 36,
  // 路径限制条数
  pathLimit: 20,
  publicHeight: 30,
  fontSize: 14,
  nodeStyle: {
    backgroundFit: 'cover',
    lineHeight: 1.6,
    overlayColor: '#ffffff',
    textBgc: '#000000'
  },
  labelSize: {
    nodeLabel: 14,
    extraLabel: 12,
    linkLabel: 12,
    tagLabel: 10
  },
  linkStyle: {
    width: 0.6,
    overlayColor: '#ffffff',
    curveStyle: 'bezier',
    arrowShape: 'triangle',
    color: '#999999',
    textColor: '#128bed'
  }
};

export default settings;
