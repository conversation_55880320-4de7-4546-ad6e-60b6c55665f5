@import '@/styles/token.less';

.container {
  overflow: hidden;
  background: url("@/assets/images/shuiying6.png") repeat;
  background-size: 360px 280px;
  line-height: 1.2;
  position: relative;
  width: 100%;
  height: 100%;

  .content {
    display: flex;
    width: 100%;
    height: calc(100% - 42px);
  }

  .chart {
    flex: 1;
    height: 100%;
    cursor: auto !important;

    &.is-full {
      top: 42px;
    }

    :global(.hover-company) {
      fill: #e2f1fd;
      > rect {
        fill: #e2f1fd;
      }

      > path {
        fill: #e2f1fd;
      }
    }
  }

  .toolbox {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px 10px;
    background: rgba(255, 255, 255, 0.9);
    border-left: 1px solid #e8e8e8;
    min-width: 80px;

    .slider {
      height: 200px;
    }

    .btnGroup {
      list-style: none;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .btnItem {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      .icon {
        font-size: 16px;
        color: #666;
      }

      .label {
        font-size: 12px;
        color: #666;
      }
    }
  }
}

:global(.gray20) {
  opacity: 20%;
}

:global(path) {
  &.hover-self {
    stroke: #0069bf;
  }
}

:global(.hover-self) {
  fill: #0069bf;

  > rect {
    fill: #0069bf;
  }
}

:global(.hover-person) {
  > circle {
    fill: #d97716;
  }
}

:global(.hover-combined) {
  .combined {
    background-color: #e2f1fd !important;
  }
}

:global(.hovered) {
  fill: transparent;
}

:global(.yfiles-tooltip) {
  border: none;
  border-radius: 2px;
  font-size: 14px;
  background-color: #fff;
  padding: 8px 12px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
}

:global(.qcc-graph-tooltip) {
  border: none;
  border-radius: 2px;
  font-size: 14px;
  background-color: #fff;
  padding: 8px 12px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
}

:global(.temp-measure) {
  position: absolute;
  top: 5000px;
  left: 0;
}
