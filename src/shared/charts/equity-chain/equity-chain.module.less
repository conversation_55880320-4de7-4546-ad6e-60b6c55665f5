@import '@/styles/token.less';

.container {
  overflow: hidden;
  background: url("@/assets/images/shuiying6.png") repeat;
  background-size: 360px 280px;
  line-height: 1.2;
  position: relative;
  width: 100%;
  height: 100%;

  .content {
    display: flex;
    width: 100%;
    height: calc(100% - 42px);
  }

  .chart {
    flex: 1;
    height: 100%;
  }

  .tips {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 12px;
    line-height: 16px;
    padding-left: 10px;
    height: 42px;
    background: rgba(255, 255, 255, 0.8);
    color: #666;
  }
}

.toolbox {
  display: flex;
  flex-direction: column;
  align-items: center;

  .slider {
    height: 140px;

    :global {
      .ant-slider-track {
        width: 6px;
        background-color: #128deb;
        border-radius: 0 0 3px 3px;
      }

      .ant-slider-handle {
        width: 16px;
        height: 16px;
        border-color: #128deb;
      }

      .ant-slider-rail {
        width: 6px;
        background-color: #f7f7f7;
        border-radius: 3px 3px 0 0;
      }
    }
  }

  .btnGroup {
    border: 1px solid #eee;
    border-radius: 2px;
    overflow: hidden;
  }

  .btnItem {
    width: 48px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 7px 0;
    color: #666;
    background-color: #fff;

    &:hover {
      color: #fff;
      background-color: #128deb;
    }

    &:not(:first-child) {
      border-top: 1px solid #eee;
    }

    .icon {
      font-size: 1.25em;
    }

    .label {
      font-size: 12px;
    }
  }
}
