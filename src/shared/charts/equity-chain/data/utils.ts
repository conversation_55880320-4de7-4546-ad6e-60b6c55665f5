export interface Node {
  id: string;
  properties: {
    keyNo: string;
    name: string;
    [key: string]: any; // 其他可选属性
  };
}

export interface Relationship {
  startNode: string;
  endNode: string;
  [key: string]: any; // 其他可选属性
}

/**
 * 查找从起始节点到目标节点的所有路径，并返回涉及的节点列表
 * @param nodes 节点列表
 * @param relationships 关系列表
 * @param startKeyNo 起始节点 keyNo
 * @param endKeyNo 目标节点 keyNo
 */
export const findPathNodes = (nodes: Node[], relationships: Relationship[], startKeyNo: string, endKeyNo: string): Node[] => {
  // 构建邻接表
  const graph: Record<string, string[]> = {};

  relationships.forEach((rel) => {
    if (!graph[rel.startNode]) {
      graph[rel.startNode] = [];
    }
    graph[rel.startNode].push(rel.endNode);
  });

  const visited = new Set<string>();
  const path: string[] = [];
  const result: string[][] = [];

  const dfs = (current: string): void => {
    if (current === endKeyNo) {
      path.push(current);
      result.push([...path]);
      path.pop();
      return;
    }

    visited.add(current);

    if (graph[current]) {
      for (const neighbor of graph[current]) {
        if (!visited.has(neighbor)) {
          path.push(current);
          dfs(neighbor);
          path.pop();
        }
      }
    }

    visited.delete(current);
  };

  dfs(startKeyNo);

  // 提取路径上的所有节点
  const uniqueNodeIds = new Set(result.flat());
  return nodes.filter((node) => uniqueNodeIds.has(node.properties.keyNo));
};
