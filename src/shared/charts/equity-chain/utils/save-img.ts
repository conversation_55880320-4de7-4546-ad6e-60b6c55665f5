import { max } from 'lodash';
import { GraphComponent, Insets, Size, SvgExport } from 'yfiles';
import ImgWatermark from '@/assets/images/shuiying6.png';
import ImgLogo from '@/assets/images/tip-logo.png';
import fileSaver from 'file-saver';
import moment from 'moment';

export const useSaveImg = (graphInstance, companyName?: string) => {
  const renderSvgToPng = (svgElement: SVGElement, size: Size, margins: Insets): Promise<HTMLImageElement> => {
    const targetCanvas = document.createElement('canvas');
    const targetContext = targetCanvas.getContext('2d')!;

    const svgString = SvgExport.exportSvgString(svgElement);
    const svgUrl = SvgExport.encodeSvgDataUrl(svgString);

    return new Promise((resolve) => {
      const svgImage = new Image();
      svgImage.onload = (): void => {
        targetContext.clearRect(0, 0, targetCanvas.width, targetCanvas.height);
        targetCanvas.width = size.width + (margins.left + margins.right);
        targetCanvas.height = size.height + (margins.top + margins.bottom);

        targetContext.drawImage(svgImage, margins.left, margins.top);
        const pngImage = new Image();
        pngImage.src = targetCanvas.toDataURL('image/png');
        pngImage.onload = (): void => resolve(pngImage);
      };
      svgImage.src = svgUrl;
    });
  };

  const exportImage = async (graphComponent, scale = 1, margins = Insets.from(5), exportRect?) => {
    const exportComponent = new GraphComponent();
    exportComponent.graph = graphComponent.graph;
    exportComponent.updateContentRect();
    const targetRect = exportRect ?? exportComponent.contentRect;
    const exporter = new SvgExport({
      worldBounds: targetRect,
      scale,
      margins,
      encodeImagesBase64: false,
      rasterizeHtmlVisuals: true,
      inlineSvgImages: true,
      strictMode: false,
      cssStyleSheet: '',
    });
    const svgElement = await exporter.exportSvgAsync(exportComponent);
    return renderSvgToPng(svgElement as SVGElement, new Size(exporter.viewWidth, exporter.viewHeight), margins);
    //   Array.from(svgElement.querySelectorAll('image')).map((imageElement) => {
    //     const href = imageElement.getAttributeNS('http://www.w3.org/1999/xlink', 'href') || imageElement.getAttribute('href');
    //     if (!href) return Promise.resolve(null);
    //     return new Promise((resolve, reject) => {
    //       const img = new Image();
    //       img.crossOrigin = 'anonymous';
    //       img.src = href;
    //       img.onerror = function () {
    //         // TODO: 弄一个默认图片上去，假如加载失败的话
    //         imageElement.setAttribute('href', '');
    //         resolve(true);
    //       };
    //       img.onload = function () {
    //         const canvas = document.createElement('canvas');
    //         canvas.width = img.width;
    //         canvas.height = img.height;
    //         const ctx = canvas.getContext('2d');
    //         if (ctx) {
    //           ctx.drawImage(img, 0, 0, img.width, img.height);
    //           imageElement.setAttribute('href', canvas.toDataURL());
    //         }
    //         resolve(true);
    //         canvas?.remove?.();
    //       };
    //       img.src = href;
    //     });
    //   })
    // );
  };

  const loadingImage = (image) => {
    return new Promise((resolve, reject) => {
      image.onload = function () {
        resolve(image);
      };
    });
  };

  const getTextSize = (str: string, styles = { fontSize: '12px' }) => {
    const span = document.createElement('span');
    span.innerHTML = str;
    span.style.fontSize = styles?.fontSize;
    span.style.visibility = 'hidden';
    span.style.display = 'inline-block';
    document.body.appendChild(span);
    const size = { w: span.clientWidth, h: span.clientHeight };
    document.body.removeChild(span);
    return size;
  };

  const saveImgElementAsPng = async (
    imgElement,
    filename = `${Date.now()}.png`,
    { noWatermark = false, noDisclaimer = false, scale = 1 }: { noWatermark?: boolean; noDisclaimer?: boolean; scale?: number }
  ) => {
    const canvas = document.createElement('canvas');
    const minSize = { w: 940 * scale, h: 402 * scale };
    canvas.width = max([minSize.w, imgElement.width + 100]);
    canvas.height = max([minSize.h, imgElement.height + 200]);
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.fillStyle = '#fff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    if (!noWatermark) {
      const shuiying = new Image();
      shuiying.src = ImgWatermark;
      await loadingImage(shuiying);
      shuiying.width = 240;
      shuiying.height = 200;
      for (let i = 0; i < canvas.width + shuiying.width; i += shuiying.width) {
        for (let j = 0; j < canvas.height + shuiying.height; j += shuiying.height) {
          ctx.drawImage(shuiying, i, j, shuiying.width, shuiying.height);
        }
      }
    }

    if (!noDisclaimer) {
      const qccLogo = new Image();
      qccLogo.src = ImgLogo;
      qccLogo.crossOrigin = 'Anonymous';
      await loadingImage(qccLogo);
      qccLogo.width = 50 * scale;
      qccLogo.height = 16 * scale;
      const fontSize = 12 * scale;
      const prefix = '以上数据是';
      const suffix = '大数据分析引擎基于公开信息挖掘的成果，仅供参考。该成果不构成任何明示或暗示的观点或保证。';
      const prefixSize = getTextSize(prefix, { fontSize: `${fontSize}px` });
      const suffixSize = getTextSize(suffix, { fontSize: `${fontSize}px` });
      const gap = 5;
      const disclaimerWidth = prefixSize.w + suffixSize.w + qccLogo.width + gap * 2 * scale;
      const startX = canvas.width / 2 - disclaimerWidth / 2;
      ctx.font = `${fontSize}px 微软雅黑`;
      ctx.fillStyle = '#666';
      ctx.fillText(prefix, startX, canvas.height - 14 * scale);
      const logoStartX = startX + prefixSize.w + gap * scale;
      ctx.drawImage(qccLogo, logoStartX, canvas.height - qccLogo.height - 11 * scale, qccLogo.width, qccLogo.height);
      ctx.fillText(suffix, logoStartX + qccLogo.width + gap * 2 * scale, canvas.height - 14 * scale);
    }

    ctx.drawImage(imgElement, (canvas.width - imgElement.width) / 2, (canvas.height - imgElement.height) / 2);

    canvas.toBlob((blob) => {
      if (!blob) return;
      // const link = document.createElement('a');
      // link.href = URL.createObjectURL(blob);
      // link.download = filename;
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(link.href);
      fileSaver.saveAs(blob, filename);
    }, 'image/png');
  };

  const saveImage = async () => {
    console.log("🚀 ~ saveImage ~ graphInstance.value:", graphInstance.value)
    const dataPng = await exportImage(graphInstance.value, 2);
    saveImgElementAsPng(dataPng, `${companyName}-股权图谱-${moment().format('YYYY-MM-DD')}.png`, { scale: 2 });
  };

  return {
    saveImage,
    exportImage,
    saveImgElementAsPng,
  };
};
