import { PropType, defineComponent, ref, unref } from 'vue';
import { Popover, Spin } from 'ant-design-vue';
import { useStorage } from '@vueuse/core';
import { useRouter } from 'vue-router/composables';

import { diligence } from '@/shared/services';
import { RISK_LEVEL, getRiskLevelStyle } from '@/config/risk.config';
import { isValidCompanyType } from '@/utils/company/company-type';

import styles from './diligence-warning-pop.module.less';
import DiligenceWarningBlock from '../diligence-waring-block';

// 带popover的风险等级信息
const DiligenceWarningPop = defineComponent({
  name: 'DiligenceWarningPop',
  props: {
    score: {
      type: [String, Number],
      required: false,
    },
    rowData: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}) as Record<string, any>,
    },
    diligenceId: {
      type: [String, Number],
      required: false,
    },
    // 是否是监控
    isMonitor: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const router = useRouter();
    // 排查id
    const diligenceData = ref(props.rowData);
    const diligenceRecordData = useStorage('diligenceRecordData', {});
    const loading = ref(false);

    /**
     * 获取风险维度信息
     */
    const getDiligenceData = async () => {
      // 如果没有diligenceId，直接return
      if (!props.diligenceId) {
        return;
      }
      // 有id先取缓存，取不到再调用接口
      if (diligenceRecordData.value[props.diligenceId]) {
        diligenceData.value.details = diligenceRecordData.value[props.diligenceId];
        return;
      }
      // 如果没有details但是有diligenceId，调用接口查询
      try {
        loading.value = true;
        const res = await diligence.scanRiskDetail({
          companyName: props.rowData.name,
          companyId: props.rowData.companyId,
          diligenceId: props.diligenceId,
        });
        diligenceData.value.details = res.details;
        // 根据diligenceId进行缓存
        diligenceRecordData.value = { ...unref(diligenceRecordData), [props.diligenceId]: res.details };
      } catch (error) {
        //
      } finally {
        loading.value = false;
      }
    };
    /**
     * 跳转到风险详情对应的维度
     * @param item
     * @param dimensionKey
     */
    const handleClickDimension = (item: Record<string, any>, dimensionKey: string) => {
      const { href } = router.resolve({
        path: `/investigation/history/detail/${item.companyId}`,
        query: {
          from: 'record',
          diligenceId: props.diligenceId || item.id,
        },
        hash: dimensionKey,
      });
      window.open(href);
    };

    return {
      loading,
      diligenceData,
      getDiligenceData,
      handleClickDimension,
    };
  },
  render() {
    const { score, diligenceData, loading, $slots } = this;
    // 渲染 中高低风险标签
    const renderRiskLabel = (needPointer = false) => {
      const resultSetting = diligenceData.orgModel?.resultSetting || RISK_LEVEL;
      const levelTextObj = resultSetting.find((v) => (v.level ?? v.value) === score);
      const levelText = levelTextObj?.name || levelTextObj?.label;
      if (!levelText) {
        return <div>-</div>;
      }
      return <span style={{ ...getRiskLevelStyle(score, this.isMonitor), cursor: needPointer ? 'pointer' : 'auto' }}>{levelText}</span>;
    };
    const enableInvestigate = (diligenceData.t_type && isValidCompanyType(diligenceData.t_type)) || !diligenceData.t_type;
    if (enableInvestigate && this.diligenceId) {
      return (
        <Popover placement="rightBottom" overlayClassName={styles.riskLevel} onMouseenter={this.getDiligenceData}>
          <div slot="content">
            {loading ? (
              <Spin spinning />
            ) : (
              <DiligenceWarningBlock
                chart={false}
                creditRateDetail={false}
                riskLevel={score}
                riskInfo={diligenceData}
                scrollToView={(dimensionKey) => this.handleClickDimension(diligenceData, dimensionKey)}
              />
            )}
          </div>
          {$slots.label ? $slots.label : renderRiskLabel(true)}
        </Popover>
      );
    }
    return renderRiskLabel(false);
  },
});

export default DiligenceWarningPop;
