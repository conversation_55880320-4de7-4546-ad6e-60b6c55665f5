import * as echarts from 'echarts';
import { defineComponent, ref, onMounted, onUnmounted, watch } from 'vue';
import { isEqual } from 'lodash';
import { useWindowSize, watchDebounced } from '@vueuse/core';

export const QChart = defineComponent({
  name: 'QChart',
  props: {
    option: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String,
    },
    height: {
      type: String,
      default: '300px',
    },
    width: {
      type: String,
      default: 'auto',
    },
  },
  setup(props, { emit }) {
    const chartRef = ref();
    const chart = ref();
    const windowSize = useWindowSize();

    const initChart = () => {
      chart.value = echarts.init(chartRef.value);
      chart.value.resize();
      ['click', 'mouseover', 'mouseout', 'downplay', 'highlight', 'selectchanged', 'legendselectchanged'].forEach((event) => {
        chart.value.on(event, (...args) => {
          emit(event, chart.value, ...args);
        });
      });
    };

    const draw = () => {
      const option = props.option;
      if (!option) {
        return;
      }
      chart.value.setOption(option);
      chart.value.resize({ height: props.height });
    };

    onMounted(() => {
      initChart();
      draw();
    });

    onUnmounted(() => {
      chart.value.clear();
      chart.value.dispose();
    });

    watch(
      () => props.option,
      (newVal, oldVal) => {
        if (chart.value && !isEqual(oldVal, newVal)) {
          draw();
        }
      },
      {
        deep: true,
        immediate: false,
      }
    );

    watchDebounced(windowSize.width, draw, { debounce: 200 });

    return { chart, chartRef, initChart, draw };
  },

  render() {
    return (
      <div class="app-echarts" style="position: relative" ref="containerRef">
        <div ref="chartRef" class="main" style={{ height: this.height, width: this.width }}></div>
      </div>
    );
  },
});

export default QChart;
