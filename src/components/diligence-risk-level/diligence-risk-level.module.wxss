.container {
  position: relative;
  width: 198px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.container .text {
  position: absolute;
  top: calc(50% - 16px);
  transform: translate(-50%, -50%);
  width: 130px;
  height: 130px;
  left: 50%;
  right: 0;
  text-align: center;
  font-size: 26px;
  font-weight: 500;
  line-height: 32px;
  border-radius: 50%;
  background: linear-gradient(180deg, rgba(179, 0, 0, 0.5) 0%, rgba(179, 0, 0, 0) 100%);
}
.container .result {
  padding: 5px 10px;
  width: 182px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  box-sizing: border-box;
  color: #666666;
  background: #fff8f9;
  border: 1px solid #ffcccc;
  font-size: 14px;
  line-height: 22px;
}
