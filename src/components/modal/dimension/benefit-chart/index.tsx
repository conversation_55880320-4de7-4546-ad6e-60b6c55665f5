import { defineComponent, onMounted, PropType, ref } from 'vue';
import EquityChain from '@/shared/charts/equity-chain';
import QPlainTable from '@/components/global/q-plain-table';

const BenefitChart = defineComponent({
  name: 'BenefitChart',
  props: {
    viewData: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  },
  render() {
    const { viewData } = this;
    const { ActualControllerName, ActualControllerKeyNo, Paths } = viewData;
    return (
      <QPlainTable>
        <tr>
          <td class="tb" width="180">
            受益所有人
          </td>
          <td colspan="5">
            <q-entity-link
              coy-obj={{
                Name: viewData.name,
                KeyNo: viewData.keyNo,
              }}
            ></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb" width="180">
            持股比例
          </td>
          <td colspan="5">{viewData.totalStockPercent || '-'}</td>
        </tr>
        <tr>
          <td>股权关系图</td>
          <td colspan="5">
            <div style={{ height: '450px' }}>
              <EquityChain containerId="equity-1" companyInfo={viewData.companyInfo} />
            </div>
          </td>
        </tr>
      </QPlainTable>
    );
  },
});

export default BenefitChart;
