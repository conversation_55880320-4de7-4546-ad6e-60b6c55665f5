import { defineComponent, ref, PropType } from 'vue';
import { company } from '@/shared/services';

export default defineComponent({
  name: 'LEIDetail',
  props: {
    viewData: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  },
  setup(props) {
    const { viewData } = props;
    const leiDetail = ref<Record<string, any>>({});
    const loading = ref<boolean>(true);

    company.getLEIDetail(viewData.keyNo).then((res) => {
      leiDetail.value = res.Result;
      loading.value = false;
    });

    return {
      leiDetail,
      loading,
    };
  },

  render() {
    const { leiDetail, loading } = this;
    return loading ? null : (
      <table class="ntable">
        <tr>
          <td class="tb" width="210">
            全球法人识别编码（LEI）
          </td>
          <td colspan="3">{leiDetail.LEICode || '-'}</td>
        </tr>
        <tr>
          <td class="tb" width="210">
            LEI码状态
          </td>
          <td colspan="1" width="274">
            {leiDetail.RegStatus || '-'}
          </td>
          <td class="tb" width="210">
            注册日期
          </td>
          <td colspan="1" width="274">
            {leiDetail.RegDate || '-'}
          </td>
        </tr>
        <tr>
          <td class="tb" width="210">
            最近更新日期
          </td>
          <td colspan="1" width="274">
            {leiDetail.LastUpdateDate || '-'}
          </td>
          <td class="tb" width="210">
            下次续订日期
          </td>
          <td colspan="1" width="274">
            {leiDetail.NextRenewalDate || '-'}
          </td>
        </tr>
        <tr>
          <td class="tb" width="210">
            发行机构
          </td>
          <td colspan="3">
            <q-entity-link coyObj={{ KeyNo: leiDetail.IssuerKeyno, Name: leiDetail.IssuerName }}></q-entity-link>
          </td>
        </tr>
      </table>
    );
  },
});
