import { monitor } from '@/shared/services';
import { useRouter } from 'vue-router/composables';
import { message } from 'ant-design-vue';
import { openAddCompanyModal } from '@/components/modal/supplier/add-company-modal/hooks';
export const useAddCompanyModal = (props: { permission; monitorGroupId; selectCompanyList; disabled }, emit) => {
  const router = useRouter();
  /** 添加公司 */
  const handleAddCompanies = async (newCompanies: { companyId: string; companyName: string; monitorGroupId: number }[], monitorGroupId) => {
    const { failList } = await monitor.addCompanyToGroup({
      monitorGroupId,
      items: newCompanies,
    });
    if (failList?.length) {
      const companyNames = newCompanies.map((item) => item.companyName).join('、');
      return message.error(`添加失败，${companyNames}已经存在列表中`);
    }
    message.success('添加成功');
    return emit('refresh');
  };
  /**
   * 添加企业
   */
  const handleAddCompany = async (disabled = props.disabled, selectCompanyList = props.selectCompanyList) => {
    if (disabled) {
      return;
    }
    // 导航有id先选Id，没有再取默认分组
    const defaultGroupId = props.monitorGroupId;
    const tabs = selectCompanyList?.length
      ? [{ label: selectCompanyList?.length > 1 ? '添加多个企业' : '添加单个企业', key: 'single' }]
      : undefined;
    const params = {
      originData: {
        monitorGroupId: defaultGroupId,
      },
      companyList: props.selectCompanyList,
      tabs,
    };
    const res = await openAddCompanyModal(params);
    const { groupsChange, newCompanies, batchId, groupId, monitorGroupId } = res as any;

    if (batchId) {
      router.push({
        name: 'risk-monitor-upload-confirm',
        query: { batchId, groupId },
      });
      return;
    }

    if (newCompanies?.length) {
      return handleAddCompanies(newCompanies, monitorGroupId);
    }
    if (groupsChange) {
      emit('refresh');
    }
    return null;
  };
  return {
    handleAddCompany,
  };
};
